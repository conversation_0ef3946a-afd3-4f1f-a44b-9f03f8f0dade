import { GetSecondaryConnectionsResponse } from '@api/utrv';
import { NumberScale } from '@g17eco/types/units';
import { DataPeriods } from '@g17eco/types/universalTracker';
import { CalculationGroupValueType, CalculationType } from '@g17eco/types/utrv-connections';

export const mockResponseSecondaryConnections: GetSecondaryConnectionsResponse = {
  connections: [
    {
      _id: '67b463e37f6d925c1396bb17',
      utrCode: 'gri/2020/305-2/d',
      valueListCode: 'scope2_base_year_emissions',
      calculationGroups: [
        {
          _id: '67b463e37f6d925c1396baf1',
          calculations: [
            {
              name: 'direct 1',
              description: 'direct 1',
              variables: {
                a: {
                  code: 'survey/generic/scope-1',
                },
              },
              type: CalculationType.Direct,
              direct: 'a',
              _id: '67b463e3ed12a34534741bfd',
              data: [],
              numberScale: 'single',
            },
            {
              name: 'direct 2',
              description: 'direct 2',
              variables: {
                a: {
                  code: 'survey/generic/scope-2',
                },
              },
              type: CalculationType.Direct,
              direct: 'a',
              _id: '67b463e3ed12a34534741bfe',
              data: [
                {
                  value: 7132,
                  variables: {
                    a: {
                      value: 7132,
                      utrvId: '67ad6301f6b0edbe97f063c8',
                      unit: 'mt',
                      numberScale: 'single',
                    },
                  },
                  surveyId: '67ad6300f6b0edbe97f05fe1',
                },
              ],
              unit: 'mt',
              numberScale: 'single',
            },
            {
              name: 'formula 1',
              description: 'formula 1',
              variables: {
                a: {
                  code: 'survey/generic/scope-1',
                },
                b: {
                  code: 'survey/generic/scope-2',
                },
              },
              type: CalculationType.Formula,
              formula: '{a} + {b}',
              _id: '67b463e3ed12a34534741bff',
              data: [
                {
                  value: 7132,
                  variables: {
                    b: {
                      value: 7132,
                      utrvId: '67ad6301f6b0edbe97f063c8',
                      unit: 'mt',
                      numberScale: 'single',
                    },
                  },
                  surveyId: '67ad6300f6b0edbe97f05fe1',
                },
              ],
              numberScale: 'single',
            },
            {
              name: 'formula 2',
              description: 'formula 2',
              variables: {
                a: {
                  code: 'survey/generic/scope-1',
                },
                b: {
                  code: 'survey/generic/scope-2',
                },
              },
              type: CalculationType.Formula,
              formula: '{a} - {b}',
              _id: '67b463e3ed12a34534741c00',
              data: [
                {
                  value: -7132,
                  variables: {
                    b: {
                      value: 7132,
                      utrvId: '67ad6301f6b0edbe97f063c8',
                      unit: 'mt',
                      numberScale: 'single',
                    },
                  },
                  surveyId: '67ad6300f6b0edbe97f05fe1',
                },
              ],
              numberScale: 'single',
            },
          ],
          name: 'Group mapping 1',
          valueType: CalculationGroupValueType.Numeric,
        },
      ],
    },
    {
      _id: '67b463e37f6d925c1396bb1a',
      utrCode: 'gri/2020/305-2/d',
      valueListCode: 'scope2_base_year_rationale',
      calculationGroups: [
        {
          _id: '67b463e37f6d925c1396baf9',
          calculations: [
            {
              name: 'asd',
              description: 'asd',
              variables: {
                a: {
                  code: 'gri/2020/202-1/d',
                },
              },
              type: CalculationType.Direct,
              direct: 'a',
              _id: '67b463e3ed12a34534741c0d',
              numberScale: NumberScale.Single,
              data: [
                {
                  value:
                    'This disclosure applies to those organizations in which a substantial portion of their employees, and workers (excluding employees) performing the organization’s activities, are compensated in a manner or scale that is closely linked to laws or regulations on minimum wage. Providing wages above the minimum wage can help contribute to the economic well-being of workers performing the organization’s activities.',
                  variables: {
                    a: {
                      value:
                        'This disclosure applies to those organizations in which a substantial portion of their employees, and workers (excluding employees) performing the organization’s activities, are compensated in a manner or scale that is closely linked to laws or regulations on minimum wage. Providing wages above the minimum wage can help contribute to the economic well-being of workers performing the organization’s activities.',
                      utrvId: '67ad6340f6b0edbe97f06ac4',
                    },
                  },
                  surveyId: '67ad6300f6b0edbe97f05fe1',
                },
              ],
            },
          ],
          name: 'Text mapping ',
          valueType: CalculationGroupValueType.Text,
        },
      ],
    },
  ],
  utrs: [
    {
      _id: '5e78e23e74d1f36e84ed9e92',
      code: 'gri/2020/202-1/d',
      name: '(202-1) The Definition Used for ‘significant Locations of Operation’',
      type: 'gri',
    },
    {
      _id: '5e78e23f74d1f36e84ed9e9e',
      code: 'gri/2020/204-1/c',
      name: '(204-1) The Definition Used for ‘significant Locations of Operation’',
      type: 'gri',
    },
    {
      _id: '603e34a7d73fbe5dd6ec0601',
      code: 'survey/generic/scope-2',
      name: 'Scope 2',
      type: 'gri',
    },
  ],
  integrationUtrs: [],
  surveys: [
    {
      _id: '67ad6300f6b0edbe97f05fe1',
      period: DataPeriods.Yearly,
      effectiveDate: '2025-02-28T23:59:59.999Z',
      initiativeId: '655b1772972cb5359520abec',
    },
  ],
};
