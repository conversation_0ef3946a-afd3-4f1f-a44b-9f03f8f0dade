/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { createEditor, EditorState } from 'lexical';
import { defaultConfig } from '@features/rich-text-editor/constants';

/**
 * Creates a simple EditorState with a single paragraph containing the provided text
 * Useful for testing components that require EditorState props
 */
export const createEditorState = (text: string): EditorState => {
  const editor = createEditor(defaultConfig);
  const editorState = editor.parseEditorState(
    JSON.stringify({
      root: {
        children: [
          {
            children: [
              {
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                text,
                type: 'text',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'paragraph',
            version: 1,
          },
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'root',
        version: 1,
      },
    })
  );
  return editorState;
};
