/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import './styles.scss';
import React, { useState } from 'react';
import Dashboard, { DashboardSection, DashboardSectionTitle } from '../../../../components/dashboard';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'reactstrap';
import { CollapseButton, CollapseContent, CollapsePanel } from '@g17eco/molecules/collapse-panel';
import { BasicAlert } from '@g17eco/molecules/alert';
import { useGetCarbonCalculatorsListQuery } from '../../api';
import { QueryWrapper } from '@components/query/QueryWrapper';
import SustainabilityTeamImage from '../../../../images/sustainability_team.png';
import RequestDemoModal from '../../../../components/request-demo-modal';
import { useToggle } from '@hooks/useToggle';
import { CLOSED_PHOTO_INDEX, G17Lightbox } from '@components/lightbox';
import { getAnalytics } from '@services/analytics/AnalyticsService';
import { AnalyticsEvents } from '@services/analytics/AnalyticsEvents';
import { useHistory } from 'react-router-dom';
import { CalculatorInfo } from '../../types';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import queryString from 'query-string';
import { getCurrentYear } from '../../../../utils/date';

interface LightboxState {
  slides: { src: string }[];
  index: number;
}

const faq: { question: string, answer: string }[] = [
  {
    question: 'What do I need to look for?',
    answer: 'Firstly, when looking for a carbon calculator, make sure that emissions have relevant auditable emission factors for your company’s locations/ facilities and industries down to the product level.'
  },
  {
    question: 'What methodology should it follow?',
    answer: 'A calculator should be based on robust science and data for your relevant industry. Ensure that the platform\'s methodology is aligned to GHG protocol and supports your ability to make Science Based Targets.'
  },
  {
    question: 'What about functionality?',
    answer: 'Ideally, you want a platform that Instantly generates dashboards and reports for relevant metrics and Scopes. It should also be supported by climate experts to help check and verify the output. An easy onboarding process is a bonus.'
  },
  {
    question: 'Spend or Activity based calculations?',
    answer: 'Both! It’s best if the platform can make use of both methods to calculate your company’s footprint.'
  },
  {
    question: 'Any practicalities to consider?',
    answer: 'When looking for a carbon calculator, you should make sure that the platform allows delegation to multiple users - those inside your company plus external users such as third-party contractors. Also, it the offer aligned to your company\'s budget and reporting schedule?'
  }
];

const NoData = () => {
  return (
    <div className='mr-3'>
      <BasicAlert type='warning'>
        No Carbon Calculators currently available. Please check back again later.
      </BasicAlert>
    </div>
  );
};

interface HandleGoToLinkParams {
  link: string;
  code: string;
}

const handleGoToLink = ({ link, code }: HandleGoToLinkParams) => {
  getAnalytics().track(AnalyticsEvents.EmissionCalculatorExternalView, { code })
  window.open(link);
}

const DEFAULT_LIGHTBOX_STATE: LightboxState = { slides: [], index: CLOSED_PHOTO_INDEX };

export const CarbonCalculatorComparisonRoute = () => {

  const history = useHistory();
  const [lightbox, setLightbox] = useState<LightboxState>(DEFAULT_LIGHTBOX_STATE);
  const calculatorsList = useGetCarbonCalculatorsListQuery();
  const [requestModalOpen, toggleRequestDemoModal] = useToggle(false);
  const query = queryString.parse(history.location.search);
  const currentYear = getCurrentYear();

  const handleReset = () => {
    setLightbox(DEFAULT_LIGHTBOX_STATE);
  }

  const handleView = (code: string) => {
    getAnalytics().track(AnalyticsEvents.EmissionCalculatorView, { code })
    history.push(generateUrl(ROUTES.EMISSIONS_PARTNER, { partnerCode: code }));
  }

  const handleSetLightBox = ({ images, index }: { images: string[], index: number }) => {
    const slides = images.map((image) => ({
      src: image
    }));
    setLightbox({ slides, index });
  }

  const getButtons = ({ code, integration }: CalculatorInfo): React.JSX.Element[] => {
    // Require query param to enable for devs.
    if (integration && query.integrations) {
      return [
        <Button key={code} color='primary' className={'ml-2'} outline onClick={() => handleView(code)}>
          View<i className='ms-2 fal fa-eye' />
        </Button>
      ]
    }
    return [];
  }

  return (
    <div className='d-flex flex-column'>
      <Dashboard className='mb-0'>
        <DashboardSectionTitle
          title={`Best Emissions Calculators ${currentYear}: the successor to Carbon Calculators`}
          subtitle='Discover the best carbon emissions calculator for your needs'
        />
      </Dashboard>
      <Dashboard className='calculator-comparison mt-0' hasSidebar={true} sidebarPosition='right'>
        <DashboardSection
          padding={0}
          className='calculator-sidebar'
        >
          <div className='px-3'>
            <div className='text-center text-xl text-ThemeTextDark'>How to select a carbon calculator</div>
            <div className='text-center'>Everything you need to know</div>
          </div>
          <div className='mt-3'>
            {faq.map((qa, i) => (
              <CollapsePanel key={`cp-${qa.question}-${i}`} className='mt-2' collapsed={i !== 0}>
                <CollapseButton>
                  <span className='text-ThemeTextMedium strong'>{qa.question}</span>
                </CollapseButton>
                <CollapseContent className='mt-2 ps-5'>
                  <>{qa.answer}</>
                </CollapseContent>
              </CollapsePanel>
            ))}
          </div>
          <div className='mt-3 p-4 text-center background-ThemeBgDarkest text-ThemeTextWhite'>
            <div>
              <img src={SustainabilityTeamImage} alt='Sustainability team' />
            </div>
            <div className='text-lg'>Still have questions?</div>
            <div className='mt-2'>Can't find the answer you're look for? Please chat to our friendly team.</div>
            <div className='mt-3'>
              <Button color='secondary' size='lg' onClick={toggleRequestDemoModal}>Get in touch</Button>
            </div>
          </div>
        </DashboardSection>
        <QueryWrapper
          query={calculatorsList}
          onError={() => <NoData />}
          onNoData={() => <NoData />}
          onSuccess={(calculators) => (
            <>
              {calculators.map((calculator, i) => (
                <DashboardSection key={`calculator-${calculator.name}-${i}`} padding={2}>
                  <div className='d-flex flex-column'>
                    <div className='d-flex'>
                      <div>
                        <img className='calculators-logo' src={calculator.logo} width={60} height={60} alt={calculator.name} />
                      </div>
                      <div className='d-flex flex-column flex-fill'>
                        <div>
                          <h3 className='text-ThemeTextDark'>{calculator.name}</h3>
                        </div>
                        <div>
                          <CalculatorBadges badges={calculator.tags} />
                        </div>
                      </div>
                      <div>
                        {calculator.link ?
                          <Button color='primary' outline onClick={() => handleGoToLink({
                            link: calculator.link as string,
                            code: calculator.code
                          })}>
                            Website<i className='ms-2 fal fa-arrow-up-right' />
                          </Button> : null}
                        {getButtons(calculator)}
                      </div>
                    </div>
                  </div>
                  <hr />
                  <>{calculator.description}</>
                  {calculator.images ?
                    <div className='mt-3'>
                      {calculator.images.map((image, i) => (
                        <Button
                          key={`img-${calculator.name}-${image}`}
                          color='link-secondary'
                          onClick={() => handleSetLightBox({ images: calculator.images ?? [], index: i })}
                        >
                          <img alt={`${calculator.name} ${i + 1}`} className='' src={image} height={60} width={60} />
                        </Button>
                      ))}
                    </div>
                    : null}
                  <hr />

                    <table className='highlights-and-restrictions' width='100%'>
                      <tbody>
                      <tr>
                        <td width='49%'>
                          {calculator.highlights.length > 0 ? (
                            <>
                              <div className='mb-1'>Great for:</div>
                              <ul className='list-highlights'>
                                {calculator.highlights.map(bullet => <li key={`li1-${bullet}`}>{bullet}</li>)}
                              </ul>
                            </>
                          ) : null}
                        </td>
                        <td width='2%' />
                        <td width='49%'>
                          {calculator.restrictions.length > 0 ? (
                            <>
                              <div className='mb-1'>Worth checking:</div>
                              <ul className='list-restrictions'>
                                {calculator.restrictions.map(bullet => <li key={`li2-${bullet}`}>{bullet}</li>)}
                              </ul>
                            </>
                          ) : null}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </DashboardSection>
              ))
              }
            </>
          )}
        />
        <G17Lightbox photoIndex={lightbox.index} slides={lightbox.slides} handleReset={handleReset} />
      </Dashboard>
      <RequestDemoModal isOpen={requestModalOpen} toggle={toggleRequestDemoModal} />
    </div>
  );
}

const CalculatorBadges = ({ badges }: { badges: string[] }) => (
  <>
    {badges.map(badge => <Badge key={`badge-${badge}`} pill className='me-2 bg-transparent text-ThemeSuccessMedium fw-normal border border-ThemeSuccessMedium' color='success'>{badge}</Badge>)}
  </>
);


