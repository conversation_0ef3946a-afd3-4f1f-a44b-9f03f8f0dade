/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React, { useCallback, useMemo } from 'react';
import { Button, Input, InputGroup, InputGroupText } from 'reactstrap';
import { CollapseButton, CollapseContent, CollapsePanel } from '@g17eco/molecules/collapse-panel';
import Dashboard, { DashboardSection } from '@components/dashboard';
import { Table, ColumnDef } from '@g17eco/molecules/table';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import {
  EnergyValueInterface,
  TableDataInterface,
  scope1Data,
  scope2Data,
  scope3Data,
  dropdowns,
  CONSTANTS,
  calculate,
} from './constants';
import './style.scss';

export const SimpleCarbonCalculator = () => {
  return (
    <Dashboard>
      <DashboardSection
        title='Carbon Calculator'
        icon='fa-calculator'
        subtitle='This is a free simple calculator designed to enable companies to estimate carbon footprints.
        It is based upon the 2020 recommended conversion factors provided by Department for Environment, Food and Rural Affairs (Defra)
        and Department for Business, Energy and Industrial Strategy (BEIS).'
      >
        <Calculator />
      </DashboardSection>
    </Dashboard>
  );
};

const getEnergyValueColumns = (
  setResult: (id: string, value: number) => void,
  header: string = 'Energy Value'
): ColumnDef<TableDataInterface>[] => [
  {
    header,
    id: 'input',
    meta: {
      cellProps: {
        className: 'inputCol',
      },
    },
    cell: ({ row }) => {
      const data = row.original;
      return (
        <EnergyInputGroup
          {...data}
          value={data.input}
          onChange={(e) => setResult(data.id, parseFloat(e.target.value))}
        />
      );
    },
  },
  {
    header: 'Conversion Factor',
    id: 'conversion',
    meta: {
      cellProps: {
        className: 'conversionFactor',
      },
    },
    cell: ({ row }) => {
      const data = row.original;
      const dropdown = data.conversionLookup ? dropdowns.find((d) => d.id === data.conversionLookup) : undefined;
      return (
        <>
          {data.conversion ? `x ${data.conversion > 0.00001 ? data.conversion : '<0.00001'}` : '(Select above)'}
          {dropdown ? (
            <SimpleTooltip
              text={
                <>
                  Factor from dropdown above called: <br />
                  <strong>{dropdown.label}</strong>
                </>
              }
            >
              <i className='fa fa-question-circle ml-2 text-ThemeAccentExtradark' />
            </SimpleTooltip>
          ) : (
            null
          )}
        </>
      );
    },
  },
  {
    header: 'Result',
    id: 'result',
    meta: {
      cellProps: {
        className: 'smallAppend',
      },
    },
    cell: ({ row }) => {
      const data = row.original;
      return (
        <EnergyInputGroup
          {...data}
          value={data.result ? Math.round(data.result) : data.result}
          append={
            <>
              kgCO<sup>2</sup>e
            </>
          }
          disabled={true}
          tooltip={undefined}
          onChange={() => {}}
        />
      );
    },
  },
];

const getOptionalValueColumns = (
  setResult: (id: string, value: number) => void
): ColumnDef<{ col1: TableDataInterface; col2: TableDataInterface }>[] => [
  {
    header: '',
    id: 'option1',
    meta: {
      cellProps: {
        className: 'w-50 optionalCol smallAppend',
      },
    },
    cell: ({ row }) => {
      const { col1 } = row.original;
      return <EnergyInputGroup {...col1} onChange={(e) => setResult(col1.id, parseFloat(e.target.value))} />;
    },
  },
  {
    header: '',
    id: 'option2',
    meta: {
      cellProps: {
        className: 'w-50 optionalCol smallAppend',
      },
    },
    cell: ({ row }) => {
      const { col2 } = row.original;
      return <EnergyInputGroup {...col2} onChange={(e) => setResult(col2.id, parseFloat(e.target.value))} />;
    },
  },
];

const getResultValueColumns = (): ColumnDef<{ col1: TableDataInterface; col2: TableDataInterface }>[] => [
  {
    header: '',
    id: 'result1',
    meta: {
      cellProps: {
        className: 'w-50 optionalCol smallAppend',
      },
    },
    cell: ({ row }) => {
      const { col1 } = row.original;
      return <EnergyInputGroup {...col1} disabled={true} onChange={() => {}} />;
    },
  },
  {
    header: '',
    id: 'result2',
    meta: {
      cellProps: {
        className: 'w-50 optionalCol smallAppend',
      },
    },
    cell: ({ row }) => {
      const { col2 } = row.original;
      return <EnergyInputGroup {...col2} disabled={true} onChange={() => {}} />;
    },
  },
];

interface EnergyInputGroupProps extends TableDataInterface {
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  value?: number;
}

const EnergyInputGroup = (props: EnergyInputGroupProps) => {
  return (
    <InputGroup>
      {props.prepend ? <InputGroupText>{props.prepend}</InputGroupText> : null}
      <Input type='number' onChange={props.onChange} disabled={props.disabled ?? false} value={props.value ?? ''} />
      {props.append ? (
        <InputGroupText>
          {props.append}

          {props.tooltip ? (
            <SimpleTooltip text={props.tooltip}>
              <i className='fa fa-question-circle ml-2 text-ThemeAccentExtradark' />
            </SimpleTooltip>
          ) : null}
        </InputGroupText>
      ) : (
        null
      )}
    </InputGroup>
  );
};

const convertEnergyDataToTableData = (
  d: EnergyValueInterface,
  results: { [key: string]: number | undefined }
): TableDataInterface => {
  return {
    ...d,
    conversion: d.conversionLookup ? results[d.conversionLookup] : d.conversion,
    result: results[d.id],
    input: results[`${d.id}Input`],
  };
};

interface EnergyDropdownProps {
  label: string;
  id: string;
  options: {
    label: string;
    value: number;
  }[];
  setResult: (id: string, value: number) => void;
}

const EnergyDropdown = (props: EnergyDropdownProps) => {
  return (
    <div className='mt-2'>
      <InputGroup>
        <InputGroupText>{props.label}</InputGroupText>
        <Input type='select' name={props.id} onChange={(e) => props.setResult(props.id, parseFloat(e.target.value))}>
          {props.options.map((o, i) => (
            <option key={`energy-dropdown-input-${props.id}-${i}`} value={o.value}>
              {o.label}
            </option>
          ))}
        </Input>
      </InputGroup>
    </div>
  );
};

const inputFields = [...scope1Data, ...scope2Data, ...scope3Data];

const initialState: { [key: string]: number } = {};
dropdowns.forEach((d) => (initialState[d.id] = d.options[0].value));
inputFields.forEach((d) => {
  if (d.divisor) {
    initialState[`${d.id}Divisor`] = d.divisor;
  }
});

const setValue = (value: number) => (isNaN(value) ? undefined : value);

const Calculator = () => {
  const [results, setResults] = React.useState<{ [key: string]: number | undefined }>(initialState);
  const [assessment, setAssessment] = React.useState<{ className: string; text: string }>();

  const setResult = useCallback(
    (id: string, value: number) => {
      let r = { ...results };

      if (dropdowns.some((d) => d.id === id)) {
        // One of the dropdowns changed, so let's recalculate affected
        r[id] = setValue(value);
        inputFields.forEach((f) => {
          if (f.conversionLookup === id && r[`${f.id}Input`] !== undefined) {
            r[f.id] = setValue((r[`${f.id}Input`] ?? 0) * (r[id] ?? 0));
          }
        });
      } else {
        const inputField = inputFields.find((f) => f.id === id);
        if (inputField) {
          // This is an input field that probably needs conversion
          const conversion = inputField.conversionLookup ? r[inputField.conversionLookup] : inputField.conversion;
          r[id] = setValue(value * (conversion ?? 0));
          r[`${id}Input`] = setValue(value);
        } else {
          r[id] = setValue(value);
        }
      }

      r = calculate(r);

      if (r.buildingarea && r.buildingenergy) {
        r.buildingsec = Math.round(r.buildingenergy / r.buildingarea);

        if (r.buildingsec < CONSTANTS.ASSESSMENT_THRESHOLD_MIN) {
          setAssessment({
            className: 'alert alert-success',
            text: 'GOOD: Building use is under 100kWh/m2',
          });
        } else {
          if (r.buildingsec < CONSTANTS.ASSESSMENT_THRESHOLD_TARGET) {
            setAssessment({
              className: 'alert alert-warning',
              text: 'ROOM FOR IMPROVEMENT: Building use is between 100 and 250 kWh/m2',
            });
          } else {
            setAssessment({
              className: 'alert alert-danger',
              text: 'PROBABLY NEEDS IMPROVEMENT: Building use is over 250kWh/m2',
            });
          }
        }
      }
      setResults(r);
    },
    [results]
  );

  const data = useMemo(() => {
    return {
      scope1: scope1Data.map((d) => convertEnergyDataToTableData(d, results)),
      scope2: scope2Data.map((d) => convertEnergyDataToTableData(d, results)),
      scope3: scope3Data.map((d) => convertEnergyDataToTableData(d, results)),
    };
  }, [results]);

  const directEmissionsBuildingColumns = useMemo(
    () => getEnergyValueColumns(setResult, 'Building or process Energy'),
    [setResult]
  );
  const directEmissionsPurchaseColumns = useMemo(
    () => getEnergyValueColumns(setResult, 'Direct purchase of fuel'),
    [setResult]
  );

  return (
    <div className='calculator-container'>
      <h4>
        <div className='step'>1</div>Select the best description of the electricity source
      </h4>
      <div className='step-contents'>
        <p>
          You can specify two sources; if in doubt use "Average UK Grid Electricity". Note that although UK emissions
          are in kgCO<sup>2</sup>e, for other countries they are only in kgCO<sup>2</sup>, not taking into account any
          associated emissions of methane or nitrous oxide and use 2020 data. And if you want to calculate emissions
          from road transport using miles, use the third list to select type, size and fuel of vehicle used. There are
          separate factors in the main table for trains, buses and aeroplanes. For flights, there's a list by class and
          general distance - short haul would typically be within Europe or North America and long haul
          intercontinental. Note that distances are per passenger mile - some tickets quote distances in km.
        </p>

        <div className='energy-dropdowns'>
          {dropdowns.map((d) => (
            <EnergyDropdown key={`energy-dropdown-${d.id}`} {...d} setResult={setResult} />
          ))}
        </div>
      </div>

      <h4>
        <div className='step'>2</div>Type the energy value from your bill or records into the left column
      </h4>
      <div className='step-contents'>
        <p>The answer will appear in the column to the right</p>

        <CollapsePanel className='mt-4'>
          <CollapseButton>
            <h5 className='m-0 text-ThemeAccentExtradark'>Scope 1: Direct Emissions</h5>
          </CollapseButton>
          <CollapseContent className='mt-2'>
            <Table
              columns={directEmissionsBuildingColumns}
              data={data.scope1.filter((d) => d.group === 'scope1_building')}
            />
            <Table
              columns={directEmissionsPurchaseColumns}
              data={data.scope1.filter((d) => d.group === 'scope1_purchase')}
            />
          </CollapseContent>
        </CollapsePanel>
        <hr />

        <CollapsePanel collapsed={true} className='mt-4'>
          <CollapseButton>
            <h5 className='m-0 text-ThemeAccentExtradark'>Scope 2: Indirect Emissions</h5>
          </CollapseButton>
          <CollapseContent className='mt-2'>
            <Table columns={getEnergyValueColumns(setResult)} data={data.scope2} />
          </CollapseContent>
        </CollapsePanel>
        <hr />

        <CollapsePanel collapsed={true} className='mt-4'>
          <CollapseButton>
            <h5 className='m-0 text-ThemeAccentExtradark'>Scope 3: All other Indirect Emissions</h5>
          </CollapseButton>
          <CollapseContent className='mt-2'>
            <Table
              columns={getEnergyValueColumns(setResult, 'Business travel (Category 6)')}
              data={data.scope3.filter((d) => d.group === 'scope3_business_travel')}
            />
            <Table
              columns={getEnergyValueColumns(setResult, 'Employee commuting (Category 7)')}
              data={data.scope3.filter((d) => d.group === 'scope3_commuting')}
            />
            <Table
              columns={getEnergyValueColumns(setResult, 'Waste Generated in Operations (Category 5)')}
              data={data.scope3.filter((d) => d.group === 'scope3_waste')}
            />
            <Table
              columns={getEnergyValueColumns(setResult, 'Purchased goods and services (Category 1)')}
              data={data.scope3.filter((d) => d.group === 'scope3_purchased')}
            />
          </CollapseContent>
        </CollapsePanel>
        <hr />

        <h4>
          <i className='fa fa-charging-station mr-2' />
          Energy Usage
        </h4>
        <Table
          showHeader={false}
          columns={getResultValueColumns()}
          data={[
            {
              col1: {
                id: 'buildingenergy',
                prepend: 'In buildings & processes',
                append: 'kWh',
                value: results['buildingenergy'],
              },
              col2: {
                id: 'totalenergy',
                prepend: 'Total',
                append: 'kWh',
                value: results['totalenergy'],
              },
            },
          ]}
        />
        <hr />

        <CollapsePanel collapsed={true} className='mt-4'>
          <CollapseButton>
            <h4 className='m-0'>
              Energy per m<sup>2</sup> and per employee
            </h4>
          </CollapseButton>
          <CollapseContent className='mt-2'>
            <Table
              showHeader={false}
              columns={getOptionalValueColumns(setResult)}
              data={[
                {
                  col1: {
                    id: 'floor_area',
                    prepend: 'Floor Area',
                    append: (
                      <>
                        m<sup>2</sup>
                      </>
                    ),
                    value: results['floor_area'],
                  },
                  col2: {
                    id: 'num_of_employees',
                    prepend: 'No. of Staff',
                    append: ' ',
                    value: results['num_of_employees'],
                  },
                },
              ]}
            />
            <Table
              showHeader={false}
              columns={getResultValueColumns()}
              data={[
                {
                  col1: {
                    id: 'buildingsec',
                    prepend: (
                      <>
                        Energy per m<sup>2</sup>
                      </>
                    ),
                    append: 'kWh',
                    value: results['buildingsec'],
                  },
                  col2: {
                    id: 'energy_per_employeed',
                    prepend: 'Energy per Employee',
                    append: 'kWh',
                    value: results['energy_per_employeed'],
                  },
                },
              ]}
            />
            {assessment ? <div className={`p-3 mt-4 alert ${assessment.className}`}>{assessment.text}</div> : <></>}
          </CollapseContent>
        </CollapsePanel>

        <hr />

        <h4>
          <i className='fa fa-poll mr-2' />
          Results
        </h4>
        <Table
          showHeader={false}
          columns={getResultValueColumns()}
          data={[
            {
              col1: {
                id: 'total_scope1',
                prepend: 'Scope 1 emissions',
                append: (
                  <>
                    kgCO<sup>2</sup>e
                  </>
                ),
                value: results['total_scope1'],
              },
              col2: {
                id: 'carbon_scope1',
                prepend: 'equivalent to',
                append: 'kg Carbon',
                value: results['carbon_scope1'],
              },
            },
            {
              col1: {
                id: 'total_scope2',
                prepend: 'Scope 2 emissions',
                append: (
                  <>
                    kgCO<sup>2</sup>e
                  </>
                ),
                value: results['total_scope2'],
              },
              col2: {
                id: 'carbon_scope2',
                prepend: 'equivalent to',
                append: 'kg Carbon',
                value: results['carbon_scope2'],
              },
            },
            {
              col1: {
                id: 'total_scope3',
                prepend: 'Scope 3 emissions',
                append: (
                  <>
                    kgCO<sup>2</sup>e
                  </>
                ),
                value: results['total_scope3'],
              },
              col2: {
                id: 'carbon_scope3',
                prepend: 'equivalent to',
                append: 'kg Carbon',
                value: results['carbon_scope3'],
              },
            },
            {
              col1: {
                id: 'total',
                prepend: 'Total emissions',
                append: (
                  <>
                    kgCO<sup>2</sup>e
                  </>
                ),
                value: results['total'],
              },
              col2: {
                id: 'carbon',
                prepend: 'equivalent to',
                append: 'kg Carbon',
                value: results['carbon'],
              },
            },
          ]}
        />
      </div>
      <div className='mt-4 text-right'>
        <Button onClick={() => setResults({})}>Reset</Button>
      </div>
    </div>
  );
};
