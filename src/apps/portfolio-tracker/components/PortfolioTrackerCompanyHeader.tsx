import Dashboard from '@components/dashboard';
import { PortfolioTrackerCompanySummary, PortfolioTrackerCompanySummaryProps } from './PortfolioTrackerCompanySummary';
import { PortfolioTrackerCompanyNavigation } from './PortfolioTrackerCompanyNav';

export const PortfolioTrackerCompanyHeader = (props: PortfolioTrackerCompanySummaryProps) => (
  <Dashboard hasSidebar={props.hasSidebar}>
    {props.hasSidebar ? <div className='pt-insight-virtual-sidebar d-none d-xxl-inline-block' /> : null}
    <PortfolioTrackerCompanySummary {...props} />
    <PortfolioTrackerCompanyNavigation {...props} />
  </Dashboard>
);
