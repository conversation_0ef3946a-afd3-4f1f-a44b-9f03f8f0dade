/*
 * Copyright (c) 2022-2024. World Wide Generation Ltd
 */

import React from 'react';
import { useHistory } from 'react-router-dom';
import { ROUTES } from '@constants/routes';
import { generateUrl } from '@routes/util';
import { PORTFOLIO_TRACKER_COMPANY_DOWNLOADS } from '../routes/PortfolioTrackerCompanyRoute';
import { PortfolioTrackerCompanySummaryProps } from './PortfolioTrackerCompanySummary';
import { Menu } from '@components/menu/Menu';
import { DashboardRow } from '@components/dashboard';
import { SURVEY } from '@constants/terminology';

enum PTCompanyPage {
  Report = 'report',
  Overview = 'overview',
  Environmental = 'environmental',
  Social = 'social',
  Governance = 'governance',
  Downloads = 'downloads',
}

export const PortfolioTrackerCompanyNavigation = (props: PortfolioTrackerCompanySummaryProps) => {
  const { portfolioId, company, summaryPage, questionId } = props;
  const history = useHistory();
  const companyId = company._id;

  const goToInsights = () => history.push(generateUrl(ROUTES.PORTFOLIO_TRACKER_COMPANY, { portfolioId, companyId }));
  const goToDownloads = () =>
    history.push(generateUrl(PORTFOLIO_TRACKER_COMPANY_DOWNLOADS, { portfolioId, companyId }));
  const goToSurvey = () =>
    history.push(generateUrl(ROUTES.PORTFOLIO_TRACKER_SURVEY_OVERVIEW, { portfolioId, companyId }));

  return (
    <DashboardRow>
      <div className='w-100'>
        <Menu items={[
          {
            active: summaryPage === PTCompanyPage.Report || Boolean(questionId),
            onClick: goToSurvey,
            label: SURVEY.CAPITALIZED_SINGULAR
          },
          {
            active: (!summaryPage && !questionId) ||
              summaryPage === PTCompanyPage.Environmental ||
              summaryPage === PTCompanyPage.Social ||
              summaryPage === PTCompanyPage.Governance ||
              summaryPage === PTCompanyPage.Overview,
            onClick: goToInsights,
            label: 'Insights'
          },
          {
            active: summaryPage === PTCompanyPage.Downloads,
            onClick: goToDownloads,
            label: 'Downloads'
          }
        ]} />
      </div>
    </DashboardRow>
  );
};
