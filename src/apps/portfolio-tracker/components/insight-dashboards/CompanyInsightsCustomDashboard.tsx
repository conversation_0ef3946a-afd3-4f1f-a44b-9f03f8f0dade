import { useGetPortfolioCompanyInsightsDashboardQuery } from '@apps/portfolio-tracker/api/portfolioTrackerApi';
import { DashboardSection } from '@components/dashboard';
import { BasicAlert } from '@g17eco/molecules/alert';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { GridLayoutDashboard } from '@routes/custom-dashboard/GridLayoutDashboard';
import { QueryWrapper } from '@components/query/QueryWrapper';

interface Props {
  dashboardId: string;
  portfolioId: string;
  initiativeId: string;
}

export const CompanyInsightsCustomDashboard = ({ dashboardId, portfolioId, initiativeId }: Props) => {
  const query = useGetPortfolioCompanyInsightsDashboardQuery({
    portfolioId,
    initiativeId,
    dashboardId,
  });

  return (
    <QueryWrapper
      onLoading={() => <BlockingLoader />}
      query={query}
      onError={() => {
        return (
          <DashboardSection>
            <BasicAlert type='danger'>Insight dashboard not found</BasicAlert>
          </DashboardSection>
        );
      }}
      onSuccess={(dashboard) => {
        return (
          <>
            <h3 className='text-ThemeHeadingLight ps-3 mb-3'>{dashboard.title}</h3>
            <GridLayoutDashboard
              readOnly={true}
              isEditing={false}
              hideQuestionReference={true}
              gridItems={dashboard.items}
              utrsData={dashboard.utrsData}
              initiativeId={initiativeId}
              NoDataView={() => {
                return (
                  <div className='flex-grow-1 d-flex justify-content-center align-items-center'>
                    <span className='text-ThemeTextPlaceholder'>There is no company data.</span>
                  </div>
                );
              }}
            />
          </>
        );
      }}
    />
  );
};
