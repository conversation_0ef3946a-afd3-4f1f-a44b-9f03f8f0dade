import { DashboardRow, DashboardSectionButton } from '@components/dashboard';
import { SurveyPeriodDropdown } from '@components/survey-period-dropdown';
import { CustomDashboardTitle } from '@routes/custom-dashboard/CustomDashboardTitle';
import { InsightDashboardOption } from '@routes/summary/insights/utils/sidebar';
import { DataPeriods } from '@g17eco/types/universalTracker';
import { PortfolioCompanySummary } from './PortfolioCompanySummary';
import { InitiativeCompany } from '@g17eco/types/initiative';
import { Portfolio } from '@g17eco/types/portfolio';
import { getDashboardType } from '@g17eco/types/insight-custom-dashboard';
import { useHistory, useLocation } from 'react-router-dom';
import { SurveyListItem } from '@g17eco/types/survey';
import { hasFullScopeAccess } from '@utils/dataShare';
import { InsightPage } from '@routes/summary/insights/utils/constants';
import { MainDownloadCode } from '../../../../config/app-config';
import { useGetCompanyDashboardQuery } from '@apps/portfolio-tracker/api/portfolioTrackerApi';
import { GridLayoutDashboard } from '@routes/custom-dashboard/GridLayoutDashboard';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { QueryWrapper } from '@components/query/QueryWrapper';

interface Props {
  isOverview: boolean;
  currentPage: string;
  options: InsightDashboardOption[];
  handleClickOption: (item: InsightDashboardOption) => void;
  availablePeriods: DataPeriods[];
  period: DataPeriods;
  portfolio: Pick<Portfolio, '_id'>;
  company: InitiativeCompany;
  summaryPage: InsightPage;
  mainDownloadCode: MainDownloadCode;
  item?: SurveyListItem;
  buttons?: (DashboardSectionButton | JSX.Element | null)[];
}

export const CompanyInsightsStaticDashboard = (props: Props) => {
  const {
    isOverview,
    currentPage,
    options,
    handleClickOption,
    availablePeriods,
    period,
    portfolio,
    company,
    summaryPage,
    mainDownloadCode,
    item,
    buttons,
  } = props;
  const history = useHistory();
  const location = useLocation();
  const title = isOverview ? <span className='text-ThemeHeadingLight'>Highlights</span> : undefined;
  const dashboardType = getDashboardType(summaryPage, mainDownloadCode);
  const query = useGetCompanyDashboardQuery({
    portfolioId: portfolio._id,
    initiativeId: company._id,
    dashboardType,
    params: { period },
  });

  return (
    <>
      {buttons && buttons.length > 0 ? <DashboardRow className='topbar-container' buttons={buttons} mb={2} /> : null}
      <DashboardRow className='d-xxl-none mt-4'>
        <CustomDashboardTitle
          title={<h3 className='text-ThemeHeadingLight'>{currentPage}</h3>}
          currentPage={currentPage}
          options={options}
          handleClickOption={handleClickOption}
        />
      </DashboardRow>
      <DashboardRow
        title={title}
        headingStyle={2}
        className='text-ThemeHeadingLight'
        mb={2}
        buttons={
          !isOverview
            ? [
                <SurveyPeriodDropdown
                  key='period'
                  availablePeriods={availablePeriods}
                  period={period}
                  setPeriod={(period) => {
                    history.push({
                      pathname: location.pathname,
                      search: `?period=${period}`,
                    });
                  }}
                />,
              ]
            : []
        }
      />

      <QueryWrapper
        query={query}
        onLoading={() => <LoadingPlaceholder isLoading={true} height={600} />}
        onSuccess={(dashboard) => {
          return (
            <GridLayoutDashboard
              readOnly
              hideQuestionReference
              isEditing={false}
              gridItems={dashboard.items}
              utrsData={dashboard.utrsData}
              initiativeId={company._id}
              NoDataView={() => {
                return (
                  <div className='flex-grow-1 d-flex justify-content-center align-items-center'>
                    <span className='text-ThemeTextPlaceholder'>There is no company data.</span>
                  </div>
                );
              }}
            />
          );
        }}
      />

      {!isOverview ? null : (
        <PortfolioCompanySummary
          portfolioId={portfolio._id}
          initiativeId={company._id}
          surveyId={item?._id ?? ''}
          hasFullScopeAccess={hasFullScopeAccess(company)}
        />
      )}
    </>
  );
};
