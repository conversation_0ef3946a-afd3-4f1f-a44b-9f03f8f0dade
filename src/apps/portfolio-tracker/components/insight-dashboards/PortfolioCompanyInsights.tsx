/*
 * Copyright (c) 2023-2024. World Wide Generation Ltd
 */

import { useEffect, useMemo, useState } from 'react';
import { Button, ButtonGroup } from 'reactstrap';
import { useLocation } from 'react-router-dom';
import Dashboard from '@components/dashboard';
import { CompanyInsightsSidebar } from '@routes/summary/insights/partials/sidebar/CompanyInsightsSidebar';
import { DataPeriods } from '@g17eco/types/universalTracker';
import { layoutOptions } from '@constants/layout';
import { InsightPage } from '@routes/summary/insights/utils/constants';
import { getMainDownloadCode, MainDownloadCode } from '../../../../config/app-config';
import { getValidPage, isInsightLayoutPage } from '@routes/summary/insights/utils/helpers';
import { InitiativeCompany } from '@g17eco/types/initiative';
import { Portfolio } from '@g17eco/types/portfolio';
import G17Client from '@services/G17Client';
import { RequesterType } from '@g17eco/types/dataShare';
import { SurveyListItem } from '@g17eco/types/survey';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { InsightDashboard } from '@g17eco/types/insight-custom-dashboard';
import { usePTCompanyCustomDashboards } from '@hooks/usePTCompanyCustomDashboards';
import { CompanyInsightsStaticDashboard } from './CompanyInsightsStaticDashboard';
import { CompanyInsightsCustomDashboard } from './CompanyInsightsCustomDashboard';

interface Props {
  company: InitiativeCompany;
  className?: string;
  summaryPage: string | undefined;
  portfolio: Pick<Portfolio, '_id'>;
  dashboards?: Pick<InsightDashboard, '_id' | 'title'>[];
  dashboardId?: string;
}

export const PortfolioCompanyInsights = (props: Props) => {
  const { portfolio, company, className, dashboards = [], dashboardId } = props;

  const { addSiteError } = useSiteAlert();
  const location = useLocation();
  const period = (new URLSearchParams(location.search).get('period') ?? DataPeriods.Yearly) as DataPeriods;
  const mainDownloadCode = getMainDownloadCode(company.appConfigCode, company.permissionGroup); // Do not allow to change it
  const [selectedPack, setSelectedPack] = useState<MainDownloadCode>(mainDownloadCode);
  const summaryPage = getValidPage(props.summaryPage);
  const [item, setItem] = useState<SurveyListItem | undefined>();
  const [surveyList, setSurveyList] = useState<SurveyListItem[]>([]);

  const availablePeriods = useMemo(() => {
    return Array.from(new Set(surveyList.map((s) => s.period).filter(Boolean) as DataPeriods[]));
  }, [surveyList]);

  const { currentPage, options, handleClickOption } = usePTCompanyCustomDashboards({
    portfolioId: portfolio._id,
    initiativeId: company._id,
    summaryPage,
    dashboards,
    dashboardId,
  });

  const commonSidebarProps = {
    initiativeId: portfolio._id,
    availablePeriods,
    currentPage,
    options,
    handleClickOption,
  };

  useEffect(() => {
    G17Client.getRequesterDataShare({
      requesterId: portfolio._id,
      requesterType: RequesterType.Portfolio,
      initiativeId: company._id,
    })
      .then((data) => {
        const surveyList = data.list;
        setSurveyList(surveyList);
        setItem(surveyList[0]);
      })
      .catch((e: Error) => {
        addSiteError(e);
        setSurveyList([]);
      });
  }, [addSiteError, company._id, portfolio._id]);

  const buttons =
    mainDownloadCode === MainDownloadCode.SGX_Metrics && isInsightLayoutPage(summaryPage)
      ? [
          <ButtonGroup key={mainDownloadCode}>
            {layoutOptions.map((option) => {
              const isActive = selectedPack === option.code;
              return (
                <Button
                  key={option.code}
                  outline={!isActive}
                  active={isActive}
                  color={'primary'}
                  onClick={() => setSelectedPack(option.code)}
                >
                  {option.name}
                </Button>
              );
            })}
          </ButtonGroup>,
        ]
      : [];

  const isOverview = summaryPage === InsightPage.Overview;

  return (
    <Dashboard className={`profile-dashboard ${className}`} hasSidebar={true}>
      <CompanyInsightsSidebar {...commonSidebarProps} />
      {!dashboardId ? (
        <CompanyInsightsStaticDashboard
          isOverview={isOverview}
          currentPage={currentPage}
          options={options}
          handleClickOption={handleClickOption}
          availablePeriods={availablePeriods}
          period={period}
          portfolio={portfolio}
          company={company}
          summaryPage={summaryPage}
          mainDownloadCode={mainDownloadCode}
          item={item}
          buttons={buttons}
        />
      ) : (
        <CompanyInsightsCustomDashboard
          dashboardId={dashboardId}
          portfolioId={portfolio._id}
          initiativeId={company._id}
        />
      )}
    </Dashboard>
  );
};
