/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import Dashboard from '@components/dashboard';
import { PortfolioInsightsSidebar } from '@routes/summary/insights/partials/sidebar/PortfolioInsightsSidebar';
import { useHistory, useParams } from 'react-router-dom';
import { HistoricalUtrs, HistoricalUtrsQueryByCodeParams } from '@api/insights';
import {
  useDeletePortfolioInsightDashboardMutation,
  useDuplicatePortfolioInsightDashboardMutation,
  useGetInsightDashboardByPortfolioIdQuery,
  useLazyGetPortfolioHistoricalUtrsByCodesQuery,
  useUpdatePortfolioInsightDashboardMutation,
  useUploadPortfolioInsightDashboardMediaFilesMutation,
} from '@api/portfolio-insight-dashboards';
import { Loader } from '@g17eco/atoms/loader';
import { UtrvHistoryModalTemplate } from '@components/utr-modal/UtrvHistoryModalTemplate';
import { ROUTES } from '@constants/routes';
import { GridDashboardItem, InsightDashboard, InsightDashboardFilters } from '@g17eco/types/insight-custom-dashboard';
import { CustomDashboard } from '@routes/custom-dashboard/CustomDashboard';
import { CustomDashboardEditing } from '@routes/custom-dashboard/CustomDashboardEditing';
import { CustomDashboardWrapper } from '@routes/custom-dashboard/context/CustomDashboardWrapper';
import { UtrModalBody } from '@routes/custom-dashboard/shared-dashboard/UtrModalBody';
import { generateUrl } from '@routes/util';
import { SafeInitiativeFields } from '@g17eco/types/initiative';
import { getDeletedDocumentIds, getNewFilesToUpload, mapFilesToItems } from '@features/custom-dashboard';
import { useGetValueListByIdQuery } from '@api/value-list';
import { useDashboardHistoryModal } from '@features/custom-dashboard/hooks/useDashboardHistoryModal';
import { DateRangeType } from '@g17eco/types/common';
import {
  DEFAULT_FILTERS,
  getTimeFramePeriod,
  getTimeFrameType,
} from '@routes/custom-dashboard/dashboard-settings/utils';
import {
  useInsightDashboardFilters,
  usePresetInsightDashboardFilters,
} from '@features/custom-dashboard/hooks/useInsightDashboardFilters';
import { generateErrorToast, generateToast } from '@components/toasts';
import { useSettingsSidebar } from '@routes/custom-dashboard/dashboard-settings/useSettingsSidebar';
import { usePTCustomDashboards } from '@hooks/usePTCustomDashboards';
import { BasicAlert } from '@g17eco/molecules/alert';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { useGetPortfolioByIdQuery } from '../api/portfolioTrackerApi';
import { skipToken } from '@reduxjs/toolkit/query';
import { EditingDashboardItem } from '@features/custom-dashboard/types';
import { useState } from 'react';
import { DefaultInsightViewSwitcher } from '@features/custom-dashboard/dashboard-settings/DashboardSettings';

export const PortfolioCustomDashboardRoute = () => {
  const history = useHistory();
  const { portfolioId = '', dashboardId = '' } = useParams<{ portfolioId?: string; dashboardId?: string }>();
  const { data: portfolio } = useGetPortfolioByIdQuery(portfolioId || skipToken);

  const { filters, setFilters } = useInsightDashboardFilters();
  const { dateRange, timeFrame } = filters;

  const [updateDashboard, { isLoading: isUpdating, error: updateError }] = useUpdatePortfolioInsightDashboardMutation();
  const [deleteDashboard, { isLoading: isDeleting, error: deleteError }] = useDeletePortfolioInsightDashboardMutation();
  const [duplicateDashboard] = useDuplicatePortfolioInsightDashboardMutation();
  const [getHistoricalUtrsByCodes, { isLoading: isLoadingUtrsData }] = useLazyGetPortfolioHistoricalUtrsByCodesQuery();
  const {
    data: dashboard,
    isFetching,
    error,
  } = useGetInsightDashboardByPortfolioIdQuery({
    initiativeId: portfolioId,
    dashboardId,
    queryParams: { timeFrameType: timeFrame, dateRange },
  });
  const [uploadFiles, { isLoading: isUploading }] = useUploadPortfolioInsightDashboardMediaFilesMutation();
  const {
    firstValueListCode,
    valueListId,
    isNumericValueList,
    selectingUtrData,
    handleOpenUtrvHistoryModal,
    resetSelectingUtrData,
  } = useDashboardHistoryModal();
  const { data: valueList } = useGetValueListByIdQuery(valueListId ?? '', {
    skip: !isNumericValueList || !valueListId,
  });
  const [editingItem, setEditingItem] = useState<EditingDashboardItem | undefined>(undefined);

  usePresetInsightDashboardFilters({ filters, setFilters, dashboard });

  const settingsSidebarProps = useSettingsSidebar({ initiativeId: portfolioId });
  const { handleAddNew } = settingsSidebarProps;
  const {
    currentPage,
    options,
    isFetchingDashboards,
    isEditing,
    setIsEditing,
    handleClickOption,
    handleNavigateCustom,
  } = usePTCustomDashboards({ portfolioId, dashboardId, handleAddNew });
  const commonSidebarProps = {
    ...settingsSidebarProps,
    portfolioId,
    currentPage,
    options,
    handleClickOption,
    handleNavigateCustom,
  };

  if (!dashboard || !portfolio || isFetchingDashboards) {
    return (
      <Dashboard className={'insights-dashboard'} hasSidebar={true}>
        <PortfolioInsightsSidebar {...commonSidebarProps} />
        <LoadingPlaceholder height={600} />
      </Dashboard>
    );
  }

  const initiative: SafeInitiativeFields = {
    _id: portfolio._id,
    name: portfolio.name,
    profile: portfolio.profile ?? '',
    description: portfolio.description,
    missionStatement: portfolio.missionStatement,
    industryText: '',
    sectorText: '',
  };

  const handleClickEdit = () => setIsEditing(true);

  const handleCancel = () => {
    setIsEditing(false);
  };

  const handleSave = async (changes: Partial<InsightDashboard>, keepEditing?: boolean) => {
    const newItems = changes.items ?? dashboard.items; // Fallback to existing dashboard items when saving settings to avoid clear all items.;
    const newFiles = getNewFilesToUpload(newItems);

    const uploadedFiles = Object.keys(newFiles).length
      ? await uploadFiles({ files: newFiles, initiativeId: portfolioId, dashboardId }).unwrap()
      : [];

    // Map media back to items with new url, documentId properties.
    const itemsWithMedia = mapFilesToItems(uploadedFiles, newItems);

    const deletedDocumentIds = getDeletedDocumentIds(dashboard.items, newItems);
    await updateDashboard({
      ...dashboard,
      ...changes,
      items: itemsWithMedia,
      initiativeId: portfolioId,
      deletedDocumentIds,
    });

    if (!keepEditing) {
      setIsEditing(false);
    }

    // Sync with timeFrame filter
    if (changes.filters) {
      const { type: timeFrame, startDate, endDate } = changes.filters.timeFrame ?? DEFAULT_FILTERS.timeFrame;
      setFilters({ timeFrame: timeFrame, dateRange: { startDate, endDate } });
    }
  };

  const handleDelete = async () => {
    return deleteDashboard({ dashboardId, initiativeId: portfolioId })
      .unwrap()
      .then(() => {
        generateToast({
          color: 'info',
          title: (
            <>
              <i className='fal fa-trash text-sm mr-2'></i>Dashboard deleted
            </>
          ),
          message: `${dashboard.title} has been deleted successfully.`,
        });
        history.push(generateUrl(ROUTES.PORTFOLIO_TRACKER_INSIGHTS, { portfolioId }));
      })
      .catch((error) => {
        generateErrorToast(error);
      })
      .finally(() => {
        setIsEditing(false);
      });
  };

  const handleDuplicateDashboard = async () => {
    return duplicateDashboard({ dashboardId, initiativeId: portfolioId }).unwrap();
  };

  const onClickUtrvHistoryModal = (props: { item: GridDashboardItem; utrData?: HistoricalUtrs }) => {
    handleOpenUtrvHistoryModal({ ...props, initiativeId: portfolioId });
  };

  const handleGetHistoricalUtrsByCodes = ({ utrCodes: [utrCode], queryParams }: HistoricalUtrsQueryByCodeParams) => {
    return getHistoricalUtrsByCodes({ initiativeId: portfolioId, utrCodes: [utrCode], queryParams }).unwrap();
  };

  const handleChangeDateRange = (dateRange: DateRangeType, timeFrame: string | number) => {
    setFilters({ timeFrame: getTimeFrameType(timeFrame), dateRange });
  };

  const renderCustomDashboard = () => {
    return (
      <CustomDashboardWrapper
        getHistoricalUtrsByCodes={handleGetHistoricalUtrsByCodes}
        duplicateDashboard={handleDuplicateDashboard}
        hideOptions={[
          InsightDashboardFilters.ShareWithSubsidiaries,
          InsightDashboardFilters.BaselinesTargets,
          InsightDashboardFilters.Period,
          InsightDashboardFilters.Survey,
          InsightDashboardFilters.Privacy,
          InsightDashboardFilters.InitiativeIds,
        ]}
        hideShareButton={true}
        hideQuestionReference={true}
        readOnly={true}
        components={{
          [InsightDashboardFilters.DisplayAsDefault]: DefaultInsightViewSwitcher,
        }}
      >
        {isFetching || isUpdating || isDeleting || isLoadingUtrsData || isUploading ? <Loader /> : null}
        {selectingUtrData ? (
          <UtrvHistoryModalTemplate toggle={resetSelectingUtrData}>
            <UtrModalBody
              utr={selectingUtrData.getUniversalTracker()}
              utrvs={selectingUtrData.getUniversalTrackerValues()}
              firstValueListCode={firstValueListCode}
              valueList={valueList}
            />
          </UtrvHistoryModalTemplate>
        ) : null}
        {isEditing ? (
          <CustomDashboardEditing
            editingItem={editingItem}
            setEditingItem={setEditingItem}
            dashboard={dashboard}
            handleCancel={handleCancel}
            handleSave={handleSave}
            handleDelete={handleDelete}
            handleOpenUtrvHistoryModal={onClickUtrvHistoryModal}
            survey={undefined}
            availablePeriods={[]}
            period={undefined}
            initiative={initiative}
          />
        ) : (
          <CustomDashboard
            dashboard={dashboard}
            handleClickEdit={handleClickEdit}
            handleSave={handleSave}
            handleDelete={handleDelete}
            canManage={true}
            isCurrentLevelDashboard={true}
            handleOpenUtrvHistoryModal={onClickUtrvHistoryModal}
            survey={undefined}
            availablePeriods={[]}
            initiative={initiative}
            dateRange={dateRange}
            onChangeDateRange={handleChangeDateRange}
            timeRange={getTimeFramePeriod(timeFrame)}
            currentPage={currentPage}
            dashboardOptions={options}
            handleClickOption={handleClickOption}
          />
        )}
      </CustomDashboardWrapper>
    );
  };

  return (
    <Dashboard className={'insights-dashboard'} hasSidebar={true}>
      <PortfolioInsightsSidebar {...commonSidebarProps} />
      {error || updateError || deleteError ? (
        <>
          {[error, updateError, deleteError].map((error) =>
            error ? (
              <BasicAlert key={error.name} type={'danger'}>
                {error.message}
              </BasicAlert>
            ) : null
          )}
        </>
      ) : null}
      {renderCustomDashboard()}
    </Dashboard>
  );
};
