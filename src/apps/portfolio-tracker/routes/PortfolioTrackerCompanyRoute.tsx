/*
 * Copyright (c) 2022-2024. World Wide Generation Ltd
 */

import { useCallback, useEffect, useState } from 'react';
import { Route, Switch, useParams } from 'react-router-dom';
import { RouteInterface } from '@g17eco/types/routes';
import { DataShareDownloadContainer } from '@components/data-share/DataShareDownloadContainer';
import { DataShareScopeView, RequesterType } from '@g17eco/types/dataShare';
import { getPortfolioCompanies } from '@actions/portfolio-tracker';
import { InitiativeCompany } from '@g17eco/types/initiative';
import { addSiteAlert } from '../../../slice/siteAlertsSlice';
import { Loader } from '@g17eco/atoms/loader';
import { PortfolioCompanySurveyOverview } from '../components/PortfolioCompanySurveyOverview';
import {
  PortfolioTrackerCompanySummaryProps,
} from '../components/PortfolioTrackerCompanySummary';
import { useAppDispatch, useAppSelector } from '../../../reducers';
import { isViewShared } from '@utils/dataShare';
import { NotShared } from '../components/NotShared';
import { SURVEY } from '@constants/terminology';
import { PortfolioCompanyInsightsWrapper } from '../components/insight-dashboards/CompanyInsightsWrapper';
import { PortfolioTrackerCompanyHeader } from '../components/PortfolioTrackerCompanyHeader';
import { useGetPortfolioByIdQuery } from '../api/portfolioTrackerApi';
import { skipToken } from '@reduxjs/toolkit/query';

const summaryPageUrl = '/:summaryPage(report|overview|downloads|environmental|social|governance|internal)?';
const basePath = '/portfolio-tracker/portfolio/:portfolioId/company/:companyId';

export const PORTFOLIO_TRACKER_COMPANY: RouteInterface = {
  id: 'portfolio_tracker_company',
  label: 'Company Profile',
  path: `${basePath}${summaryPageUrl}`,
  component: PortfolioTrackerCompanyRoute,
  appPermissionId: 'app_portfolio_tracker',
  exact: true,
  auth: true,
  parentId: 'portfolio_tracker'
}

export const PORTFOLIO_TRACKER_COMPANY_DOWNLOADS: RouteInterface = {
  id: 'portfolio_tracker_company_download',
  label: 'Downloads',
  path: `${basePath}/downloads`,
  component: PortfolioTrackerCompanyRoute,
  appPermissionId: 'app_portfolio_tracker',
  exact: true,
  auth: true,
  parentId: 'portfolio_tracker'
}

interface RouteParams {
  portfolioId: string;
  companyId: string;
  page?: string;
  pageParam?: string;
  summaryPage?: string;
  questionId?: string;
  dashboardId?: string;
}

export function PortfolioTrackerCompanyRoute() {
  const { portfolioId, companyId, summaryPage, questionId, dashboardId } = useParams<RouteParams>();
  const dispatch = useAppDispatch();
  const [companies, setCompanies] = useState<InitiativeCompany[]>([]);
  const company = companies.find((c) => c._id === companyId);
  const { data: portfolio } = useGetPortfolioByIdQuery(portfolioId || skipToken);

  const reload = useCallback(() => {
    getPortfolioCompanies(portfolioId)
      .then((d) => setCompanies(d))
      .catch(() => {
        dispatch(
          addSiteAlert({
            content: 'There was a problem getting your portfolio information. Please refresh the page and try again',
            timeout: 5000,
          })
        );
      })
  }, [dispatch, portfolioId])

  useEffect(() => {
    reload();
  }, [portfolioId, reload]);

  if (!company || !portfolio) {
    return <Loader />;
  }

  const ptCompanyHeaderProps: PortfolioTrackerCompanySummaryProps = {
    portfolioId,
    company,
    summaryPage,
    questionId,
  };

  return (
    <div className='flex-column'>
      <Switch>
        <Route path={`${basePath}/report/:questionId?/:index?`}>
          {(isViewShared(company, DataShareScopeView.Survey)) ? (
            <>
              <PortfolioTrackerCompanyHeader {...ptCompanyHeaderProps} />
              <PortfolioCompanySurveyOverview initiativeId={companyId} portfolioId={portfolioId} />
            </>
          ) : (
            <>
              <PortfolioTrackerCompanyHeader {...ptCompanyHeaderProps} />
              <NotShared title={SURVEY.CAPITALIZED_PLURAL}>{`Company has not shared ${SURVEY.CAPITALIZED_PLURAL}`}</NotShared>
            </>
          )}
        </Route>
        <Route path={`${basePath}/downloads`}>
          {(isViewShared(company, DataShareScopeView.Downloads)) ? (
            <>
              <PortfolioTrackerCompanyHeader {...ptCompanyHeaderProps} />
              <DataShareDownloadContainer
                reload={reload}
                company={company}
                portfolio={portfolio}
                requesterType={RequesterType.Portfolio}
                initiativeId={companyId}
                requesterId={portfolioId}
              />
            </>
          ) : (
            <>
              <PortfolioTrackerCompanyHeader {...ptCompanyHeaderProps} />
              <NotShared title='Downloads'>Company has not shared Downloads</NotShared>
            </>
          )}
        </Route>
        <Route>
          <PortfolioCompanyInsightsWrapper
            company={company}
            portfolio={portfolio}
            summaryPage={summaryPage}
            ptCompanyHeaderProps={ptCompanyHeaderProps}
            dashboardId={dashboardId}
          />
        </Route>
      </Switch>
    </div>
  );
}
