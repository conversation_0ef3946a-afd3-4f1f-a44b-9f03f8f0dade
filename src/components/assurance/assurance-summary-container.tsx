/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import ClampLines from 'react-clamp-lines';
import { DashboardRow } from '../dashboard';
import React, { useState } from 'react';
import { AssurancePortfolio, AssurancePortfolioStatus } from '../../types/assurance';
import { <PERSON>ton, Modal, <PERSON>, <PERSON>dal<PERSON>ooter, ModalHeader } from 'reactstrap';
import { apiClient } from '../../actions/api';
import { DATE, formatDate } from '../../utils/date';
import { SubmitButton } from '../button/SubmitButton';
import { BasicAlert } from '@g17eco/molecules/alert';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

interface Props {
  assurancePortfolio: AssurancePortfolio;
  onRemoval?: () => void;
}

const fileDownload = (e: React.MouseEvent<any>, id: string, url: string) => {
  e.preventDefault();
  if (url) {
    window.open(url, '_blank', '');
    return;
  }
  return apiClient.get('documents/' + id).then((response) => {
    const body = response.data;
    if (body.success) {
      window.open(body.data.url, '_blank', '');
    }
  }).catch(console.log)
};

const getStatus = (status: AssurancePortfolioStatus) => {
  switch (status) {
    case AssurancePortfolioStatus.Completed:
      return 'Completed';
    case AssurancePortfolioStatus.Created:
      return 'Being Prepared';
    case AssurancePortfolioStatus.Pending:
      return 'Waiting for Assurer';
    case AssurancePortfolioStatus.Processing:
    default:
      return 'Waiting';
  }
}


const getDefaultState = () => ({
  errorMessage: '',
  isOpen: false,
  documentId: ''
});

export const AssuranceSummaryContainer = ({ assurancePortfolio, onRemoval }: Props) => {

  const [modalState, setModalState] = useState(getDefaultState());

  const toggleConfirm = (documentId: string) => {
    setModalState(s => ({ ...s, documentId, isOpen: true }))
  };

  const handleClose = () => setModalState(getDefaultState());

  const handlClick = async () => {
    apiClient.delete(`/assurances/portfolio/${assurancePortfolio._id}/documents`, {
      data: { documentIds: [modalState.documentId] }
    }).then(() => {
      if (onRemoval) {
        onRemoval();
      }
      handleClose();
    })
      .catch(e => setModalState(s => ({ ...s, errorMessage: e.message })))
  };


  const renderFileLink = (portfolio: AssurancePortfolio, title: string, type: string) => {
    const file = portfolio.documents.find(d => d.type === type);
    const document = file && portfolio.historyDocuments.find(d => d._id === file.documentId);
    if (!document) {
      return <div>
        <div className='mt-3'>{title}</div>
        <div className='file text-muted'>
          <i className='fa fa-file mr-2' />-
        </div>
      </div>;
    }

    const removeBtn = assurancePortfolio.status === AssurancePortfolioStatus.Created && onRemoval ? (
      <div className='status-icon text-center'>
        <SimpleTooltip text={'Remove document'}>
          <i onClick={() => toggleConfirm(file.documentId)} className={'fas fa-trash-alt text-danger'} />
        </SimpleTooltip>
      </div>
    ) : null;

    return <div>
      <div className='mt-3'>{title}</div>
      <div className={'d-flex aling-items-center'}>
        <Button color='link' size='sm' className='file text-truncate text-ThemeAccentMedium text-left'
                onClick={(e) => fileDownload(e, document._id, document.url)}>
          <i className='fa fa-file text-ThemeAccentMedium mr-2' />{document.metadata.name}
        </Button>
        {removeBtn}
      </div>
    </div>;
  }

  const owner = assurancePortfolio.assurers.find(assurer => assurer.isAdmin);
  const ownerName = owner ? `${owner.firstName ?? ''} ${owner.surname ?? ''}` : '';
  const removalDocument = assurancePortfolio.historyDocuments.find(d => d._id === modalState.documentId);
  const documentName = removalDocument?.metadata?.name ?? 'this';

  return (
    <DashboardRow>
      <div className='summary flex-fill row'>
        <div className='col-sm-8 col-12 mb-2'>
          <div className='summaryContainer d-flex flex-column'>
            <div className='header-row d-flex justify-content-between'>
              <div>
                <label className='text-muted'>ASSURER</label>
                <div>{assurancePortfolio.organization.name}</div>
              </div>
              <div>
                <label className='text-muted'>REPORT</label>
                <div>
                  <strong>
                  {formatDate(assurancePortfolio.survey.effectiveDate, DATE.MONTH_YEAR, true)}
                  </strong>
                </div>
              </div>
            </div>
            <div className='mt-3 flex-grow-1'>
              <label className='text-muted'>DESCRIPTION</label>
              <ClampLines text={assurancePortfolio.description ?? ''}
                id={assurancePortfolio._id}
                lines={4}
                moreText='more'
                lessText='less'
                ellipsis='...'
              />
            </div>

            {ownerName && <div className='mt-3 flex-grow-1'>
              <label className='text-muted'>CONTACT</label>
              <div>{ownerName}</div>
            </div>}

            <div className={'mt-2'}>
              <span className='badge rounded-pill bg-secondary background-ThemeAccentExtradark'>{getStatus(assurancePortfolio.status)}</span>
            </div>
          </div>
        </div>
        <div className='col-sm-4 col-12 mb-2'>
          <div className='summaryContainer associatedFiles ml-sm-2'>
            <label className='text-muted'>ASSOCIATED FILES</label>
            {renderFileLink(assurancePortfolio, 'Basis of Reporting', 'basis_of_reporting')}
            {renderFileLink(assurancePortfolio, 'Management Statement', 'management_statement')}
            {renderFileLink(assurancePortfolio, 'Assurance Statement', 'assurance_statement')}
          </div>
        </div>
      </div>
      <Modal isOpen={modalState.isOpen} toggle={handleClose} backdrop='static'>
        <ModalHeader toggle={handleClose} >Remove Assurance Document</ModalHeader>
        <ModalBody>
          <BasicAlert type={'danger'}>{modalState.errorMessage}</BasicAlert>
          Are you sure you would like to remove "<strong>{documentName}</strong>" document?
        </ModalBody>
        <ModalFooter>
          <Button color='link-secondary' className='mr-3' onClick={handleClose}>Cancel</Button>
          <SubmitButton color='danger' onClick={handlClick} className='text-right px-0'>
            <span>Remove</span>
          </SubmitButton>
        </ModalFooter>
      </Modal>
    </DashboardRow>
  )
}
