/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import React from 'react';
import { AssurancePortfolio } from '../../types/assurance';
import { Avatar } from '@g17eco/atoms/avatar';
import { DashboardRow } from '../dashboard';
import { optionPortfolioType } from '../../utils/portfolio';

interface Props {
  assurancePortfolio: AssurancePortfolio;
}

export const AssuranceSummaryTitle = ({ assurancePortfolio }: Props) => {

  const name = assurancePortfolio.initiative?.name ?? 'Organisation';
  const type = optionPortfolioType.find(o => o.value === assurancePortfolio.portfolioType)?.label ?? 'Portfolio';
  return <DashboardRow>
    <div className='mb-2 flex-fill d-flex align-items-center'>
      <div className='col'>
        <h3>{name} - {type}</h3>
      </div>
      {assurancePortfolio.organization?.profile ? (
        <div className='logoContainer mr-4'>
          <Avatar width='100%' className={'avatar logo-shadow'} type={'organization'}>
            <img alt='avatar' src={assurancePortfolio.organization.profile} />
          </Avatar>
        </div>
      ) : null}
    </div>
  </DashboardRow>
}
