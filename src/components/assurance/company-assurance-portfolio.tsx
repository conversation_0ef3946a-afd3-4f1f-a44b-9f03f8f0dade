/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React from 'react';
import { connect, ConnectedProps } from 'react-redux';
import { RouteComponentProps, withRouter } from 'react-router-dom';
import { Button, Modal, ModalBody, ModalHeader } from 'reactstrap';
import { Loader } from '@g17eco/atoms/loader';
import { getSurveyAssurancePortfolio, setAssurancePortfolioReady } from '../../actions/assurance';
import { AssuranceQuestionModal } from './assurance-question-modal';
import { loadSurvey, reloadSurveyListSummary } from '../../actions/survey';
import SurveyQuestionList from '../survey-question-list/survey-question-list';
import { getSurveyGroupsSelector } from '../../selectors/survey';
import { isUserManagerByInitiativeId } from '../../selectors/user';
import { PERMISSION_GROUPS } from '../../utils/permission-groups';
import { AssurancePortfolio, AssurancePortfolioStatus } from '../../types/assurance';
import { ScopeQuestionGroupOptionalValue, SurveySettings } from '../../types/survey';
import { SurveyModelMinData } from '../../model/surveyData';
import { RootState } from '../../reducers';
import NotAuthorised from '../../routes/not-authorised';
import Dashboard, { DashboardRow, DashboardSection } from '../dashboard';
import { AssuranceSummaryContainer } from './assurance-summary-container';
import { AssuranceSummaryTitle } from './assurance-summary-title';
import AssuranceModalCreate from './assurance-modal-create';
import { addSiteAlert, SiteAlertColors } from '../../slice/siteAlertsSlice';
import { assuranceSurveyGroups } from '../../utils/assurance';
import { loadScorecardsByInitiativeId, loadScorecardsBySurveyId } from '../../actions/scorecard';
import { loadBlueprintMapping } from '../../actions/blueprints';
import { loadUNSDGMap } from '../../actions/common';
import { SurveyQuestionListToolbar } from '../survey/survey-question-list-toolbar';
import { ExpandAllToggle } from '../survey/survey-question-list-toolbar/partials/ExpandAllToggle';
import { ScopeViewDropdown } from '../survey/survey-question-list-toolbar/partials/ScopeViewDropdown';
import './company-assurance-portfolio.scss';
import { FilterSettings } from '../survey/survey-question-list-toolbar/partials/FilterQuestions';
import { disableByModules, disableBySearch, disableByStatus, DisableUtrsProps } from '../survey/utils/getDisableUtrs';
import { generateUrl } from '../../routes/util';
import { ROUTES } from '../../constants/routes';
import { FeaturePermissions } from '../../services/permissions/FeaturePermissions';
import { AssuranceSettingsType, portfolioBundleDownload } from '../assurer/assurer-assurance-portfolio';
import { SubmitButton } from '../button/SubmitButton';
import { QUESTION, SURVEY } from '@constants/terminology';
import { SearchQuestions } from '../survey/survey-question-list-toolbar/partials/SearchQuestions';
import { generateSurveyQuestionsFlexSearchMap } from '../../selectors/blueprint';
import { ColumnBookmark } from '../survey-question-list/partials/ColumnBookmark';
import { ColumnValue } from '../survey-question-list/partials/ColumnValue';
import { ColumnStatusIcon } from '../survey-question-list/partials/ColumnStatusIcon';
import { endpoints as utrvsCommentEndpoints } from '../../api/utrv-comments';
import { ColumnCommentFlag, ColumnTitle } from '@g17eco/molecules/survey-question-list';
import { Divider } from '@g17eco/molecules/tracking-list';
import { FilterToggle } from '@g17eco/molecules/filter-toggle';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

interface AssurancePortfolioComponentProps {
  assurancePortfolioId: string;
}

type Props = PropsFromRedux & AssurancePortfolioComponentProps & RouteComponentProps;

interface State {
  loaded: boolean;
  saving: boolean;
  assurancePortfolio?: AssurancePortfolio;
  survey?: SurveyModelMinData;
  error: boolean;
  errorMessage: string;
  editModal: {
    isOpen: boolean,
    initiativeId: string,
    surveyId: string,
    portfolioId: string
    initialForm?: Record<string, string | undefined>,
  };
  questionModal: { initiativeId: string, surveyId: string; isOpen: boolean; portfolioId: string };
  stakeholdersModal: { portfolioId: string; isOpen: boolean };
  permissionRequired: undefined | [string, string],
  permissionGroup: string;
  filterSettings: Partial<FilterSettings>;
}

/** @TODO: should migrate this to function component
 * @deprecated loadBlueprintMapping, should migrate to use RTK blueprintsApi
 */
class CompanyAssurancePortfolio extends React.Component<Props> {

  state: State = {
    loaded: false,
    saving: false,
    assurancePortfolio: undefined,
    error: false,
    errorMessage: '',
    questionModal: {
      isOpen: false,
      initiativeId: '',
      surveyId: '',
      portfolioId: '',
    },
    stakeholdersModal: {
      portfolioId: '',
      isOpen: false,
    },
    editModal: {
      isOpen: false,
      surveyId: '',
      initiativeId: '',
      portfolioId: '',
    },
    permissionRequired: undefined,
    permissionGroup: PERMISSION_GROUPS.FREE,
    filterSettings: {
      filterByStatus: [],
      filterByModules: [],
      searchText: ''
    },
  };

  toggleQuestionModal = (portfolio?: AssurancePortfolio) => {
    this.setState({
      questionModal: {
        ...this.state.questionModal,
        surveyId: portfolio?.surveyId,
        initiativeId: portfolio?.initiativeId,
        portfolioId: portfolio?._id,
        isOpen: !this.state.questionModal.isOpen
      }
    })
  };

  toggleEditModal = (portfolio?: AssurancePortfolio) => {

    const admin = portfolio?.assurers.find(assurer => assurer.isAdmin);
    const name = admin ? [admin.firstName, admin.surname].filter(Boolean).join(' ') : undefined;

    this.setState({
      editModal: {
        ...this.state.editModal,
        isOpen: !this.state.editModal.isOpen,
        portfolioId: portfolio?._id,
        surveyId: portfolio?.surveyId,
        initiativeId: portfolio?.initiativeId,
        initialForm: {
          portfolioId: portfolio?._id,
          portfolioType: portfolio?.portfolioType,
          organizationId: portfolio?.organizationId,
          assurancePrimaryContactName: name,
          assurancePrimaryContactId: admin?._id,
          description: portfolio?.description,
          title: portfolio?.title,
        }
      }
    })
  };

  handleChangeSettings = (key: keyof Partial<SurveySettings>, setting: string | string[] | boolean) => {
    this.setState((state: State) => (
      {
        filterSettings: {
          ...state.filterSettings,
          [key]: setting
        }
      }));
  }

  setReadyForAssurer = () => {
    this.setState({
      saving: true
    });
    setAssurancePortfolioReady(this.props.assurancePortfolioId)
      .then(() => this.loadAssurancePortfolio(this.props.assurancePortfolioId))
      .then(() => {
        this.props.addSiteAlert({
          content: <><strong>Success!</strong> The {QUESTION.PLURAL} have been sent for assurance</>,
          timeout: 10000
        });
        this.setState({ saving: false });
      })
      .catch((e: Error) => {
        this.props.addSiteAlert({
          content: `There was a problem sending the ${QUESTION.PLURAL} for assurance. Please try again.`,
          timeout: 10000,
          color: SiteAlertColors.Danger
        });
        this.setState({
          loaded: true,
          error: true,
          saving: false,
          errorMessage: e.message
        });
      });
  }

  handleQuestionUpdate = () => {
    this.setState({
      loaded: false,
      questionModal: {
        ...this.state.questionModal,
        surveyId: undefined,
        portfolioId: undefined,
        initiativeId: undefined,
        isOpen: !this.state.questionModal.isOpen
      }
    }, () => this.reloadData());
  };

  componentDidMount() {
    this.loadAssurancePortfolio(this.props.assurancePortfolioId);
  }

  componentDidUpdate(prevProps: Props) {
    if (
      !prevProps.surveyState.loaded && this.props.surveyState.loaded
    ) {
      const surveyData = this.props.surveyState.data;
      this.props.loadUNSDGMap();
      this.props.loadBlueprintMapping(surveyData.sourceName);
      this.props.loadScorecardsByInitiativeId(surveyData.initiativeId);
      this.props.loadScorecardsBySurveyId(surveyData.initiativeId, surveyData._id);
      this.props.getUtrvsCommentCount(surveyData._id);
    }
  }

  reloadData() {
    this.loadAssurancePortfolio(this.props.assurancePortfolioId);
    this.props.reloadSurveyListSummary();
  }

  async loadAssurancePortfolio(assurancePortfolioId: string) {
    return getSurveyAssurancePortfolio(assurancePortfolioId)
      .then((assurancePortfolio: AssurancePortfolio) => {
        const alreadyLoaded = this.state.loaded;
        this.setState({
          assurancePortfolio,
          loaded: true
        });
        return this.props.loadSurvey(assurancePortfolio.surveyId, !alreadyLoaded);
      })
      .catch((e: Error) => this.setState({
        loaded: true,
        error: true,
        errorMessage: e.message
      }));
  }

  getDisabledUtrs = ({
    surveyGroups,
    searchIndex,
  }: {
    surveyGroups: ScopeQuestionGroupOptionalValue[];
    searchIndex: DisableUtrsProps['searchIndex'];
  }) => {
    const { user } = this.props;
    const surveyData = this.props.surveyState.loaded ? this.props.surveyState.data : undefined
    const filterProps = {
      ...this.state.filterSettings,
      surveyGroups,
      user,
      metricGroups: surveyData?.customMetricGroups
    };

    return [
      ...disableByStatus(filterProps),
      ...disableByModules(filterProps),
      ...disableBySearch({ ...filterProps, searchIndex }),
    ];
  };

  render() {
    const { loaded, assurancePortfolio, error, errorMessage, filterSettings } = this.state;
    const { surveyState, utrvsCommentCount } = this.props;

    if (error) {
      return <Dashboard>
        <DashboardSection title='Unable to load assurance tracker'>
          <>{errorMessage}</>
        </DashboardSection>
      </Dashboard>
    }

    if (!loaded || !surveyState.loaded) {
      return <Loader />;
    }

    if (!assurancePortfolio) {
      return <Dashboard>
        <DashboardSection title='Unable to load assurance tracker'>
          <>Assurance Tracker not found</>
        </DashboardSection>
      </Dashboard>
    }

    if (!this.props.canAccessAssurance) {
      return <NotAuthorised />
    }

    const isSentToAssurer = assurancePortfolio.status === AssurancePortfolioStatus.Pending;
    const isCompleted = assurancePortfolio.status === AssurancePortfolioStatus.Completed;
    const surveyGroups = this.getFilteredScopeQuestionGroup(assurancePortfolio);
    const filteredSurveyGroups = surveyGroups.filter(group => group.list.length > 0);
    const searchIndex = generateSurveyQuestionsFlexSearchMap(surveyState.data);
    const disabledUtrs = this.getDisabledUtrs({ surveyGroups: filteredSurveyGroups, searchIndex });

    const isEmptyList = filteredSurveyGroups.length === 0;
    const isReadyBtnDisabled = this.state.saving || isSentToAssurer || isEmptyList;
    const surveyGroupCount = filteredSurveyGroups.length;
    return <div>
      <Dashboard className={'pb-0'}>
        <div className={'col'}>
          <Button
            color='link'
            style={{ fontSize: '13px' }}
            onClick={() => {
              const redirectUrl = generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, {
                initiativeId: assurancePortfolio.initiativeId,
                surveyId: assurancePortfolio.surveyId,
                page: 'overview',
              });
              this.props.history.push(redirectUrl);
            }}
          >
            <i className='fa fa-arrow-circle-left mr-2' />
            Go back
          </Button>
        </div>
      </Dashboard>
      <Dashboard className='assuranceContainer'>
        <AssuranceSummaryTitle assurancePortfolio={assurancePortfolio} />
        <AssuranceSummaryContainer
          onRemoval={() => this.loadAssurancePortfolio(this.props.assurancePortfolioId)}
          assurancePortfolio={assurancePortfolio} />
        <DashboardRow
          className='mt-3'
          title={`${QUESTION.CAPITALIZED_PLURAL} for assurance`}
          headingStyle={3}
          buttons={[
            this.getEditButton(assurancePortfolio),
            this.renderManageQuestionsButton(isCompleted, assurancePortfolio),
            this.renderDownloadButton(
              {
                text: 'Download all data & evidence',
                tooltip: 'Download all data and evidence',
                icon: 'fa fa-file-archive',
                handler: () => {
                  this.setState({ saving: true })
                  portfolioBundleDownload(
                    assurancePortfolio._id,
                    assurancePortfolio.initiativeId
                  ).catch((e: Error) => {
                    this.props.addSiteAlert({
                      content: 'There was a problem downloading',
                      color: SiteAlertColors.Danger
                    })
                  }).finally(() => this.setState({ saving: false }))
                }
              }
            )
          ]}
        >
        </DashboardRow>
        <DashboardSection padding={0}>
          <SurveyQuestionList
            surveyId={assurancePortfolio.surveyId}
            initiativeId={assurancePortfolio.initiativeId}
            surveyGroups={filteredSurveyGroups}
            disabledUTRs={disabledUtrs}
            handleNoRows={() => this.toggleQuestionModal(assurancePortfolio)}
            noRowsMessage={`No ${QUESTION.PLURAL} have been selected for assurance`}
            columns={[
              ColumnBookmark,
              ColumnTitle,
              Divider,
              (props) => <ColumnCommentFlag {...props} utrvsCommentCount={utrvsCommentCount.data} />,
              Divider,
              ColumnValue,
              Divider,
              ColumnStatusIcon
            ]}
            toolbar={(props) => (
              <SurveyQuestionListToolbar
                {...props}
                settings={filterSettings}
                handleChangeSettings={this.handleChangeSettings}
                surveyScope={surveyState.data.scope}
                components={{
                  top: [SearchQuestions, ScopeViewDropdown],
                  bottom: [FilterToggle, ...(surveyGroupCount > 1 ? [ExpandAllToggle] : [])]
                }}
              />
            )}
          />
          {!isCompleted ?
            <div className='text-right mt-2'>
              <SimpleTooltip id='ready-for-assurance' text='Send this Portfolio to Assurer'>
                <div className={'d-inline-block'}>
                  <Button className='ml-2 mt-3 px-3 btn-disable-tooltip'
                    disabled={isReadyBtnDisabled}
                    onClick={() => this.setReadyForAssurer()}
                  >
                    {isSentToAssurer ? 'Sent to assurer' : 'Send to assurer'}
                  </Button>
                </div>
              </SimpleTooltip>
            </div>
            : <></>}
        </DashboardSection>
        <AssuranceQuestionModal
          {...this.state.questionModal}
          afterSubmit={this.handleQuestionUpdate}
          toggle={() => this.toggleQuestionModal()} />

        <Modal isOpen={this.state.editModal.isOpen} toggle={() => this.toggleEditModal()} backdrop='static' className='assurance-modal'>
          <ModalHeader toggle={() => this.toggleEditModal()}>Edit Assurance</ModalHeader>
          <ModalBody>
            <AssuranceModalCreate
              key={this.state.editModal.portfolioId}
              {...this.state.editModal} toggle={(submitted: boolean) => {
                if (submitted) {
                  this.loadAssurancePortfolio(this.props.assurancePortfolioId)
                    .then(() => this.toggleEditModal())
                } else {
                  this.toggleEditModal();
                }
              }} />
          </ModalBody>
        </Modal>
      </Dashboard>;
    </div>
  }

  private getEditButton(assurancePortfolio: AssurancePortfolio) {
    if (assurancePortfolio.status !== AssurancePortfolioStatus.Created) {
      return <></>;
    }

    return <SimpleTooltip
      id='toggle-edit-modal'
      text={'Edit assurance'}
      component={<Button
        disabled={!this.props.isManager}
        onClick={() => this.toggleEditModal(assurancePortfolio)}
        className='ml-2 px-3' outline color='primary' size='sm'>
        Edit<i className='fal fa-cog ml-2' />
      </Button>} />;
  }

  getFilteredScopeQuestionGroup = (assurancePortfolio: AssurancePortfolio) => {
    return assuranceSurveyGroups(
      assurancePortfolio.universalTrackerValueAssurances,
      this.props.surveyGroups,
    );
  }

  renderDownloadButton(setting: AssuranceSettingsType) {
    return <SimpleTooltip
      text={setting.tooltip}
      component={
        <SubmitButton
          className='ml-2'
          size='sm'
          color='primary'
          onClick={async () => setting.handler()}
          outline={Object.hasOwn(setting, 'icon')}
          disabled={setting.isDisabled}
        >
          <>
            <i className={`${setting.icon} mr-2`} />
            {setting.text}
          </>
        </SubmitButton>
      }
    />
  }

  renderManageQuestionsButton(isCompleted: boolean, assurancePortfolio: AssurancePortfolio) {
    if (isCompleted) {
      return <></>;
    }

    if (!this.props.isManager) {
      return (
        <Button disabled={true} className='ml-2 px-3' outline color='primary' size='sm'>
          Manage {QUESTION.CAPITALIZED_PLURAL}<i className='fa fa-edit ml-2' />
        </Button>
      );
    }

    const questionTooltip = assurancePortfolio.universalTrackerValueAssurances.length > 0 ?
      `Add/Remove ${SURVEY.CAPITALIZED_SINGULAR} ${QUESTION.CAPITALIZED_PLURAL}` : `Add ${SURVEY.CAPITALIZED_SINGULAR} ${QUESTION.CAPITALIZED_PLURAL}`;

    return <SimpleTooltip
      id='toggle-question-modal'
      text={questionTooltip}
      component={<Button
        disabled={!this.props.isManager}
        onClick={() => this.toggleQuestionModal(assurancePortfolio)}
        className='ml-2 px-3' outline color='primary' size='sm'>
        Manage {QUESTION.CAPITALIZED_PLURAL}<i className='fa fa-edit ml-2' />
      </Button>} />;
  }
}

const mapStateToProps = (state: RootState) => {
  return {
    surveyGroups: getSurveyGroupsSelector(state),
    surveyState: state.survey,
    user: state.currentUser.loaded ? state.currentUser.data : undefined,
    isManager: isUserManagerByInitiativeId(state, state.survey.loaded ? state.survey.data.initiativeId : undefined),
    canAccessAssurance: FeaturePermissions.canAccessAssurance(state),
    utrvsCommentCount: utrvsCommentEndpoints.getUtrvsCommentCountBySurveyId.select(state.survey.loaded ? state.survey.data._id : '')(state as any),
  };
};

const mapDispatchToProps = {
  loadUNSDGMap,
  loadBlueprintMapping,
  loadScorecardsByInitiativeId,
  loadScorecardsBySurveyId,
  loadSurvey,
  reloadSurveyListSummary,
  addSiteAlert,
  getUtrvsCommentCount: utrvsCommentEndpoints.getUtrvsCommentCountBySurveyId.initiate,
};

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>

export default withRouter(connector(CompanyAssurancePortfolio));
