/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React from 'react';
import { useHistory } from 'react-router';
import { Button } from 'reactstrap';
import { ROUTES } from '../../constants/routes';
import { generateUrl } from '../../routes/util';
import { AssuranceListPortfolio } from '../../types/assurance';
import { DATE, formatDateUTC } from '../../utils/date';
import Dashboard, { DashboardSection, DashboardSectionTitle } from '../dashboard';
import { Loader } from '@g17eco/atoms/loader';
import { statusColumns } from '../../constants/assurance';
import { downloadPortfolioBundle } from '../../actions/assurance';
import { getAnalytics } from '../../services/analytics/AnalyticsService';
import { AnalyticsEvents } from '../../services/analytics/AnalyticsEvents';
import { naturalSort } from '../../utils';
import './assurance-list.scss';
import { useAppSelector } from '../../reducers';
import { getCurrentUser } from '../../selectors/user';
import {
  getAssurerName,
  getRowTooltip,
  getStatusList,
  hasDeletedSurvey,
  isCompleted,
  isMatchDelegationFilter,
  isMatchReportFilter,
  isMatchStatusFilter,
} from './utils';
import { AssuranceListFilters, Filters, ReportOption, StatusOption } from './AssuranceListFilters';
import { useToggle } from '@hooks/useToggle';
import {
  useGetAssuranceOrganizationMembersQuery,
  useGetAssurerPortfoliosQuery,
  useGetUserOrganizationQuery,
} from '../../apps/assurance/api/assurance';
import { AssuranceAction, hasAssurancePermission, hasOrganizationPermission } from './permissions-utils';
import { skipToken } from '@reduxjs/toolkit/query';
import { QueryError } from '@components/query/QueryError';
import { BasicAlert } from '@g17eco/molecules/alert';
import { Column, Divider, Row, TrackingList } from '@g17eco/molecules/tracking-list';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

const getStatusIcon = (assurancePortfolio: AssuranceListPortfolio, isDisabled: boolean) => {
  const icon = isCompleted(assurancePortfolio) ? 'fas fa-circle-check' : 'fal fa-circle';
  const color = isDisabled
    ? 'text-ThemeBgDisabled'
    : isCompleted(assurancePortfolio)
    ? 'text-ThemeSuccessMedium'
    : 'text-ThemeIconSecondary';
  return <i className={`${icon} ${color}`} />;
};

const getStatusTooltip = (assurancePortfolio: AssuranceListPortfolio) => {
  return isCompleted(assurancePortfolio) ? 'Assurance Completed' : 'Assurance Not Completed';
};

const getCompanyName = (assurancePortfolio: AssuranceListPortfolio, isDisabled: boolean) => {
  const { initiative, rootInitiative } = assurancePortfolio;
  const color = isDisabled ? 'text-ThemeBgDisabled' : 'text-ThemeTextMedium';
  const isRootOrg = initiative._id === rootInitiative._id;

  return (
    <>
      <div className='text-truncate'>{initiative.name}</div>
      {!isRootOrg ? <div className={`text-truncate text-uppercase ${color}`}>{rootInitiative.name}</div> : null}
    </>
  );
};

const defaultFilters: Filters = {
  delegation: [],
  report: ReportOption.ExcludeDeleted,
  status: StatusOption.All,
}

const AssuranceList = () => {
  const currentUser = useAppSelector(getCurrentUser);
  const [isCollapsed, toggleCollapse] = useToggle(true);
  const [filters, setFilters] = React.useState<Filters>(defaultFilters);

  const { data: organization, isFetching: isFetchingOrg, error: orgQueryError } = useGetUserOrganizationQuery();
  const {
    data: assuranceList,
    isFetching: isFetchingAssuranceList,
    error: portfoliosQueryError,
  } = useGetAssurerPortfoliosQuery();

  const {
    data,
    isFetching: isFetchingUsers,
    error: usersQueryError,
  } = useGetAssuranceOrganizationMembersQuery(
    organization?._id ? { organizationId: organization._id } : skipToken
  );

  const handleSetFilters = (newFilters: Partial<Filters>) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
  };

  const history = useHistory();
  const redirect = (assurancePortfolioId: string) => {
    history.push(generateUrl(ROUTES.ASSURANCE_PORTFOLIO, { assurancePortfolioId }));
  };

  const filteredAssuranceList = React.useMemo(() => {
    if (!assuranceList) {
      return [];
    }
    return assuranceList.filter(
      (assurancePortfolio) =>
        isMatchDelegationFilter({ assurancePortfolio, delegatedUserIds: filters.delegation }) &&
        isMatchReportFilter(assurancePortfolio, filters.report) &&
        isMatchStatusFilter(assurancePortfolio, filters.status)
    );
  }, [assuranceList, filters]);

  const downloadEvidence = (portfolio: AssuranceListPortfolio) => {
    downloadPortfolioBundle(portfolio._id)
      .then(async ([document]) => {
        if (document?.url) {
          window.open(document.url, '_blank', '');
          const analytics = getAnalytics();
          return analytics.track(AnalyticsEvents.AssuranceDataDownloaded, {
            assurancePortolioId: portfolio._id,
            surveyId: portfolio.survey._id,
            initiativeId: portfolio.initiative._id,
            source: 'survey_assurance',
          });
        }
      })
      .catch((e: Error) => console.log(e));
  };

  if (
    isFetchingOrg ||
    isFetchingUsers ||
    isFetchingAssuranceList ||
    !organization ||
    !currentUser
  ) {
    return <Loader />;
  }

  const years = new Set();
  filteredAssuranceList.forEach((a) => {
    years.add(formatDateUTC(a.survey.effectiveDate, DATE.YEAR_ONLY));
  });

  const getDownloadButton = (assurancePortfolio: AssuranceListPortfolio, isDisabled: boolean) => {
    return (
      <Button
        color='link'
        disabled={isDisabled}
        onClick={(e) => {
          e.stopPropagation();
          downloadEvidence(assurancePortfolio);
        }}
      >
        <i className='fa fa-file-download' />
      </Button>
    );
  };

  const goToManageUsers = () => history.push(generateUrl(ROUTES.ASSURANCE_MANAGE_USERS));

  const canManage = hasOrganizationPermission({
    user: currentUser,
    action: AssuranceAction.CanManage,
    organization,
  });

  return (
    <Dashboard className='assurance-list'>
      <>
        {portfoliosQueryError ? <QueryError error={portfoliosQueryError} type={'danger'} /> : null}
        {orgQueryError ? <QueryError error={orgQueryError} type={'danger'} /> : null}
        {usersQueryError ? <QueryError error={usersQueryError} type={'danger'} /> : null}
        <DashboardSectionTitle
          className='assurance-list__heading'
          title={organization.name}
          buttons={[
            <Button key={'filter__button'} className='ml-2 px-3' onClick={toggleCollapse} color='link-secondary'>
              Advance filters
              <i className={`${isCollapsed ? 'fal' : 'fas'} fa-filter ml-2`} />
            </Button>,
            <Button
              key={'manage-users__button'}
              className='ml-2 px-3'
              disabled={!canManage}
              onClick={goToManageUsers}
              outline={true}
            >
              <i className={'fal fa-users-gear mr-2'} />
              Manage assurance team
            </Button>,
          ]}
        />
        {isCollapsed ? null : (
          <AssuranceListFilters
            users={data?.users || []}
            currentUser={currentUser}
            assuranceList={assuranceList}
            filters={filters}
            handleSetFilters={handleSetFilters}
          />
        )}
        {filteredAssuranceList.length === 0 ? (
          <DashboardSection title='Reports'>
            <BasicAlert type='info'>No reports match your criteria</BasicAlert>
          </DashboardSection>
        ) : (
          Array.from(years)
            .sort()
            .reverse()
            .map((year) => (
              <DashboardSection key={year as number} title={`${year} Reports`}>
                <TrackingList border={false}>
                  <Row className='header'>
                    <Column className='col_status strong' />
                    <Column className='col_date strong'>Month</Column>
                    <Column className='col_org strong'>Company</Column>
                    <Column className='col_assurer strong' fill>
                      Assurance admin
                    </Column>
                    {Object.values(statusColumns).map((column) => (
                      <Column key={column.name} className='col_stats'>
                        <SimpleTooltip text={column.text}>
                          <i className={`${column.name} fa-lg`} />
                        </SimpleTooltip>
                      </Column>
                    ))}
                    <Column className='col_download' />
                  </Row>
                  {filteredAssuranceList
                    .filter((d) => formatDateUTC(d.survey.effectiveDate, DATE.YEAR_ONLY) === year)
                    .sort((a, b) => naturalSort(b.survey.effectiveDate, a.survey.effectiveDate))
                    .map((d) => {
                      const statusList = getStatusList(d);
                      const hasAccess = hasAssurancePermission({
                        user: currentUser,
                        action: AssuranceAction.CanViewPortfolio,
                        organization,
                        assurancePortfolio: d,
                      });
                      const isDisabled = !hasAccess || hasDeletedSurvey(d);
                      return (
                        <SimpleTooltip key={`assurance_row_${d._id}`} text={getRowTooltip(d, hasAccess)}>
                          <Row
                            className={`dont_translate ${isDisabled ? 'disabled' : 'enabled'}`}
                            onClick={isDisabled ? undefined : () => redirect(d._id)}
                          >
                            <Column className='col_status' tooltip={getStatusTooltip(d)}>
                              {getStatusIcon(d, isDisabled)}
                            </Column>
                            <Column className='col_date'>
                              {formatDateUTC(d.survey.effectiveDate, DATE.MONTH_ONLY)}
                            </Column>
                            <Column stretch className='col_org'>
                              {getCompanyName(d, isDisabled)}
                            </Column>
                            <Column stretch truncate className='col_assurer'>
                              {getAssurerName(d)}
                            </Column>
                            <Divider />
                            {Object.values(statusColumns).map((column) => (
                              <Column key={column.accessor} className='col_stats'>
                                {statusList[column.accessor as keyof typeof statusList].length || '0'}
                              </Column>
                            ))}
                            <Divider />
                            <Column className='col_download'>{getDownloadButton(d, isDisabled)}</Column>
                          </Row>
                        </SimpleTooltip>
                      );
                    })}
                </TrackingList>
              </DashboardSection>
            ))
        )}
      </>
    </Dashboard>
  );
};

export default AssuranceList;
