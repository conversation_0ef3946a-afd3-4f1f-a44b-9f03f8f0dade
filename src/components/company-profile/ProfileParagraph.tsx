import { useRef, useState } from 'react';
import { Button } from 'reactstrap';
import { companyEditOptions } from '../company-settings/EditOption';
import { useTextCollapse } from '@hooks/useTextCollapse';
import { InitiativePlain } from '../../types/initiative';

type InitiativeProfile = Pick<InitiativePlain, 'description' | 'missionStatement'>;

interface ProfileParagraphProps {
  initiative: InitiativeProfile;
  field: keyof InitiativeProfile;
}
export const ProfileParagraph = (props: ProfileParagraphProps) => {
  const { initiative, field } = props;
  const [isHidden, setHidden] = useState(true);
  const contentRef = useRef<HTMLDivElement | null>(null);

  const { height, className, isTruncated } = useTextCollapse({
    elementRef: contentRef,
    defaultHeight: 55,
    isHidden,
  });
  const HiddenBtn = () => {
    const text = isHidden ? 'Read more' : 'Read less';
    const icon = isHidden ? 'fa-angle-down' : 'fa-angle-up';
    return (
      <Button color='link-secondary' onClick={() => setHidden((prev) => !prev)}>
        <div className='text-sm py-1'>
          <i className={`fa ${icon} mr-1 text-sm`} />
          {text}
        </div>
      </Button>
    );
  };

  return initiative[field] ? (
    <>
      <div className='mt-4 mb-1 text-label text-medium'>
        {companyEditOptions.find((option) => option.accessor === field)?.label.toUpperCase()}
      </div>
      <div ref={contentRef} className={`${field} ${className}`} style={{ height }}>
        {initiative[field]}
      </div>
      {isTruncated ? <HiddenBtn /> : null}
    </>
  ) : null
};
