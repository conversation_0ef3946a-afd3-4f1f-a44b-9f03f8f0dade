/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';
import { DashboardSectionCollapsible } from '../dashboard';
import EditButton from '../button/EditButton';
import './style.scss';
import { generateUrl } from '../../routes/util';
import { ROUTES } from '../../constants/routes';
import { ProfileParagraph } from './ProfileParagraph';
import { Avatar } from '@g17eco/atoms/avatar';
import { SafeInitiativeFields } from '../../types/initiative';

interface CompanyProfileProps {
  initiative: SafeInitiativeFields | undefined;
  readOnly: boolean;
}

export const CompanyProfile = ({ readOnly, initiative }: CompanyProfileProps) => {
  const history = useHistory();
  const [isCollapsed, setCollapsed] = useState(true);

  if (!initiative) {
    return <></>;
  }

  const toggleCollapse = () => setCollapsed((prevState) => !prevState);

  const getTitle = () => {
    const color = !isCollapsed ? 'text-ThemeTextMedium' : '';

    if (isCollapsed && !initiative.profile) {
      return <div className='h5'>{initiative.name}</div>;
    }

    return (
      <div className={`${color} d-flex align-items-center`}>
        <div className='company-logo__wrapper mr-3'>
          {initiative.profile ? (
            <Avatar width='40px'>
              <img alt='avatar' src={initiative.profile} />
            </Avatar>
          ) : null}
        </div>

        <div className={`h5 ${color}`}>{initiative.name}</div>
      </div>
    );
  };

  return (
    <DashboardSectionCollapsible
      className='summary-info-panel'
      title={getTitle()}
      toggleCollapse={toggleCollapse}
      isCollapsed={isCollapsed}
      buttons={
        !readOnly
          ? [
            <EditButton
              key='account-settings'
              onClick={(e) => history.push(generateUrl(ROUTES.ACCOUNT_SETTINGS, { initiativeId: initiative._id }))}
            />,
          ]
          : []
      }
    >
      <div className='row text-ThemeTextMedium summary-content__wrapper pb-4'>
        <div className='col-12 col-sm-11'>
          {initiative.sectorText && (
            <>
              <div className='mt-2 mb-1 text-label text-medium'>INDUSTRY: {initiative.sectorText}</div>
            </>
          )}
          {initiative.industryText && (
            <>
              <div className='mt-2 mb-1 text-label text-medium'>SECTOR: {initiative.industryText}</div>
            </>
          )}

          {initiative.description && (
            <ProfileParagraph initiative={initiative} field={'description'} isCollapsed={isCollapsed} />
          )}

          {initiative.missionStatement && (
            <ProfileParagraph initiative={initiative} field={'missionStatement'} isCollapsed={isCollapsed} />
          )}

        </div>
      </div>
    </DashboardSectionCollapsible>
  );
};
