import { ReactElement } from 'react';
import Dashboard, { DashboardRow, DashboardSection } from '../dashboard';
import { NavTab, Navigation } from './Navigation';
import './style.scss';
import { InitiativeData } from '@g17eco/types/initiative';
import { useHistory } from 'react-router-dom';
import { generateUrl } from '@routes/util';
import { RouteInterfaceMin } from '@g17eco/types/routes';
import { AdminBreadcrumbsProps } from '@g17eco/molecules/breadcrumbs';

interface ContainerProps {
  children: ReactElement;
  initiative: InitiativeData;
  BreadCrumbsComponent: (props: AdminBreadcrumbsProps) => JSX.Element;
  page: string;
  navTabs: NavTab[];
  settingRoute: RouteInterfaceMin;
}

export const Container = ({
  initiative,
  BreadCrumbsComponent,
  page,
  navTabs,
  settingRoute,
  children,
}: ContainerProps) => {
  const history = useHistory();
  const onClick = (code: string) => {
    history.push({
      pathname: generateUrl(settingRoute, { initiativeId: initiative._id, page: code }),
      search: history.location.search,
    });
  };

  return (
    <Dashboard className='company-settings__wrapper'>
      <DashboardRow>
        <BreadCrumbsComponent breadcrumbs={[{ label: 'Company Settings' }]} initiativeId={initiative._id} />
      </DashboardRow>
      <DashboardRow>
        <h3 className='pl-2'>Company settings</h3>
      </DashboardRow>
      <DashboardSection>
        <Navigation navTabs={navTabs} page={page} onClick={onClick} />
        <div className='mt-4'>{children}</div>
      </DashboardSection>
    </Dashboard>
  );
};
