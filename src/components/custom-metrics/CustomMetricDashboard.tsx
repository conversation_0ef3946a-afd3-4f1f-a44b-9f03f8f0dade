/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { useContext, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { deleteMetricGroup } from '../../actions/initiative';
import { generateUrl } from '../../routes/util';
import { Sort, sortItems } from '../../utils/sort';
import { DashboardSection, DashboardSectionTitle } from '../dashboard';
import { Loader } from '@g17eco/atoms/loader';
import CardGrid, { CardGridItem, CardGridItemProps, CardGridViewMode } from '../survey-scope/CardGrid';
import { SearchBox } from '../survey-settings-toolbar/SearchBox';
import { CustomMetricContext, getBaseRoute } from './CustomMetricContainer';
import { CustomMetricsDashboardToolbar } from './partials/CustomMetricDashboardToolbar';
import { CustomMetricCardProps, getCustomMetricGroups, MetricGroupsProps } from './CustomMetricGroups';
import './styles.scss';
import { DuplicateMetricGroupModal, useDuplicateMetricGroup } from './DuplicateMetricGroupModal';
import { Button } from 'reactstrap';
import { CustomMetricsViewMode } from './constants';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';

interface CustomMetricDashboardProps {
  handleReload: () => Promise<void>;
}

const CustomMetricDashboard = ({ handleReload }: CustomMetricDashboardProps) => {
  const { initiativeId, isPortfolioTracker } = useContext(CustomMetricContext);
  const [saving, setSaving] = useState(false);
  const [currentSort, setSort] = useState(Sort.TitleAsc);
  const [filterText, setFilterText] = useState('');
  const [errorMessage, setMessage] = useState('');
  const history = useHistory();
  const baseRoute = getBaseRoute(isPortfolioTracker);

  const {
    metricGroups,
    viewLayout = CardGridViewMode.gallery,
    groupBy,
  } = useContext(CustomMetricContext);

  const { isDuplicating, stopDuplicating, metricGroup, setMetricGroup } = useDuplicateMetricGroup();

  const completeUpdate = async (error?: string) => {
    await handleReload();
    setSaving(false);
    if (error) {
      setMessage(error);
    }
  };

  const handleCreateMetricGroup = () => {
    const url = generateUrl(baseRoute, {
      initiativeId,
      portfolioId: initiativeId,
      groupId: CustomMetricsViewMode.Create,
    });
    history.push(url);
  };

  const handleDeleteMetricGroup = async (metricGroupId: string) => {
    if (!metricGroupId) {
      return;
    }
    const actionConfirmation = window.confirm(
      'Are you sure you want to delete this group? No Metrics will be deleted during this action'
    );
    if (actionConfirmation) {
      try {
        setSaving(true);
        await deleteMetricGroup(initiativeId, metricGroupId);
        await completeUpdate();
      } catch (e) {
        await completeUpdate(e.message);
      }
    }
  };

  const handleDuplicateMetricGroup = async (metricGroupId: string) => {
    if (!metricGroupId) {
      return;
    }

    setMetricGroup(metricGroups.find((m) => m._id === metricGroupId));
  };

  const onDuplicated = async () => {
    await handleReload();
  };

    const handleEditMetricGroup = (groupId: string) => {
      const url = generateUrl(baseRoute, { initiativeId, portfolioId: initiativeId, groupId });
      history.push(url);
    };

    const cardGroupProps: MetricGroupsProps = {
      metricGroups,
      handleCreateMetricGroup,
      handleDeleteMetricGroup,
      handleDuplicateMetricGroup,
      handleEditMetricGroup,
    };

  const customMetricCards = getCustomMetricGroups(cardGroupProps);

  const isMatched = (sortTitle: string, searchTerm: string) =>
    sortTitle.toLowerCase().includes(searchTerm.toLowerCase());

  const cardFilter = (card: CustomMetricCardProps) => {
    if (groupBy !== card.accessType || (filterText && !isMatched(card.sortTitle, filterText))) {
      return false;
    }
    return true;
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    return setFilterText(e.target.value);
  };

  return (
    <>
      <DashboardSectionTitle
        title='Custom metrics'
        buttons={[
          <Button
            key='manage-custom-metrics'
            onClick={() =>
              history.push(
                generateUrl(baseRoute, {
                  initiativeId,
                  portfolioId: initiativeId,
                  groupId: CustomMetricsViewMode.Manage,
                })
              )
            }
          >
            Manage/Buy custom metrics
          </Button>,
        ]}
      />
      <DashboardSection icon='fal fa-file-circle-question'>
        {saving ? <Loader /> : null}
        <LoadingPlaceholder count={1} height={39} isLoading={false}>
          <CustomMetricsDashboardToolbar currentSort={currentSort} handleSort={setSort}>
            <div className='mr-2'>
              <SearchBox handleOnChange={handleSearch} value={filterText} />
            </div>
          </CustomMetricsDashboardToolbar>
        </LoadingPlaceholder>
        <LoadingPlaceholder className='mt-3' count={1} height={115} isLoading={false}>
          {errorMessage && <div className='alert alert-danger'>{errorMessage}</div>}
          <div className='mt-3'>
            <CardGrid viewLayout={viewLayout}>
              {sortItems(customMetricCards, currentSort)
                .filter(cardFilter)
                .map(({ key, ...cardProps }: CardGridItemProps) => {
                  return <CardGridItem key={key} {...cardProps} />;
                })}
            </CardGrid>
          </div>
        </LoadingPlaceholder>
      </DashboardSection>

      <DuplicateMetricGroupModal
        isOpen={isDuplicating}
        isPortfolioTracker={isPortfolioTracker}
        toggle={stopDuplicating}
        metricGroup={metricGroup}
        onDuplicated={onDuplicated}
        metricGroups={metricGroups}
      />
    </>
  );
};

export default CustomMetricDashboard;
