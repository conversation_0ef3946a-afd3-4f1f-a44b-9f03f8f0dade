/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import G17Client from '../../services/G17Client';
import { Button, FormGroup } from 'reactstrap';
import FileZone from '../profile/FileZone';
import { useSelector } from 'react-redux';
import { isStaff } from '../../selectors/user';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { DashboardSection, DashboardSectionTitle } from '../dashboard';
import NotAuthorised from '../../routes/not-authorised';
import { useSiteAlert } from '../../hooks/useSiteAlert';
import { BasicAlert } from '@g17eco/molecules/alert';
import { ExtraFeature, FeatureStability } from '@g17eco/molecules/feature-stability';

interface Props {
  initiativeId: string;
  onCancel: () => void;
  onComplete: () => void;
}

export const CustomMetricImportExport = ({ initiativeId, onCancel, onComplete }: Props) => {

  const [isLoading, setLoading] = useState(false);
  const [message, setMessage] = useState<string>('');
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const isUserStaff = useSelector(isStaff);
  const { addSiteAlert } = useSiteAlert();

  if (!isUserStaff) {
    return <NotAuthorised />
  }

  const clear = () => {
    setMessage('');
    setUploadFile(null);
  }

  const handleAddFile = async (files: File[]) => {
    setMessage('');
    setUploadFile(files[0]);
  }

  const handleExportClick = async () => {
    return G17Client.exportCustomMetrics(initiativeId)
      .catch(e =>
        setMessage(e.message)
      );
  }

  const canSubmit = !isLoading && uploadFile;

  const handleImportClick = async () => {
    setLoading(true);
    setMessage('');
    if (!uploadFile) {
      return;
    }

    try {
      await G17Client.importCustomMetrics(initiativeId, uploadFile);
      setUploadFile(null);
      addSiteAlert({
        content: 'Successfully imported Custom Metrics'
      });
      onComplete();
    } catch (e) {
      setMessage(e.message);
    }
    setLoading(false);
  }

  return (
    <div className='company-import-container'>
      <DashboardSectionTitle
        title={
          <span>
            Manage custom metrics <FeatureStability feature={ExtraFeature.CustomMetricImports} />
          </span>
        }
        buttons={[<Button key='custom-metrics-list' onClick={onCancel}>Custom metrics list</Button>]}
      />
      {isLoading ? <BlockingLoader /> : null}

      <DashboardSection headingStyle={3} title={'Export'} subtitle={'Download a file containing the metrics.'}>
        <div className='text-right'>
          <Button color='link-secondary' className='mr-3' onClick={() => onCancel()}>
            cancel
          </Button>
          <Button color='primary' onClick={handleExportClick}>
            Download export file
          </Button>
        </div>
      </DashboardSection>

      <DashboardSection headingStyle={3} title={'Import'} subtitle={'Upload a file with the metrics.'}>
        <BasicAlert className={'my-2'} type='warning'>
          {message}
        </BasicAlert>
        {uploadFile ? (
          <div className='text-ThemeSuccessMedium d-flex align-items-baseline'>
            <i className='fa fa-check-circle text-ThemeSuccessMedium mr-3' />
            Done{' '}
            <Button color='link' onClick={clear}>
              (upload again)
            </Button>
          </div>
        ) : (
          <>
            <FormGroup>
              <FileZone
                accept={{
                  'application/g17': ['.g17'],
                }}
                disabled={isLoading}
                handleFilesAdded={handleAddFile}
                dropText={() => <span>Drop a file here or click to browse for a file.</span>}
              />
            </FormGroup>
          </>
        )}

        <div className='mt-4 text-right'>
          <Button color='link-secondary' className='mr-3' onClick={() => onCancel()}>
            cancel
          </Button>
          <Button color='primary' disabled={!canSubmit} onClick={handleImportClick}>
            Upload import file
          </Button>
        </div>
      </DashboardSection>
    </div>
  );
}
