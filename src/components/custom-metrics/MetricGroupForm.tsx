/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { useContext, useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { CustomMetricRouteParams } from '@features/custom-metrics';
import { CustomMetricsUsage, MetricGroup } from '../../types/metricGroup';
import { DashboardSection } from '../dashboard';
import { ViewMode } from './constants';
import { CustomMetricContext, getUrls } from './CustomMetricContainer';
import './styles.scss';
import { Menu } from '../menu/Menu';
import { PackAssignmentTable } from './PackAssignmentTable';
import { MetricGroupConfiguration } from './MetricGroupConfiguration';
import { QuestionsAssignmentTable } from './QuestionsAssignmentTable';
import { PACK, QUESTION } from '@constants/terminology';
import { MetricGroupImport } from './MetricGroupImport';
import { useAppSelector } from '../../reducers';
import { isStaff } from '../../selectors/user';
import { ExtraFeature, FeatureStability } from '@g17eco/molecules/feature-stability';

export interface FormProps {
  metricGroup?: MetricGroup;
  customMetricsUsage: CustomMetricsUsage;
  handleReload: () => Promise<void>;
  handleCancel?: () => void;
}

interface MetricGroupFormProps extends FormProps {
  readOnly: boolean;
  canEdit: boolean;
  allowCancel?: boolean;
  handleChangeMetricGroup: (params: CustomMetricRouteParams) => void;
}

const MetricGroupForm = (props: MetricGroupFormProps) => {
  const { readOnly, canEdit, allowCancel, metricGroup, customMetricsUsage, handleReload, handleChangeMetricGroup } = props;
  const { initiativeId, isPortfolioTracker, surveyId, view } = useContext(CustomMetricContext);

  const history = useHistory();
  const isUserStaff = useAppSelector(isStaff);
  const [mode, setMode] = useState<ViewMode>(ViewMode.Edit);
  const urls = getUrls({ isPortfolioTracker, initiativeId, groupId: metricGroup?._id, surveyId });

  useEffect(() => {
    if (view) {
      setMode(view as ViewMode);
    }
  }, [view]);

  const handleCancelImport = () => {
    history.push(urls.assignQuestions);
  };

  const handleCompleteImport = async () => {
    await handleReload();
    handleCancelImport();
  };

  const renderView = (view: ViewMode) => {
    switch (view) {
      case ViewMode.Edit:
        return (
          <MetricGroupConfiguration
            metricGroup={metricGroup}
            handleChangeMetricGroup={handleChangeMetricGroup}
            handleReload={handleReload}
            readOnly={readOnly}
            allowCancel={allowCancel}
            editUrl={urls.edit}
          />
        );
      case ViewMode.AssignQuestions:
      case ViewMode.EditQuestion:
        if (!metricGroup) {
          return null;
        }
        return (
          <QuestionsAssignmentTable
            editQuestionUrl={urls.editQuestion}
            assignQuestionsUrl={urls.assignQuestions}
            canEdit={canEdit}
            readOnly={readOnly}
            customMetricsUsage={customMetricsUsage}
            handleReload={handleReload}
            metricGroup={metricGroup}
          />
        );
      case ViewMode.AssignPack:
        return <PackAssignmentTable metricGroup={metricGroup} />;
      case ViewMode.Import:
        if (!metricGroup) {
          return null;
        }
        return <MetricGroupImport
          metricGroup={metricGroup}
          onComplete={handleCompleteImport}
          onCancel={() => handleCancelImport()}
        />
      default:
        return null;
    }
  };

  return (
    <DashboardSection
      icon='fal fa-file-circle-question'
      title={
        <>
          {metricGroup?.groupName}
          <FeatureStability feature={ExtraFeature.CustomMetricImports} />
        </>
      }
    >
      <Menu
        className='mb-4'
        items={[
          {
            active: mode === ViewMode.Edit,
            onClick: () => history.push(urls.edit),
            label: 'Configuration',
          },
          {
            active: mode === ViewMode.AssignQuestions,
            disabled: !metricGroup?._id,
            onClick: () => history.push(urls.assignQuestions),
            label: `Assign ${QUESTION.PLURAL} to ${PACK.SINGULAR}`,
          },
          {
            active: mode === ViewMode.AssignPack,
            disabled: !metricGroup?._id,
            onClick: () => history.push(urls.assignPack),
            label: `Assign ${PACK.SINGULAR} to companies`,
            hidden: !isPortfolioTracker || !metricGroup?._id,
          },
          {
            active: mode === ViewMode.Import,
            disabled: !metricGroup?._id || readOnly,
            onClick: () => history.push(urls.import),
            label: (
              <>
                <FeatureStability className='mr-2' feature={ExtraFeature.CustomMetricImports} />
                Import / Export {QUESTION.PLURAL}
              </>
            ),
            hidden: !isUserStaff,
          },
        ]}
      />
      {renderView(mode)}
    </DashboardSection>
  );
};
export default MetricGroupForm;
