/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React, { useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import { Button } from 'reactstrap';
import Dashboard, { DashboardRow } from '../../components/dashboard';
import { ROUTES } from '../../constants/routes';
import { AdminBreadcrumbs } from '../../routes/admin-dashboard/AdminBreadcrumbs';
import { CustomMetricRouteParams } from '@features/custom-metrics';
import { generateUrl } from '../../routes/util';
import { AccessType, MetricGroup } from '../../types/metricGroup';
import { getGroupLabel } from '../../utils/metricGroup';
import { CustomMetricsViewMode, ViewMode } from './constants';
import { CustomMetricContext, getBaseRoute } from './CustomMetricContainer';
import CustomMetricDashboard from './CustomMetricDashboard';
import { CustomMetricManage } from './CustomMetricManage';
import { MetricGroupFormContainer } from './MetricGroupFormContainer';
import './styles.scss';
import { PACK, SURVEY } from '@constants/terminology';
import { hasHitQuestionLimit } from './utils';
import { BasicAlert } from '@g17eco/molecules/alert';

interface MetricGroupOption {
  value: string;
  label: string;
}

interface CustomMetricProps {
  handleReload: (firstRun?: boolean) => Promise<void>;
  handleChangeMetricGroup: (params: CustomMetricRouteParams) => void;
}

const CustomMetrics = (props: CustomMetricProps) => {
  const { metricGroups, groupId, isPortfolioTracker, initiativeId, view, surveyId, customMetricsUsage } =
    React.useContext(CustomMetricContext);

  const history = useHistory();
  const isDashboardMode = groupId === CustomMetricsViewMode.Dashboard;
  const isNewGroupMode = groupId === CustomMetricsViewMode.Create;
  const isManageMode = groupId === CustomMetricsViewMode.Manage;

  const metricGroupOptions: MetricGroupOption[] = [];
  const assignedMetricGroupOptions: MetricGroupOption[] = [];

  metricGroups.forEach((g: MetricGroup) => {
    const item = { value: g._id ?? '', label: getGroupLabel(g) };
    if (g.accessType === AccessType.Assigned) {
      assignedMetricGroupOptions.push(item);
    } else {
      metricGroupOptions.push(item);
    }
  });

  const handleReload = async () => {
    await props.handleReload();
  };

  const currentMetricGroup = useMemo(() => {
    if (!isNewGroupMode) {
      return metricGroups.find((g) => g._id === groupId);
    }
  }, [isNewGroupMode, metricGroups, groupId]);

  const isAssigned = assignedMetricGroupOptions.some((o) => o.value === groupId);
  const baseRoute = getBaseRoute(isPortfolioTracker);

  const handleClickManageCustomMetricUrl = () => {
    const url = generateUrl(baseRoute, {
      initiativeId,
      groupId: CustomMetricsViewMode.Manage,
      ...(isPortfolioTracker && { portfolioId: initiativeId }),
    });
    history.push(url);
  };

  const showAlert = () => {
    if (hasHitQuestionLimit(customMetricsUsage)) {
      return (
        <BasicAlert type='warning' className='d-flex align-items-baseline text-ThemeWarningExtradark'>
          <span>{`${customMetricsUsage.organisationLimit} custom metric limit reached - you can delete some metrics or buy more here:`}</span>
          <Button
            color='link text-ThemeWarningExtradark'
            className='text-decoration-underline px-1 py-0'
            onClick={handleClickManageCustomMetricUrl}
          >
            Manage custom metrics
          </Button>
        </BasicAlert>
      );
    }
    return <></>;
  };

  const goToSurvey = () => {
    if (!surveyId) {
      return;
    }
    const url = generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, { initiativeId, surveyId, page: 'scope' });
    history.push(url);
  };

  const renderBreadcrumbs = () => {

    const breadcrumbs = [
      {
        label: 'Custom Metrics',
        url: generateUrl(baseRoute, {
          initiativeId,
          groupId: CustomMetricsViewMode.Dashboard,
          ...(isPortfolioTracker && { portfolioId: initiativeId }),
        }),
      },
    ];
    if (isNewGroupMode) {
      breadcrumbs.push({
        label: `Create custom metric ${PACK.SINGULAR}`,
        url: '',
      });
    }
    if (isManageMode) {
      breadcrumbs.push({
        label: 'Manage custom metrics',
        url: '',
      });
    }

    if (groupId && !isNewGroupMode && !isDashboardMode && !isManageMode) {
      const url = generateUrl(baseRoute, {
        initiativeId,
        groupId,
        ...(isPortfolioTracker && { portfolioId: initiativeId }),
        view: ViewMode.AssignQuestions,
      });
      breadcrumbs.push({ label: currentMetricGroup?.groupName ?? '', url });
    }
    if (view === ViewMode.Import) {
      const url = generateUrl(ROUTES.CUSTOM_METRICS, {
        initiativeId,
        groupId,
        view: ViewMode.Import,
      });
      breadcrumbs.push({ label: 'Import or Export', url });
    }
    return (
      <>
        {surveyId ? (
          <div className='w-100 ml-3 mb-4'>
            <Button color='link' className='px-2 pt-2' onClick={goToSurvey}>
              <i className='fa fa-arrow-circle-left mr-2' />
              Go back to {SURVEY.SINGULAR} scope
            </Button>
          </div>
        ) : null}
        <DashboardRow>
          <AdminBreadcrumbs breadcrumbs={breadcrumbs} initiativeId={initiativeId} isPortfolioTracker={isPortfolioTracker} />
        </DashboardRow>
      </>
    );
  };

  const renderView = () => {
    if (isManageMode) {
      return (
        <CustomMetricManage
          initiativeId={initiativeId}
          customMetricsUsage={customMetricsUsage}
          handleReload={handleReload}
        />
      );
    }
    if (isDashboardMode) {
      return <CustomMetricDashboard handleReload={handleReload} />;
    }

    return (
      <MetricGroupFormContainer
        isAssigned={isAssigned}
        currentMetricGroup={currentMetricGroup}
        customMetricsUsage={customMetricsUsage}
        handleReload={handleReload}
        handleChangeMetricGroup={props.handleChangeMetricGroup}
      />
    );
  };
  return (
    <Dashboard className='custom-metric-container'>
      {showAlert()}
      {renderBreadcrumbs()}
      {renderView()}
    </Dashboard>
  );
};

export default CustomMetrics;
