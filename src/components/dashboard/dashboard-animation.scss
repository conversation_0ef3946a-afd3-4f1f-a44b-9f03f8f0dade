

@mixin dashboardAnimation {
  max-width: $max-width-container;
  padding: 0rem 0.1rem;
  width: 100%;
  transition-duration: 500ms;

  &.open,
  &.closed {
    .whiteBoxContainer>div {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
  }

  &.open {
    .collapse-button {
      >i {
        animation: 200ms linear 0s 1 rotate180 normal forwards;
      }
    }

    .whiteBoxContainer {
      transition: all 1s;

      .dashboard-children,
      .subtitle-container {
        max-height: 100vh; // Used for animation initial frame
        animation: 500ms linear 0s 1 slideOpen normal forwards;
      }
    }
  }

  &.closed {
    .collapse-button {
      >i {
        animation: 200ms linear 0s 1 rotate180 reverse forwards;
      }
    }

    .whiteBoxContainer {
      transition: all 1s;

      .dashboard-children,
      .subtitle-container {
        animation: 500ms ease-in 0s 1 sliceClose normal forwards;
      }
    }
  }

  .whiteBoxContainer {
    min-height: 50px;
  }

  .collapse-button {
    margin-left: 0.5rem;
  }
}
