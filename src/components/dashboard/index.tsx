/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { ReactNode } from 'react';
import IconButton from '../button/IconButton';
import './style.scss';

export interface DashboardProps {
  id?: string;
  testId?: string;
  className?: string;
  children: ReactNode;
  hasSidebar?: boolean;
  sidebarPosition?: 'left' | 'right';
}

export interface DashboardSectionButton {
  icon: string;
  testId?: string;
  tooltip: string;
  disabled?: boolean;
  active?: boolean;
  outline?: boolean;
  className?: string;
  onClick: () => void;
}

export type BootstrapSteps = 0 | 1 | 2 | 3 | 4 | 5;
type HTagSteps = 1 | 2 | 3 | 4 | 5 | 6;

export interface DashboardSectionProps {
  children?: ReactNode;
  isVisible?: boolean;
  title?: string | JSX.Element;
  icon?: string;
  subtitle?: string | JSX.Element;
  className?: string;
  buttons?: (DashboardSectionButton | JSX.Element | null)[];
  handleClick?: () => void;
  padding?: BootstrapSteps;
  paddingInternal?: number;
  mb?: number;
  headingStyle?: HTagSteps;
  titleClass?: string;
  header?: JSX.Element | null;
  classes?: Record<string, string | undefined>;
}

type DashboardSectionCollapsibleProps = Omit<DashboardSectionProps, 'subtitle'> & {
  toggleCollapse: () => void;
  isCollapsed: boolean;
};

const ActionButtons = ({ buttons }: { buttons?: (DashboardSectionButton | JSX.Element | null)[] }) => {
  if (!buttons) {
    return null;
  }
  return (
    <div className='action-buttons text-nowrap d-flex align-items-center'>
      {buttons.map((button, i) => {
        if (!button) {
          return null;
        }
        if ('icon' in button) {
          return (
            <IconButton
              data-testid={button.testId}
              key={`${button.icon}-${i}`}
              icon={button.icon}
              outline={button.outline !== false}
              disabled={!!button.disabled}
              tooltip={button.tooltip}
              className={`ml-2 ${button.className ?? ''}`}
              active={!!button.active}
              onClick={button.onClick}
            />
          )
        }
        return <React.Fragment key={`btn-${button.key}-${i}`}>{button}</React.Fragment>;
      })
      }
    </div>
  );
}

export const DashboardSectionTitle = (
  props: Pick<DashboardSectionProps, 'title' | 'buttons' | 'subtitle' | 'headingStyle' | 'className'>
) => {
  return (
    <DashboardRow
      title={props.title}
      subtitle={props.subtitle}
      buttons={props.buttons}
      headingStyle={props.headingStyle ?? 3}
      className={props.className ?? ''}
    />
  );
};

const DashboardSectionHeader = (props: DashboardSectionProps & { onClick?: () => void }) => {
  const { title, icon, buttons, headingStyle = 5, titleClass = 'flex-fill', onClick } = props;
  const titleStyles = `h${headingStyle} ${titleClass}`;

  const handleClick = onClick ? (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.preventDefault();
    onClick();
  } : undefined

  return (
    <div className={'d-flex justify-content-between align-items-center'}>
      {title ? (
        <div className={titleStyles} style={onClick ? { cursor: 'pointer' } : undefined} onClick={handleClick}>
          {icon ? <i className={`fa ${icon} mr-2`} /> : null}
          {title}
        </div>
      ) : null}
      <ActionButtons buttons={buttons} />
    </div>
  );
};

export const DashboardSection = (props: DashboardSectionProps & { onClick?: () => void }) => {
  const { children, title, subtitle, isVisible, className, padding = 4, paddingInternal = 3, mb = 3, header, classes = {} } = props;

  if (isVisible === false) {
    return null;
  }

  return <div className={`dashboard-section mb-${mb} ${className ?? ''}`}>
    <div className={`whiteBoxContainer p-${padding} ${classes.whiteBoxContainer || ''}`}>
      <div className={`p-${paddingInternal} d-flex flex-column ${classes.whiteBoxWrapper || ''}`}>
        {header ?? <DashboardSectionHeader {...props} />}
        {subtitle && (
          <div className='mt-1 subtitle-container'>
            {subtitle}
          </div>
        )}
        {children ? (
          <div className={`mt-1 dashboard-children ${title && children ? `mt-${padding > 3 ? padding - 1 : padding}` : ''}`}>
            {children}
          </div>
        ) : null}
      </div>
    </div>
  </div>;
};

export const DashboardRow = (props: Pick<DashboardSectionProps, 'children' | 'title' | 'subtitle' | 'isVisible' | 'className' | 'buttons' | 'mb' | 'headingStyle' | 'classes'>) => {
  const { children, title, subtitle, isVisible, className, buttons, mb = 3, headingStyle = 3, classes = {} } = props;

  if (isVisible === false) {
    return null;
  }

  return <div className={`dashboard-row mb-${mb} ${className ?? ''}`}>
    {title || buttons ?
      (
        <div className={'pl-2 d-flex align-items-center'}>
          <div className={`dashboard-row__title__container h${headingStyle} flex-fill d-flex align-items-center`}>
            {title ?? ''}
          </div>
          <ActionButtons buttons={buttons} />
        </div>
      ) : <></>
    }
    {subtitle && (
      <div className='subtitle-container pl-2 mb-1'>
        {subtitle}
      </div>
    )}
    {children ? <div className={`dashboard-children d-flex flex-column flex-lg-row ${classes.children ?? ''}`}>
      {children}
    </div> : null}
  </div>;
}

export const DashboardColumn = (
  props: DashboardSectionProps & { flexBasisPc?: string; headerIcon?: JSX.Element | null }
) => {
  const {
    children,
    title,
    subtitle,
    isVisible,
    className,
    buttons,
    flexBasisPc = '33%',
    padding = 3,
    headerIcon = null,
  } = props;
  if (isVisible === false) {
    return <></>;
  }

  return <div style={{ flexBasis: flexBasisPc }}
    className={`dashboard-column whiteBoxContainer d-flex flex-column p-${padding} ${className ?? ''}`}>
    <div className='d-flex align-items-center'>
      <div className='d-flex flex-fill'>
        <div className={`flex-fill h${props.headingStyle ?? 5}`}>{title ?? ''}</div>
        {headerIcon}
      </div>
      <ActionButtons buttons={buttons} />
    </div>
    {subtitle && (
      <div className='subtitle-container mb-1'>
        {subtitle}
      </div>
    )}
    <div className={'dashboard-children flex-fill'}>
      {children}
    </div>
  </div>;
}

export const DashboardEmptyColumn = (props: Pick<DashboardSectionProps, 'padding'> & { flexBasisPc?: string }) => {
  const { flexBasisPc = '33%', padding = 3 } = props;
  return <div style={{ flexBasis: flexBasisPc }} className={`dashboard-column d-flex flex-column p-${padding}`}></div>;
}

export default function Dashboard(props: DashboardProps) {
  const { id, testId, hasSidebar, className, sidebarPosition = 'left' } = props;

  const children = Array.isArray(props.children) ? [...props.children] : [props.children];
  // Assumes first item is the sidebar!
  const sidebar = hasSidebar && children.length > 0 ? children.shift() : <></>;

  return (
    <div
      id={id}
      data-testid={testId}
      className={`dashboard-container d-flex ${className ?? ''} ${
        hasSidebar === true ? `has-sidebar sidebar-${sidebarPosition}` : ''
      }`}
    >
      {sidebarPosition === 'left' ? sidebar : null}
      {sidebar && sidebarPosition === 'right' ? <div className='sidebar-spacer' /> : null}
      <div className='dashboard-children w-100 d-flex flex-column'>{children}</div>
      {sidebarPosition === 'right' ? sidebar : null}
    </div>
  );
}

export const DashboardSectionCollapsible = (props: DashboardSectionCollapsibleProps) => {
  const { isCollapsed, toggleCollapse, padding = 2 } = props;
  const children = isCollapsed ? null : props.children;

  return (
    <DashboardSection
      padding={padding}
      onClick={toggleCollapse}
      {...props}
      buttons={[
        ...(isCollapsed ? [] : props.buttons ?? []),
        <IconButton
          key='collapse-button'
          outline={false}
          className='collapse-button'
          color='transparent'
          onClick={toggleCollapse}
          icon={'fal fa-caret-down'}
        />
      ]}
      className={`${props.className} ${isCollapsed ? 'closed' : 'open'}`}
    >
      {children}
    </DashboardSection>
  );
};
