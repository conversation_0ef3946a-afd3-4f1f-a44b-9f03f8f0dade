/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

@import '../../css/variables';
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins';
@import './dashboard-animation.scss';

.dashboard-container {
  width: 100%;
  max-width: $max-width-container;
  margin: 2rem auto;

  &.w-100 {
    max-width: 100% !important;
    .dashboard-row,
    .dashboard-section {
      max-width: 100% !important;
    }
  }

  .dashboard-section {
    @include dashboardAnimation;
    margin-right: auto;
  }

  &.has-sidebar {
    flex-direction: column !important;

    &.sidebar-left {
      max-width: $max-width-container;
      @include media-breakpoint-up(xxl) {
        max-width: $max-width-container-xxl;
      }
      > :first-child {
        padding-right: 0rem !important;
      }
    }

    &.sidebar-right {
      max-width: $max-width-container;
      @include media-breakpoint-up(xxl) {
        max-width: $max-width-container-xxl;
      }
      > :last-child {
        padding-right: 0rem;
        padding-left: 0rem;
        max-width: $max-width-container;
        @include media-breakpoint-up(xxl) {
          max-width: $sidebar-width;
        }
      }
      .dashboard-children {
        margin-left: auto;
        margin-right: auto;
        @include media-breakpoint-up(xxl) {
          margin-left: auto;
          margin-right: 1rem;
        }
        .dashboard-section {
          @include dashboardAnimation;
          margin-right: 0;
          margin-left: auto;
        }
      }
      .sidebar-spacer {
        display: none;
        flex-shrink: 1;
        width: 100%;
        @include media-breakpoint-up(xxl) {
          max-width: calc((100vw - $max-width-container) / 2);
          display: block;
          flex-grow: 0;
          flex-shrink: 1;
        }
        @media (min-width: 1600px) {
          max-width: calc($sidebar-width + $sidebar-margin);
        }
      }
    }

    @include media-breakpoint-up(xxl) {
      flex-direction: row !important;
      padding: 0rem 1rem;

      .dashboard-section,
      .dashboard-row {
        padding: 0rem 1rem;
      }
    }
  }

  .dashboard-row {
    max-width: $max-width-container;
    width: 100%;
    padding: 0rem 0.1rem;
    .dashboard-column {
      margin-bottom: 1rem;
      &.whiteBoxContainer:last-of-type {
        margin-bottom: 0rem;
      }
    }
  }

  @include media-breakpoint-up(lg) {
    .dashboard-section,
    .dashboard-row {
      padding: 0rem 1rem;
      .dashboard-column {
        margin-left: 0.5rem;
        margin-right: 0.5rem;
        margin-bottom: 0rem;
        &:first-of-type {
          margin-left: 0rem;
        }
        &:last-of-type {
          margin-right: 0rem;
        }
      }
    }
  }

  .dashboard-section,
  .dashboard-row .dashboard-column {
    .h2,
    .h3 {
      margin-bottom: 0rem;
      .action-buttons {
        > button,
        > span > button,
        > .dropdown > button {
          margin-left: 0.3rem;
        }

        @include media-breakpoint-down(xl) {
          top: -12px;
        }
      }
    }
  }

  .subtitle-container {
    color: var(--theme-TextMedium);
    @include media-breakpoint-down(xl) {
      margin-top: 10px;
    }
  }
}
