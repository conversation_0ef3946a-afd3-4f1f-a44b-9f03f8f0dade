/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { useCallback, useEffect, useState } from 'react';
import Dashboard, { DashboardRow, DashboardSection } from '../dashboard';
import { getGroup } from '@g17eco/core';
import { ReduxStateLoadable } from '@reducers/types';
import { DownLoadCardItem, getShareDownloadCards } from './downloadCards';
import { DataShareDownloads } from './DataShareDownloads';
import { SurveyListDropdown } from '../initiative/SurveyListDropdown';
import { DataScopeAccess, DataShareCreateBasic, DataShareLookup } from '../../types/dataShare';
import G17Client from '../../services/G17Client';
import { Loader } from '@g17eco/atoms/loader';
import { SurveyListItem } from '../../types/survey';
import { isCTL } from '../../utils/permission-groups';
import { getDataShareScopeConstraints } from '../../utils/dataShare';
import { getMainDownloadCode } from '../../config/app-config';
import { Button } from 'reactstrap';
import { DataShareModal, getDefaultContent } from './DataShareModal';
import { InitiativeCompany, InitiativeType } from '../../types/initiative';
import { Portfolio } from '../../types/portfolio';
import { BasicAlert } from '@g17eco/molecules/alert';

interface Props extends Pick<DataShareCreateBasic, 'requesterId' | 'requesterType'> {
  initiativeId: string;
  portfolio: Portfolio,
  company: InitiativeCompany;
  backButton?: React.JSX.Element;
  reload: () => void;
}

type DataShareState = ReduxStateLoadable<DataShareLookup>

export const DataShareDownloadContainer = (props: Props) => {

  const { initiativeId, requesterType, requesterId, company, portfolio, reload } = props;

  const [shareModal, setShareModal] = useState(false);
  const [item, setItem] = useState<SurveyListItem | undefined>();
  const [state, setState] = useState<DataShareState>({
    loaded: false,
    data: undefined,
    errored: false
  })


  const load = useCallback(({
    requesterId,
    requesterType,
    initiativeId
  }: Pick<DataShareCreateBasic, 'requesterId' | 'requesterType' | 'initiativeId'>) => {
    G17Client.getRequesterDataShare({ requesterId, requesterType, initiativeId }).then(data => {
      setState((s) => ({ ...s, loaded: true, data }))
      setItem(data.list[0])
    }).catch((e: Error) => {
      setState((s) => ({
        ...s,
        loaded: false,
        data: undefined,
        errored: true,
        errorMessage: e.message
      }))
    })
  }, [])

  useEffect(() => {
    load({ initiativeId, requesterId, requesterType })
  }, [initiativeId, load, requesterId, requesterType])

  if (state.errored) {
    return (
      <Dashboard className='mt-0'>
        {props.backButton ? <DashboardRow>{props.backButton}</DashboardRow> : null}
        <DashboardSection title={'Downloads'}>
          <BasicAlert className={'my-5'} type={'danger'}>
            {state.errorMessage}
          </BasicAlert>
        </DashboardSection>
      </Dashboard>
    );
  }

  if (!state.loaded) {
    return (
      <Dashboard className='mt-0'>
        {props.backButton ? <DashboardRow>{props.backButton}</DashboardRow> : null}
        <Loader relative={true} />
      </Dashboard>
    );
  }

  const toggleShareModal = (update?: { reload?: boolean }) => {
    setShareModal(false)
    if (update?.reload) {
      return reload()
    }
  }

  // No active data shares available, allow them to trigger data share here?
  const { initiative, dataShares, list, combinedDataScopeAccess, metricGroups = [] } = state.data;
  if (dataShares.length === 0) {
    return (
      <Dashboard className='mt-0'>
        {props.backButton ? <DashboardRow>{props.backButton}</DashboardRow> : null}
        <DashboardSection>
          <div className='my-5 text-center'>
            <h3>Company has not shared any data</h3>

            {company.type !== InitiativeType.initiative ? null : (
              <div>
                <Button color='primary' className='mt-3' onClick={() => setShareModal(true)}>
                  <i className='fa fa-bell mr-2' />
                  Request access
                </Button>
                <DataShareModal
                  key={initiativeId}
                  isOpen={shareModal}
                  toggle={toggleShareModal}
                  requesterId={requesterId}
                  requesterType={requesterType}
                  title={`Data share request from ${portfolio.name}`}
                  content={getDefaultContent({ requesterName: portfolio.name })}
                  initiativeId={company._id}
                  initiativeName={company.name}
                />
              </div>
            )}
          </div>
        </DashboardSection>
      </Dashboard>
    );
  }

  if (!item) {
    return (
      <Dashboard className='mt-0'>
        {props.backButton ? <DashboardRow>{props.backButton}</DashboardRow> : null}
        <DashboardSection>
          <div className='my-5 text-center'>
            <h3>Company does not have any completed reports to download</h3>
          </div>
        </DashboardSection>
      </Dashboard>
    );
  }

  const { scopeConstraint, access } = getDataShareScopeConstraints(dataShares, combinedDataScopeAccess);
  const fullAccess = access === DataScopeAccess.Full;
  const cards = getShareDownloadCards({ isCTL: isCTL(initiative), fullAccess })
    .reduce((acc, card) => {
    // Disable cards if constrain is available, and it's not part of it
    const isDisabled = card.isDisabled ||
      access === DataScopeAccess.None ||
      // Must be partial access at this point
      (!fullAccess && !scopeConstraint.has(card.code));

    const group = getGroup('frameworks', card.code) || getGroup('standards', card.code);
    return [...acc, { ...card, isDisabled, src: group?.src }];
  }, [] as DownLoadCardItem[]);

  return (
    <Dashboard className='downloads mt-0'>
      <DashboardSection title='Sustainability report' headingStyle={4} buttons={[
        <SurveyListDropdown
          key='survey-list-dropdown'
          surveyList={list}
          selectedItem={item}
          handleDropdownSelect={(item) => setItem(item)}
        />
      ]}>
        <DataShareDownloads
          surveyId={item._id}
          metricGroups={metricGroups}
          combinedDataScopeAccess={combinedDataScopeAccess}
          primaryCode={getMainDownloadCode(initiative.appConfigCode, initiative.permissionGroup)}
          initiativeId={initiativeId}
          requesterId={requesterId}
          requesterType={requesterType}
          surveyListItem={item}
          items={cards}
        />
      </DashboardSection>
    </Dashboard>
  );
}

