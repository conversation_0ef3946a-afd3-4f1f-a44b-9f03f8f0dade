/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { useEffect, useMemo, useState } from 'react';
import Dashboard, { DashboardRow, DashboardSection, DashboardSectionTitle } from '../dashboard';
import { AdminBreadcrumbs } from '../../routes/admin-dashboard/AdminBreadcrumbs';
import { RequestReviewSelection } from './partials/RequestReviewSelection';
import { ReviewPendingRequests } from './partials/ReviewPendingRequests';
import { DataShareActions, DataShareStatus, DataShareWithRequester } from '../../types/dataShare';
import './DataShareSettings.scss';
import { DataShareAcceptModal } from './partials/DataShareAcceptModal';
import {
  useAcceptDataShareMutation,
  useGetCombinedScopeQuery,
  useGetDataShareTemplateQuery,
  useGetDataSharesQuery,
  useRemoveDataShareMutation,
  useRevokeDataShareMutation,
  useUpdateDataShareMutation,
  useUpdateDataShareTemplateMutation,
} from '../../api/data-share';
import { Loader } from '@g17eco/atoms/loader';
import { DataSharePermissions } from './partials/DataSharePermissions';
import { MandatoryDataSharePermissions } from './partials/MandatoryDataSharePermissions';
import { loadCustomMetricGroupsByInitiativeId } from '../../actions/initiative';
import { NoDataShareConfirmModal } from './partials/NoDataShareConfirmModal';
import { useToggle } from '../../hooks/useToggle';
import { RequestReviewMethod } from '../../types/initiative';
import { MESSAGE } from '../../utils/dataShare';
import { PermissionsTemplate } from './partials/PermissionsTemplate';
import { useAppDispatch, useAppSelector } from '../../reducers';
import { BasicAlert } from '@g17eco/molecules/alert';

const NoDataShareRequest = (props: { text: string }) => (
  <DashboardSection
    className='data-share-permissions'
    classes={{
      whiteBoxContainer: 'border-0',
    }}
  >
    <div className='text-center mt-3 text-muted'>{props.text}</div>
  </DashboardSection>
);

export interface DataShareSettingsProps {
  initiativeId: string;
  shareId?: string;
}

export const DataShareSettings = (props: DataShareSettingsProps) => {
  const { initiativeId, shareId } = props;
  const [modalState, setModalState] = useState<DataShareWithRequester | undefined>();
  const dispatch = useAppDispatch();
  const [openNoAccessModal, toggleNoAccessModal] = useToggle(false);

  const { data: metricGroups } = useAppSelector((state) => state.customMetricGroups);
  const {
    data: dataShares,
    isFetching,
    error: getDataSharesError,
  } = useGetDataSharesQuery(initiativeId, { skip: !initiativeId });
  const [updateDataShare, { isLoading: loadingUpdate, error: updateDataShareError }] = useUpdateDataShareMutation();
  const [acceptDataShare, { isLoading: loadingAccept, error: acceptDataShareError }] = useAcceptDataShareMutation();
  const [revokeDataShare, { isLoading: loadingRevoke, error: revokeDataShareError }] = useRevokeDataShareMutation();
  const [removeDataShare, { isLoading: loadingRemove, error: removeDataShareError }] = useRemoveDataShareMutation();
  const {
    data: initiativeCombinedScope,
    isFetching: isFetchingCombinedScope,
    error: getCombinedScopeError,
  } = useGetCombinedScopeQuery(initiativeId, { skip: !initiativeId });

  const {
    data: dataShareTemplate,
    isFetching: isFetchingTemplate,
    error: getDataShareTemplateError,
  } = useGetDataShareTemplateQuery(initiativeId, {
    skip: !initiativeId,
  });
  const [updateDataShareTemplate, { isLoading: loadingUpdateTemplate, error: updateDataShareTemplateError }] =
    useUpdateDataShareTemplateMutation();

  useEffect(() => {
    if (!initiativeId) {
      return;
    }
    dispatch(loadCustomMetricGroupsByInitiativeId(initiativeId));
  }, [dispatch, initiativeId]);

  const { pending, accepted, mandatory } = useMemo(() => {
    return (dataShares ?? []).reduce(
      (a, c) => {
        if (c.status === DataShareStatus.Pending) {
          a.pending.push(c);
        } else if (c.restriction) {
          a.mandatory.push(c);
        } else if (c.status === DataShareStatus.Accepted) {
          a.accepted.push(c);
        }
        return a;
      },
      {
        pending: [] as DataShareWithRequester[],
        accepted: [] as DataShareWithRequester[],
        mandatory: [] as DataShareWithRequester[],
      },
    );
  }, [dataShares]);

  const isLoading = [
    isFetching,
    loadingUpdate,
    loadingAccept,
    loadingRevoke,
    loadingRemove,
    isFetchingTemplate,
    loadingUpdateTemplate,
    isFetchingCombinedScope,
  ].some((loading) => loading);

  const errorMessage = [
    getDataSharesError,
    updateDataShareError,
    acceptDataShareError,
    revokeDataShareError,
    removeDataShareError,
    getDataShareTemplateError,
    updateDataShareTemplateError,
    getCombinedScopeError,
  ].find((error) => error?.message)?.message;

  useEffect(() => {
    if (shareId && dataShares) {
      const requestAccess = dataShares.find((d) => d._id === shareId);
      if (requestAccess?.status === DataShareStatus.Pending) {
        setModalState(requestAccess);
      }
    }
  }, [dataShares, shareId]);

  const handleChangeRequestReview = async (method: RequestReviewMethod) => {
    if (method === RequestReviewMethod.NoAccess) {
      toggleNoAccessModal();
      return;
    }
    await updateDataShareTemplate({
      initiativeId,
      updateDataShare: {
        requestReview: method,
        dataScope: dataShareTemplate?.dataScope,
      },
    });
  };

  const handleOpenPendingRequest = (dataShare: DataShareWithRequester) => setModalState(dataShare);
  const handleClosePendingRequest = () => setModalState(undefined);

  const actions = useMemo<DataShareActions>(
    () => ({
      remove: async (d) => {
        await removeDataShare(d._id);
        handleClosePendingRequest();
      },
      revoke: async (d) => {
        await revokeDataShare(d._id);
        handleClosePendingRequest();
      },
      accept: async (d) => {
        await acceptDataShare({ dataShareId: d._id, updateDataScope: d.dataScope });
        handleClosePendingRequest();
      },
      update: async (d, dataScope) => {
        await updateDataShare({ dataShareId: d._id, dataScope });
      },
      updateTemplate: async (updateDataShare) => {
        await updateDataShareTemplate({ initiativeId, updateDataShare });
      },
    }),
    [removeDataShare, revokeDataShare, acceptDataShare, updateDataShare, updateDataShareTemplate, initiativeId],
  );

  const isAutomate = dataShareTemplate?.requestReview === RequestReviewMethod.Automate;
  const isNoAccess = dataShareTemplate?.requestReview === RequestReviewMethod.NoAccess;
  const hasDataShares = dataShares && dataShares.length > 0;
  const hasPending = pending.length > 0;
  const hasAccepted = accepted.length > 0;

  return (
    <div className='data-share-settings'>
      {isLoading ? <Loader /> : null}
      <Dashboard className='mb-2'>
        <DashboardRow>
          <AdminBreadcrumbs breadcrumbs={[{ label: 'Data Sharing' }]} initiativeId={initiativeId} />
        </DashboardRow>
      </Dashboard>
      <Dashboard key={'main'} className='mt-0'>
        <DashboardSectionTitle
          title='Data sharing and messaging'
          subtitle='This page allows you to control permissions around data sharing and messaging with users of the Portfolio
          Tracker application on the G17Eco platform. Portfolio Tracker users are financial institutions, such as banks,
          investment companies, government regulators, and stock exchanges.'
        />
        {errorMessage ? <BasicAlert type='danger'>{errorMessage}</BasicAlert> : null}
        <RequestReviewSelection
          dataShareTemplate={dataShareTemplate}
          handleChangeRequestReview={handleChangeRequestReview}
        />

        {isNoAccess || (!isAutomate && !hasDataShares) ? (
          <NoDataShareRequest text={isNoAccess ? MESSAGE.NO_ACCESS : MESSAGE.NO_REQUEST} />
        ) : (
          <>
            {isAutomate || hasPending || hasAccepted ? <DashboardSectionTitle title='Data share permissions' /> : null}
            {hasPending ? (
              <ReviewPendingRequests pendingRequests={pending} handleOpenPendingRequest={handleOpenPendingRequest} />
            ) : null}

            {isAutomate ? (
              <DashboardSection mb={4}>
                <PermissionsTemplate
                  dataShareTemplate={dataShareTemplate}
                  metricGroups={metricGroups}
                  initiativeCombinedScope={initiativeCombinedScope}
                  actions={actions}
                />
              </DashboardSection>
            ) : null}

            {hasAccepted || isAutomate ? (
              <DataSharePermissions
                dataShares={accepted}
                metricGroups={metricGroups}
                actions={actions}
                initiativeCombinedScope={initiativeCombinedScope}
              />
            ) : null}

            {mandatory.length > 0 ? (
              <>
                <DashboardSectionTitle title='Mandatory data share permissions' />
                <MandatoryDataSharePermissions dataShares={mandatory} />
              </>
            ) : null}
          </>
        )}
      </Dashboard>

      {modalState ? (
        <DataShareAcceptModal toggle={() => setModalState(undefined)} dataShare={modalState} actions={actions} />
      ) : null}

      <NoDataShareConfirmModal
        isOpen={openNoAccessModal}
        dataShareTemplate={dataShareTemplate}
        actions={actions}
        toggle={toggleNoAccessModal}
      />
    </div>
  );
};
