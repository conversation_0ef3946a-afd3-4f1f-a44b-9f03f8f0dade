/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React from 'react';
import { Input } from 'reactstrap';
import { DashboardSection } from '../../dashboard';
import { InitiativeDataShare, RequestReviewMethod } from '../../../types/initiative';
import { MESSAGE } from '../../../utils/dataShare';

export const RequestReviewSelection = (props: {
  dataShareTemplate: InitiativeDataShare | undefined;
  handleChangeRequestReview: (method: RequestReviewMethod) => void;
}) => {
  const { dataShareTemplate, handleChangeRequestReview } = props;
  const requestReviewMethod = dataShareTemplate?.requestReview;

  return (
    <DashboardSection
      title='I want to:'
      paddingInternal={0}
      classes={{
        whiteBoxContainer: 'border-0 p-5',
        whiteBoxWrapper: 'px-2',
      }}
    >
      <div className='d-flex align-items-center'>
        <Input
          id={RequestReviewMethod.Review}
          type='radio'
          name='acceptanceMethod'
          className='mr-4'
          onChange={() => handleChangeRequestReview(RequestReviewMethod.Review)}
          checked={requestReviewMethod === RequestReviewMethod.Review}
          value={RequestReviewMethod.Review}
        />
        <label htmlFor={RequestReviewMethod.Review}>
          <strong>Review requests: </strong>Review data share requests when they are made and set permissions per
          request
        </label>
      </div>
      <div className='d-flex align-items-center mt-3'>
        <Input
          id={RequestReviewMethod.Automate}
          type='radio'
          name='acceptanceMethod'
          className='mr-4'
          onChange={() => handleChangeRequestReview(RequestReviewMethod.Automate)}
          checked={requestReviewMethod === RequestReviewMethod.Automate}
          value={RequestReviewMethod.Automate}
        />
        <label htmlFor={RequestReviewMethod.Automate}>
          <strong>Automate access: </strong>Allow all Portfolio Tracker the same level of access and set permissions up
          front
        </label>
      </div>
      <div className='d-flex align-items-center mt-3'>
        <Input
          id={RequestReviewMethod.NoAccess}
          type='radio'
          name='acceptanceMethod'
          className='mr-4'
          onChange={() => handleChangeRequestReview(RequestReviewMethod.NoAccess)}
          checked={requestReviewMethod === RequestReviewMethod.NoAccess}
          value={RequestReviewMethod.NoAccess}
        />
        <label htmlFor={RequestReviewMethod.NoAccess}>
          <strong>No access: </strong>Turn off all access / Reject all requests
        </label>
      </div>
      <div className='text-center mt-3 text-muted'>{MESSAGE.SHARE_NOTE}</div>
    </DashboardSection>
  );
};
