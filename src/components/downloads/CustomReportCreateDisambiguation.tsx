/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useHistory } from 'react-router-dom';
import { Button } from 'reactstrap';
import { generateUrl } from '../../routes/util';
import { DashboardRow, DashboardSection } from '../dashboard';
import CustomReportManage from './CustomReportManage';
import CustomReportManageSubsidiary from './CustomReportManageSubsidiary';
import { CustomReportRoutes } from './CustomReportsRoute';
import G17Client from '../../services/G17Client';
import CustomReportManageDate from './CustomReportManageDate';
import './styles.scss';
import { QUESTION, SURVEY } from '@constants/terminology';
import { CustomReportTemplateBuilder } from '../../apps/company-tracker/components/custom-report';
import { CreateCustomReportType } from '@g17eco/types/custom-report';

interface CardProps {
  title: string;
  description: string;
  handleSelect: () => void;
  handleDownloadExample: () => void;
  disabled?: boolean;
}

const Card = ({ disabled, title, description, handleSelect, handleDownloadExample }: CardProps) => {
  if (disabled) {
    return null;
  }

  return (
    <DashboardSection paddingInternal={0}>
      <div className='d-flex align-items-center gap-4'>
        <div className=''>
          <h5 className='mt-0'>{title}</h5>
          <p className='mb-0 text-ThemeTextMedium'>{description}</p>
        </div>
        <div className='flex-shrink-0 d-flex flex-column'>
          <Button color='link' onClick={handleDownloadExample} className='mb-1'>
            Export example
          </Button>
          <Button color='primary' onClick={handleSelect}>
            Use report
          </Button>
        </div>
      </div>
    </DashboardSection>
  );
};

interface Props {
  initiativeId: string;
  createType?: CreateCustomReportType;
}

export const CustomReportCreateDisambiguation = ({ initiativeId, createType }: Props) => {
  const history = useHistory();

  switch (createType) {
    case CreateCustomReportType.Calculated:
      return <CustomReportManage initiativeId={initiativeId} />;
    case CreateCustomReportType.Subsidiary:
      return <CustomReportManageSubsidiary initiativeId={initiativeId} />;
    case CreateCustomReportType.Date:
      return <CustomReportManageDate initiativeId={initiativeId} />;
    case CreateCustomReportType.Template:
      return <CustomReportTemplateBuilder initiativeId={initiativeId} />;
  }

  const handleSelect = (createType: CreateCustomReportType) => {
    history.push(generateUrl(CustomReportRoutes.CREATE_REPORT, { initiativeId, createType }));
  };

  const handleDownloadExample = async (type: CreateCustomReportType) => {
    return G17Client.customReportDownloadExample(initiativeId, type).catch((e) => console.log(e));
  };

  const cards: (Pick<CardProps, 'title' | 'disabled' | 'description'> & { createType: CreateCustomReportType })[] = [
    {
      title: 'Custom report template',
      description: `Build a custom report pulling only the datapoints you need. Configure the specific data source ${SURVEY.PLURAL} you will generate the report from, and customise which data-points (columns) you want to export for each scoped ${QUESTION.SINGULAR}. `,
      createType: CreateCustomReportType.Template,
    },
    {
      title: 'Date comparison report',
      description: `Compare and validate how your organisation has answered ${QUESTION.PLURAL} over specific periods and timeframe (i.e. Jan-Dec). All scoped ${QUESTION.PLURAL} are included in this report download and you will only be able to select specific report dates.`,
      createType: CreateCustomReportType.Date,
    },
    {
      title: 'Subsidiary comparison report',
      description: `Review and compare how your subsidiaries have answered ${SURVEY.SINGULAR} ${QUESTION.PLURAL} for a specified ${SURVEY.SINGULAR} period and date combination. All scoped ${QUESTION.PLURAL} are included in this report download and you can compare subsidiaries from anywhere in your organisation.`,
      createType: CreateCustomReportType.Subsidiary,
    },
    {
      title: 'Calculated report',
      description: `Build your own report containing only the ${QUESTION.SINGULAR} rows you want export. Additionally create calculated ${QUESTION.PLURAL} from your existing ${QUESTION.SINGULAR}\'s to generate the reporting outputs you require (For example number of female employees divided by total employee count).`,
      createType: CreateCustomReportType.Calculated,
    },
  ];

  return (
    <>
      <DashboardRow>
        <h3>Which type of excel report would you like to create:</h3>
      </DashboardRow>

      {cards.map((card) => (
        <Card
          key={card.title}
          handleSelect={() => handleSelect(card.createType)}
          handleDownloadExample={() => handleDownloadExample(card.createType)}
          {...card}
        />
      ))}
    </>
  );
};
