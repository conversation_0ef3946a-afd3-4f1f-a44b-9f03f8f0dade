/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { CustomReportProps } from '@g17eco/types/custom-report';
import { DashboardSection } from '../dashboard';
import CustomReportEditForm from './CustomReportEditForm';
import CustomReportMetricsList from './CustomReportMetricsList';
import './styles.scss';

const CustomReportManage = (props: CustomReportProps) => {
  return (
    <>
      <DashboardSection
        headingStyle={4}
        title={'Calculated report builder'}
      >
        <div className='mt-4'>
          <CustomReportEditForm {...props} />
        </div>

        <div className='mt-5'>
          <hr />

          <div className='mt-4 strong'>Metrics in this report</div>

          <div className='mt-3'>
            <CustomReportMetricsList {...props} />
          </div>
        </div>
      </DashboardSection>
    </>
  )
}

export default CustomReportManage;
