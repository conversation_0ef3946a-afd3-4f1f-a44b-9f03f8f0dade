/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React from 'react';
import { DashboardSection } from '../dashboard';
import { SurveyRange } from '../../types/custom-report';
import CustomReportEditFormDate from './CustomReportEditFormDate';
import './styles.scss';

interface CustomReportManageDateProps {
  customReport?: SurveyRange;
  initiativeId: string;
}

const CustomReportManageDate = ({ customReport, initiativeId }: CustomReportManageDateProps) => {

  return (
    <>
      <DashboardSection title={'Date comparison report builder'}>
        <div className='mt-4'>
          <CustomReportEditFormDate
            customReport={customReport}
            initiativeId={initiativeId}
          />
        </div>
      </DashboardSection>
    </>
  );
};

export default CustomReportManageDate;
