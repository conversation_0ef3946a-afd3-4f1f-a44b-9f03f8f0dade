/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React from 'react';
import { DashboardSection } from '../dashboard';
import { CustomInitiatives, CustomReportType } from '../../types/custom-report';
import CustomReportEditFormSubsidiary from './CustomReportEditFormSubsidiary';
import './styles.scss';

interface CustomReportManageSubsidiaryProps {
  customReport?: CustomInitiatives;
  initiativeId: string;
}

const CustomReportManageSubsidiary = ({ customReport, initiativeId }: CustomReportManageSubsidiaryProps) => {

  return (
    <>
      <DashboardSection
        title={'Subsidiary comparison report builder'}
      >
        <div className='mt-4'>
          <CustomReportEditFormSubsidiary customReport={customReport} initiativeId={initiativeId} />
        </div>

      </DashboardSection>
    </>
  )
}

export default CustomReportManageSubsidiary;
