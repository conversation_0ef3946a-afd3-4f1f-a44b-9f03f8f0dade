/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { useHistory } from 'react-router-dom';
import { Button } from 'reactstrap';
import { generateUrl } from '../../routes/util';
import { CustomReport, getMetricTypeLabel, MetricType } from '../../types/custom-report';
import { DashboardSection } from '../dashboard';
import { CustomReportRoutes } from './CustomReportsRoute';
import './styles.scss'

interface CustomReportMetricAddDisambiguationProps {
  customReport: CustomReport;
}

const CustomReportMetricAddDisambiguation = ({ customReport }: CustomReportMetricAddDisambiguationProps) => {
  const history = useHistory();

  const handleClick = (type: MetricType) => () => {
    const url = generateUrl(CustomReportRoutes.ADD_METRIC, {
      initiativeId: customReport.initiativeId,
      reportId: customReport._id,
      metricType: type
    });
    history.push(url);
  };

  return <DashboardSection title={customReport.name}>
    <div className='mt-4'>
      <strong>I want to add a</strong>
    </div>

    <div className='text-center mt-4'>
      <Button onClick={handleClick(MetricType.Single)}>
        {getMetricTypeLabel(MetricType.Single)}
      </Button>
      <Button className='ml-4' onClick={handleClick(MetricType.Calculated)}>
        {getMetricTypeLabel(MetricType.Calculated)}
      </Button>
      <Button className='ml-4' onClick={handleClick(MetricType.Text)}>
        {getMetricTypeLabel(MetricType.Text)}
      </Button>
    </div>
  </DashboardSection>
}

export default CustomReportMetricAddDisambiguation;
