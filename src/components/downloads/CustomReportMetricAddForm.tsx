/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';
import { Button, Pagination, PaginationItem, PaginationLink } from 'reactstrap';
import { CustomReport, MetricSubmitData, MetricType, ShowAsTypes } from '../../types/custom-report';
import { TableColumnType, UtrValueType } from '../../types/universalTracker';
import CreateMetricForm from '../create-metric-form/CreateMetricForm';
import { DashboardSection } from '../dashboard';
import { generateUrl } from '../../routes/util';
import { Loader } from '@g17eco/atoms/loader';
import G17Client from '../../services/G17Client';
import { CustomReportRoutes } from './CustomReportsRoute';
import { RouteInterfaceMin } from '../../types/routes';
import NumberFormat from '../../utils/number-format';
import { calculateFontsize } from '../../utils/string';
import './styles.scss';
import { useAddMetricToCustomReportMutation } from '@api/custom-reports';

const getShowAsTypes = (type: MetricType): ShowAsTypes[] => {
  switch (type) {
    case MetricType.Calculated:
      return ['total', 'percentage'];
    case MetricType.Text:
      return ['text'];
    case MetricType.Single:
    default:
      return ['total'];
  }
}

const getPrimaryUtrTypes = (type: MetricType): UtrValueType[] => {
  switch (type) {
    case MetricType.Text:
      return [UtrValueType.Text, UtrValueType.ValueList, UtrValueType.ValueListMulti, UtrValueType.Table]
    case MetricType.Single:
      return [UtrValueType.Number, UtrValueType.Percentage, UtrValueType.ValueList, UtrValueType.ValueListMulti, UtrValueType.Table]
    default:
      return [UtrValueType.ValueList, UtrValueType.ValueListMulti, UtrValueType.Table]
  }
}

const getUtrColumnTypes = (type: MetricType) => {
  switch (type) {
    case MetricType.Calculated:
      return [TableColumnType.ValueList, TableColumnType.Text, TableColumnType.ValueListMulti, TableColumnType.Number];
    case MetricType.Text:
      return [TableColumnType.Text];
    case MetricType.Single:
    default:
      return [TableColumnType.Number];
  }
}

interface CustomReportMetricAddFormProps {
  type: MetricType;
  customReport: CustomReport;
}

interface PreviewData {
  value: string | number | string[];
  type: 'string' | 'number';
}

const CustomReportMetricAddForm = (props: CustomReportMetricAddFormProps) => {
  const {
    type,
    customReport,
  } = props;

  const history = useHistory();
  const [data, setData] = useState<MetricSubmitData | undefined>();
  const [preview, setPreview] = useState<PreviewData | undefined>();

  const initiativeId = customReport.initiativeId;

  const [addMetricToCustomReport] = useAddMetricToCustomReportMutation();

  if (!customReport) {
    return <Loader />;
  }

  const handleSubmit = async (addAnother = false) => {
    if (!data) {
      return;
    }

    addMetricToCustomReport({ initiativeId, reportId: customReport._id, metricType: props.type, metricData: data })
      .unwrap()
      .then(() => {
        handleRedirect(addAnother ? CustomReportRoutes.ADD_METRIC : CustomReportRoutes.MANAGE_REPORT);
      });
  }

  const handleCancel = () => handleRedirect(CustomReportRoutes.ADD_METRIC);

  const handleRedirect = (path: RouteInterfaceMin) => {
    const redirectUrl = generateUrl(path, {
      initiativeId,
      reportId: customReport._id,
    });

    history.push(redirectUrl);
  }

  const handlePreview = () => {
    if (!data) {
      return setPreview(undefined);
    }
    G17Client.customReportPreviewMetric(initiativeId, customReport._id, props.type, data)
      .then(setPreview);
  }

  return <DashboardSection title={customReport.name}>
    <div className='mt-4'>
      <CreateMetricForm
        initiativeId={initiativeId}
        handleUpdate={setData}
        primaryUtrTypes={getPrimaryUtrTypes(type)}
        primaryUtrColumnTypes={getUtrColumnTypes(type)}
        showAsTypes={getShowAsTypes(type)}
        instructions={null}
      />
    </div>
    <div className={`text-right d-flex mt-2 ${preview?.type === 'number' ? 'flex-row-reverse justify-content-between align-items-center' : 'flex-column'}`}>
      <div className='mt-3 text-right'>
        <Button className='ml-3' color='link' onClick={handleCancel}>Back</Button>
        <Button className='ml-3' disabled={!data} outline onClick={handlePreview}><i className='far fa-eye mr-2' />Preview</Button>
        <Button className='ml-3' disabled={!data} outline onClick={() => handleSubmit(true)}>Save & add another</Button>
        <Button className='ml-3' disabled={!data} onClick={() => handleSubmit(false)}>Save</Button>
      </div>
      <div>
        <Preview preview={preview} />
      </div>
    </div>
  </DashboardSection>
}

const Preview = ({ preview }: { preview: PreviewData | undefined }) => {
  const [page, setPage] = useState(0);

  if (!preview) {
    return null;
  }

  const renderValue = (value: string | number | undefined) => {

    if (value === undefined) {
      return (
        <div className={`metric-preview type-${preview.type} no-data`}>
          <span>There is no data currently reported for this metric</span>
        </div>
      );
    }

    if (preview.type === 'number' && typeof value === 'number') {
      const decimalPlaces = 2;
      // Fit maxLenth formatted characters within available width
      const fz = calculateFontsize({ value, defaultRem: 3, maxLenth: 6, decimalPlaces });

      return (
        <div className={'metric-preview type-number'} style={{ fontSize: `${fz}rem` }}>
          <NumberFormat value={value} maximumFractionDigits={decimalPlaces} />
        </div>
      );
    }

    return (
      <div className={`metric-preview type-${preview.type}`}>
        <span>{value}</span>
      </div>
    )
  };

  const renderPagination = () => {
    if (!Array.isArray(preview.value)) {
      return null;
    }
    const pages = preview.value.length;
    const items: JSX.Element[] = [];

    items.push(
      <PaginationItem key={'previous'}>
        <PaginationLink previous href='#' onClick={() => page > 0 && setPage(page - 1)} />
      </PaginationItem>
    );
    for (let i = 0; i < pages; i++) {
      items.push(
        <PaginationItem key={'active'} active={page === i}>
          <PaginationLink href='#' onClick={() => setPage(i)}>{i + 1}</PaginationLink>
        </PaginationItem>
      );
    }
    items.push(<PaginationItem key={'next'}>
      <PaginationLink next href='#' onClick={() => page < (pages - 1) && setPage(page + 1)} />
    </PaginationItem>
    );
    return (
      <div className='mt-2 d-flex justify-content-center'>
        <Pagination>
          {items}
        </Pagination>
      </div>
    );
  }

  if (Array.isArray(preview.value)) {
    return <>
      {renderValue(preview.value[page])}
      {renderPagination()}
    </>;
  }

  return renderValue(preview.value);
}

export default CustomReportMetricAddForm;
