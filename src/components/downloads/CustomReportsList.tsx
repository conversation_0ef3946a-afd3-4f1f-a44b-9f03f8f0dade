/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useHistory } from 'react-router-dom';
import { Button, DropdownItem, DropdownMenu, DropdownToggle, UncontrolledDropdown } from 'reactstrap';
import { useAppSelector } from '../../reducers';
import { generateUrl } from '../../routes/util';
import G17Client from '../../services/G17Client';
import { CustomReport, CustomReportType } from '../../types/custom-report';
import { DATE, formatDateNonUTC } from '../../utils/date';
import { DashboardSection, DashboardSectionTitle } from '../dashboard';
import { Loader } from '@g17eco/atoms/loader';
import { CustomReportRoutes } from './CustomReportsRoute';
import { canManageCurrentLevel } from '../../selectors/user';
import './styles.scss';
import { UpgradeRequired } from '../../routes/upgrade-required';
import { FeaturePermissions } from '../../services/permissions/FeaturePermissions';
import { ColumnDef, Table } from '@g17eco/molecules/table';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import {
  useCloneCustomReportMutation,
  useDeleteCustomReportMutation,
  useGetCustomReportsQuery,
} from '@api/custom-reports';
import { skipToken } from '@reduxjs/toolkit/query';
import { generateErrorToast } from '@components/toasts';

const getType = (type?: CustomReportType) => {
  switch (type) {
    case CustomReportType.Initiatives:
      return 'Subsidiary comparison';
    case CustomReportType.Survey:
    case CustomReportType.SurveyAggregation:
      return 'Date comparison';
    case CustomReportType.Template:
      return 'Custom report';
    case CustomReportType.Metrics:
    default:
      return 'Calculated';
  }
};

interface Props {
  initiativeId: string;
}

export const CustomReportsList = ({ initiativeId }: Props) => {
  const isManager = useAppSelector(canManageCurrentLevel);
  const canAccessCombinedReport = useAppSelector(FeaturePermissions.canAccessCombinedReport);

  const history = useHistory();

  const { data: customReports = [], isFetching } = useGetCustomReportsQuery(initiativeId ?? skipToken);
  const [cloneCustomReport] = useCloneCustomReportMutation();
  const [deleteCustomReport] = useDeleteCustomReportMutation();

  const handleDuplicate = (reportId: string) =>
    cloneCustomReport({ initiativeId, reportId })
      .unwrap()
      .catch((e) => generateErrorToast(e));

  const handleDelete = async (reportId: string) => {
    deleteCustomReport({ initiativeId, reportId })
      .unwrap()
      .catch((e) => generateErrorToast(e));
  };

  const handleEdit = (reportId: string) => {
    history.push(generateUrl(CustomReportRoutes.MANAGE_REPORT, { initiativeId, reportId }));
  };

  const handleDownload = (reportId: string) => {
    return G17Client.downloadCustomReport(initiativeId, reportId).catch((e) => console.log(e));
  };

  const handleGoToCreateReport = () => {
    history.push(generateUrl(CustomReportRoutes.CREATE_REPORT, { initiativeId }));
  };

  if (!canAccessCombinedReport) {
    return <UpgradeRequired />;
  }

  const columns: ColumnDef<CustomReport>[] = [
    {
      header: 'Report',
      accessorKey: 'reportName',
      meta: {
        cellProps: {
          className: 'truncated-container',
        },
      },
      cell: (c) => (
        <div className='text-truncate'>
          <SimpleTooltip
            text={
              <>
                {c.row.original.name}
                <br />
                {c.row.original.description}
              </>
            }
          >
            <Button color='link' onClick={() => handleEdit(c.row.original._id)} className='p-0'>
              {c.row.original.name}
            </Button>
          </SimpleTooltip>
        </div>
      ),
    },
    {
      header: 'Type',
      accessorKey: 'type',
      cell: (c) => getType(c.row.original.type),
    },
    {
      header: 'Created date',
      accessorKey: 'created',
      cell: (c) => formatDateNonUTC(c.row.original.created, DATE.YEAR_MONTH_DATE),
    },
    {
      header: 'Last updated',
      accessorKey: 'lastUpdated',
      cell: (c) => formatDateNonUTC(c.row.original.lastUpdated, DATE.HUMANIZE),
    },
    {
      id: 'buttons',
      header: '',
      accessorKey: 'buttons',
      enableSorting: false,
      cell: (c) => (
        <UncontrolledDropdown>
          <DropdownToggle outline color='transparent' className='border-ThemeNeutralsLight px-2'>
            <i className={'fal fa-bars'}></i>
          </DropdownToggle>
          <DropdownMenu className='border-ThemeBorderDefault'>
            <DropdownItem onClick={() => handleDownload(c.row.original._id)}>
              <i className='fal fa-file-excel mr-2' />
              Download
            </DropdownItem>
            {isManager ? (
              <>
                <DropdownItem onClick={() => handleEdit(c.row.original._id)}>
                  <i className='fal fa-pencil mr-2' />
                  Edit
                </DropdownItem>
                <DropdownItem onClick={() => handleDuplicate(c.row.original._id)}>
                  <i className='fal fa-copy mr-2' />
                  Duplicate
                </DropdownItem>
                <DropdownItem onClick={() => handleDelete(c.row.original._id)}>
                  <i className='fal fa-trash mr-2' />
                  Delete
                </DropdownItem>
              </>
            ) : (
              <></>
            )}
          </DropdownMenu>
        </UncontrolledDropdown>
      ),
    },
  ];

  return (
    <>
      <DashboardSectionTitle
        title='Manage custom reports'
        buttons={
          isManager
            ? [
                <Button onClick={handleGoToCreateReport} key='create-custom-report'>
                  <i className='fal fa-files mr-2' />
                  Create new custom report
                </Button>,
              ]
            : []
        }
      />
      <DashboardSection className='custom-reports-list'>
        <div>
          {isFetching ? (
            <Loader relative />
          ) : (
            <Table
              columns={columns}
              responsive
              data={customReports}
              noData='Use the form below to create a new custom report'
            />
          )}
        </div>
        {isManager ? (
          <div className='mt-5 text-center'>
            <Button color='primary' outline={Boolean(customReports.length)} onClick={handleGoToCreateReport}>
              <i className='fal fa-files mr-2' />
              Create new custom report
            </Button>
          </div>
        ) : null}
      </DashboardSection>
    </>
  );
};
