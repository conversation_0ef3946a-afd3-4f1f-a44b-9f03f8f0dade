/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { useCallback } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { selectWithDefault } from '../../slice/initiativeSurveyListSlice';
import Dashboard, { DashboardSection, DashboardSectionTitle } from '../dashboard';
import { ReportSwitcherContainer } from '../initiative/ReportSwitcherContainer';
import { DownloadCard, DownloadCardItem } from './DownloadCard';
import { getGroup } from '@g17eco/core';
import { ROUTES } from '@constants/routes';
import { getMultiScopeReport, SupportedReportCodes } from '../report-output/outputs';
import { openUpgradeModal } from '@actions/upgradeModal';
import G17Client from '../../services/G17Client';
import { getRootOrg } from '@selectors/initiative';
import { getAnalytics } from '@services/analytics/AnalyticsService';
import { AnalyticsEvents } from '@services/analytics/AnalyticsEvents';
import { Button } from 'reactstrap';
import { generateUrl } from '@routes/util';
import { useAppDispatch, useAppSelector } from '../../reducers';
import { addSiteAlert, SiteAlertColors } from '../../slice/siteAlertsSlice';
import { SDGImpactDownloadButton } from '../report-output/sdg-impact-report';
import { SustainabilityDownloadButton } from '../report-output/sustainability-report';
import { DownloadButton } from '../button/DownloadButton';
import { GroupMessage, isGroupEnabled } from '@constants/groups';
import { getMainDownloadCode } from '../../config/app-config';
import './styles.scss';
import { DownloadMultiScope, DownloadType, HandleDownloadConfig, VisibilityStatus } from '../../types/download';
import { UtrvStatus } from '@constants/status';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { canAccessInsightsAndDownloads, isStaff } from '@selectors/user';
import NotAuthorised from '../../routes/not-authorised';
import { EESG_TYPE, getSortedVersions } from '@constants/standards-frameworks';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { useScopePacksFeature } from '../survey-scope/scope-packs/useScopePacksFeature';
import { UpgradeScopePack } from '@components/survey-scope/scope-packs/UpgradeScopePack';
import {
  DownloadUtrvStatus,
  DownloadXlsxOrCsv,
  convertToUtrvStatuses,
  defaultDownloadDisplayOptions,
  getDefaultDownloadSettings,
} from './util/downloadReportHandler';
import { SURVEY } from '@constants/terminology';
import { FeatureStability } from '@g17eco/molecules/feature-stability';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { useGetSurveyStatusesCountByGroupQuery } from '@api/metrics-download';
import { QueryError } from '@components/query/QueryError';
import { useAppSettings } from '@hooks/app/useAppSettings';
import { getDownloadMultiScope, otherReportCard } from '@components/downloads/util/custom';
import { useGetOrganizationMetricGroupsQuery } from '@api/organization';
import { EESGDownloadButton } from '@components/report-output/eesg-report';
import config from '../../config';

export const INITIAL_TOTALS = { total: 0, verified: 0 }

const cards: DownloadCardItem[] = [
  {
    code: 'gri',
    name: 'GRI aligned report',
    versionGroupCode: 'gri'
  },
  {
    code: 'sasb',
    name: 'SASB aligned report',
  },
  {
    code: 'issb',
    name: 'ISSB aligned report',
  },
  {
    code: 'sdg',
    name: 'SDG aligned report',
    hideXlsx: true,
  },
  {
    code: 'tcfd',
    name: 'TCFD aligned report',
    versionGroupCode: 'tcfd'
  },
  {
    code: 'ungc',
    name: 'UNGC aligned report',
  },
  {
    code: 'ipieca',
    name: 'IPIECA aligned report',
  },
  {
    code: 'iogp',
    name: 'IOGP aligned report',
  },
];

export const DownloadsRoute = () => {

  const list = useAppSelector(selectWithDefault);
  const canAccessDownloads = useAppSelector(FeaturePermissions.canAccessDownloads);
  const canAccessCombinedReport = useAppSelector(FeaturePermissions.canAccessCombinedReport);
  const rootInitiative = useAppSelector(getRootOrg);
  const isUserStaff = useAppSelector(isStaff)
  const scopeConfig = useAppSelector(state => state.globalData.loaded ? state.globalData.data?.config?.survey.scope : undefined);
  const dispatch = useAppDispatch();
  const analytics = getAnalytics();
  const history = useHistory();
  const canViewCSRD = isUserStaff && (history.location.search?.includes('csrd') || config.features.csrdDownload);

  const { defaultDownloadOptions } = useAppSettings();

  const mainCardCode = getMainDownloadCode(rootInitiative?.appConfigCode, rootInitiative?.permissionGroup);
  const isEsGenome = mainCardCode === 'sgx_metrics';
  const { initiativeId, surveyId = '' }: { initiativeId: string, surveyId?: string } = useParams();

  const { isRestricted, scope, onUpgrade } = useScopePacksFeature({ initiativeId });
  const {
    data: cardsTotals = {},
    isLoading,
    error,
  } = useGetSurveyStatusesCountByGroupQuery({ initiativeId, surveyId }, { skip: !surveyId || surveyId === 'current' });

  const { data: metricGroups = [] } = useGetOrganizationMetricGroupsQuery(initiativeId);

  const defaultDownloadSettings = getDefaultDownloadSettings();

  const setMessage = useCallback((message: string) => {
    dispatch(addSiteAlert({
      content: message,
      color: SiteAlertColors.Danger,
    }));
  }, [dispatch])

  const track = async (type: string, code?: string) => {
    return analytics.track(AnalyticsEvents.SurveyDataDownloaded, {
      initiativeId,
      surveyId: surveyId,
      source: 'downloads_page',
      type: type,
      scopeValue: code,
    })
  };

  const canAccessDownloadsByRole = useAppSelector((state) => canAccessInsightsAndDownloads(state, initiativeId));

  const downloadXhtml = async (
    code?: string,
    visibility: VisibilityStatus = VisibilityStatus.Include,
    utrvStatus: DownloadUtrvStatus = UtrvStatus.Verified,
    config?: HandleDownloadConfig,
  ) => {
    return G17Client.generateXhtmlReport({
      surveyId,
      downloadScope: {
        visibility: visibility,
        scope: getDownloadMultiScope(code),
        ...convertToUtrvStatuses(utrvStatus),
        ...defaultDownloadDisplayOptions,
      },
      ...config
    });
  }

  const downloadDocx = async (code: string, downloadScope: DownloadMultiScope) => {
    if (!canAccessDownloads) {
      dispatch(openUpgradeModal(['features', 'data_download']));
      return;
    }
    const type = 'docx';
    track(type, code);

    return getMultiScopeReport(surveyId, code as SupportedReportCodes, {
      ...downloadScope,
      displayUserInput: true,
    }).catch((e) => setMessage(e.message));
  };

  const downloadXlsxOrCsv: DownloadXlsxOrCsv = async ({
    type,
    code,
    visibility = VisibilityStatus.Include,
    utrvStatus = UtrvStatus.Verified,
    downloadDisplayOptions = defaultDownloadDisplayOptions,
    scopeType,
  }) => {
    if (!surveyId) {
      return;
    }

    const downloadType = DownloadType.Csv === type ? 'csv' : 'xlsx';
    track(type, code);

    if (code === 'sdg') {
      // SDG contribution download
      const url = G17Client.getDownloadUrl(`reports/initiative/${initiativeId}/scorecard/${surveyId}`);
      return G17Client.downloadFile(url);
    }

    return G17Client.downloadSurveySimple({
      surveyId,
      type: downloadType,
      downloadScope: {
        scope: getDownloadMultiScope(code, scopeType),
        visibility,
        ...convertToUtrvStatuses(utrvStatus),
        ...downloadDisplayOptions,
      },
    }).catch((e) => setMessage(e.message));
  };


  const items = cards.map(card => {
    const group = getGroup('frameworks', card.code) || getGroup('standards', card.code);

    const isEnabled = isGroupEnabled(group, scopeConfig);

    return {
      ...card,
      src: group?.src,
      disabled: !isEnabled,
      tooltip: !isEnabled ? GroupMessage.Disabled : undefined
    };
  });

  const currentSurvey = list.find(item => item._id === surveyId);
  const hasSurvey = list.some(item => item._id)

  const goToCustomDownloads = () => {
    const url = generateUrl(ROUTES.DOWNLOADS_CUSTOM, { initiativeId });
    history.push(url);
  }

  const getTotals = (type: string) => {
    const card = cards.find(c => c.code === type);
    if (card?.versionGroupCode) {
      const versions = getSortedVersions(card.versionGroupCode);
      const [latestVersion, prevVersion] = versions;
      if (cardsTotals?.[latestVersion.code]?.total) {
        return cardsTotals?.[latestVersion.code];
      }
      return cardsTotals?.[prevVersion.code] ?? INITIAL_TOTALS;
    }
    return cardsTotals?.[type] ?? INITIAL_TOTALS;
  }

  const getTotalsByVersionCode = (versionCode: string) => {
    return cardsTotals?.[versionCode] ?? INITIAL_TOTALS;
  }

  const renderSDGImpactReport = () => {
    if (isRestricted) {
      return null;
    }

    return (
      <DownloadCard
        aside={false}
        item={{
          name: 'SDG Impact report',
          code: 'ctl',
          src: getGroup('standards', 'sdg')?.src,
        }}
        initiativeId={initiativeId}
        noSurvey={!hasSurvey}
        isDisabled={!hasSurvey}
        totals={getTotals('ctl')}
      >
        <SDGImpactDownloadButton
          initiativeId={initiativeId}
          surveyId={surveyId}
          downloadXlsxOrCsv={downloadXlsxOrCsv}
          totals={getTotals('ctl')}
          defaultDownloadSettings={defaultDownloadSettings}
          defaultDownloadOptions={defaultDownloadOptions}
        />
      </DownloadCard>
    );
  };

  const renderForESGenome = () => {
    if (!isEsGenome) {
      return null;
    }

    return (
      <>
        <DownloadCard
          key='sgx_metrics'
          item={{
            code: 'sgx_metrics',
            name: 'Sustainability report',
            src: getGroup('standards', 'sgx_metrics')?.src,
          }}
          initiativeId={initiativeId}
          downloadButton={
            <SustainabilityDownloadButton
              requiredScope='sgx_metrics'
              modalTitleOptions={{
                word: { title: 'Sustainability Word report', tooltip: 'SGX and RegCo compliant Word report' },
              }}
              code={mainCardCode}
              initiativeId={initiativeId}
              surveyId={surveyId}
              disabled={!hasSurvey}
              defaultDownloadSettings={defaultDownloadSettings}
            />
          }
          noSurvey={!hasSurvey}
          isDisabled={!hasSurvey}
          totals={getTotals('sgx_metrics')}
        />
        <div className='download-card'>
          <section className='download-details shadow-none border-0 d-flex flex-column'>
            <div className='download-actions d-flex flex-column mt-0'>
              <div className={'strong download-card__heading'}>Download all {SURVEY.SINGULAR} data</div>
              <div className={'mt-3 mb-2 h6 strong'}>Data report</div>
              <DownloadButton
                disabled={!hasSurvey}
                block
                color='link'
                onClick={() => downloadXlsxOrCsv({ type: DownloadType.Xlsx })}
              >
                <i className='fas fa-file-excel mr-2 fa-lg' />
                Download all data to Excel
              </DownloadButton>
              <DownloadButton
                disabled={!hasSurvey}
                block
                color='link'
                onClick={() => downloadXlsxOrCsv({ type: DownloadType.Csv })}
              >
                <i className='fas fa-file-csv mr-2 fa-lg' />
                Download all data to CSV
              </DownloadButton>
              <Button
                className='w-100 text-center mt-4'
                color='primary'
                size='lg'
                onClick={() => goToCustomDownloads()}
              >
                Custom Reports
              </Button>
            </div>
          </section>
        </div>
      </>
    );
  };

  // render ctl specific report and all standards & frameworks report
  const renderSustainabilityReports = () => {
    if (!currentSurvey || isEsGenome || isRestricted) {
      return null;
    }

    return (
      <>
        {renderSDGImpactReport()}
        <DownloadCard
          key={mainCardCode}
          item={{
            code: mainCardCode,
            name: 'Sustainability report',
            icon: 'fa-thin fa-seedling text-ThemeSuccessMedium',
          }}
          initiativeId={initiativeId}
          downloadButton={
            <SustainabilityDownloadButton
              defaultDownloadSettings={defaultDownloadSettings}
              code={mainCardCode}
              initiativeId={initiativeId}
              surveyId={surveyId}
              disabled={!hasSurvey}
              totals={getTotals('all')}
              defaultDownloadOptions={defaultDownloadOptions}
            />
          }
          noSurvey={!hasSurvey}
          isDisabled={!hasSurvey}
          totals={getTotals('all')}
        />
        <DownloadCard
          aside={false}
          item={{
            name: 'Custom reports',
            code: 'custom',
            icon: 'fa-thin fa-file-chart-pie text-ThemeIconSecondary',
          }}
          initiativeId={initiativeId}
          noSurvey={!hasSurvey}
          isDisabled={!hasSurvey}
        >
          <DownloadButton className='doc-btn' color='secondary' disabled={!canAccessCombinedReport} outline={true} onClick={goToCustomDownloads}>
            Custom reports <i className='fas fa-file-download ml-2' />
          </DownloadButton>
        </DownloadCard>
      </>
    );
  };

  // render CT Starter reports and all standards & frameworks report
  const renderForCTStarter = () => {
    if (!currentSurvey || !isRestricted) {
      return null;
    }

    const scopePacks = [...scope.standards, ...scope.frameworks];
    const filteredItems = items.filter((item) => {
      if (scopePacks.includes(item.code)) {
        return true;
      }

      // Card can switch to a new version inside, therefore we also need to follow
      // same strategy and check if never is included, therefore we should also allow this code
      // as it will be replaced by newer groupCodeVersion
      if (item.versionGroupCode) {
        return getSortedVersions(item.versionGroupCode).some((s) => scopePacks.includes(s.code));
      }

      return false;
    });

    return (
      <>
        <DownloadCard
          item={{
            name: 'Sustainability report',
            code: EESG_TYPE,
            src: getGroup('frameworks', EESG_TYPE)?.src,
          }}
          initiativeId={initiativeId}
          noSurvey={!hasSurvey}
          isDisabled={!hasSurvey}
          totals={getTotals(EESG_TYPE)}
        >
          <EESGDownloadButton
            initiativeId={initiativeId}
            surveyId={surveyId}
            downloadXlsxOrCsv={downloadXlsxOrCsv}
            totals={getTotals(EESG_TYPE)}
            defaultDownloadSettings={defaultDownloadSettings}
          />
        </DownloadCard>
        {filteredItems.map((item) => (
          <DownloadCard
            key={item.code}
            item={item}
            initiativeId={initiativeId}
            downloadXlsxOrCsv={downloadXlsxOrCsv}
            downloadDocx={downloadDocx}
            noSurvey={!hasSurvey}
            isDisabled={!hasSurvey}
            totals={getTotals(item.code)}
            getTotals={getTotalsByVersionCode}
            surveyScope={currentSurvey.scope}
            defaultDownloadSettings={defaultDownloadSettings}
          />
        ))}
        <DownloadCard
          item={{
            name: 'Custom reports',
            code: 'custom',
            icon: 'fa-thin fa-file-chart-pie text-ThemeIconSecondary',
          }}
          initiativeId={initiativeId}
        >
          <SimpleTooltip text='Not available in your current plan'>
            <DownloadButton className='doc-btn' color='secondary' disabled={true} outline={true} onClick={goToCustomDownloads}>
              Custom reports <i className='fas fa-file-download ml-2' />
            </DownloadButton>
          </SimpleTooltip>
        </DownloadCard>
      </>
    );
  };

  const renderStandardsFrameworksPolicies = () => {
    if (!currentSurvey) {
      return null;
    }

    if (isRestricted) {
      return (
        <div className='mt-5'>
          <UpgradeScopePack isRestricted={isRestricted} onUpgrade={onUpgrade} />
        </div>
      );
    }

    return (
      <div className='download-container mt-3 py-3 px-4'>
        <div className='mt-3 mx-2 d-flex flex-wrap justify-content-between mb-5'>
          {canViewCSRD ? (
            <DownloadCard
              key={'csrd'}
              item={{
                code: 'csrd',
                name: 'CSRD aligned report',
                src: getGroup('standards', 'csrd')?.src,
              }}
              initiativeId={initiativeId}
              totals={getTotals('csrd')}
              surveyScope={currentSurvey.scope}
              getTotals={getTotalsByVersionCode}
              defaultDownloadSettings={defaultDownloadSettings}
              defaultDownloadOptions={defaultDownloadOptions}
            >
              <DownloadButton
                className='doc-btn'
                color='secondary'
                onClick={() => {
                  history.push(generateUrl(ROUTES.REPORT_EDITOR_LIST, { initiativeId, type: 'csrd' }));
                }}
                outline={true}
                disabled={!hasSurvey}
              >
                <span>View Reports <FeatureStability stability={'internal'} /></span>
              </DownloadButton>
            </DownloadCard>
          ) : null}

          {items.map((item) => (
            <DownloadCard
              key={item.code}
              item={item}
              initiativeId={initiativeId}
              downloadXlsxOrCsv={downloadXlsxOrCsv}
              downloadDocx={downloadDocx}
              downloadXhtml={downloadXhtml}
              noSurvey={!hasSurvey}
              isDisabled={!hasSurvey}
              totals={getTotals(item.code)}
              surveyScope={currentSurvey.scope}
              getTotals={getTotalsByVersionCode}
              defaultDownloadSettings={defaultDownloadSettings}
              defaultDownloadOptions={defaultDownloadOptions}
            />
          ))}
          <DownloadCard
            key={otherReportCard.code}
            item={otherReportCard}
            initiativeId={initiativeId}
            downloadXlsxOrCsv={downloadXlsxOrCsv}
            downloadDocx={downloadDocx}
            noSurvey={!hasSurvey}
            isDisabled={!hasSurvey}
            getTotals={getTotalsByVersionCode}
            defaultDownloadSettings={defaultDownloadSettings}
            defaultDownloadOptions={defaultDownloadOptions}
            metricGroups={metricGroups}
          />
        </div>
      </div>
    );
  };

  const onReportChange = useCallback(
    (surveyId: string) => {
      history.push(generateUrl(ROUTES.DOWNLOADS, { initiativeId, surveyId }));
    },
    [history, initiativeId]
  );

  if (!canAccessDownloadsByRole) {
    return <NotAuthorised />
  }

  return (
    <Dashboard className='downloads'>
      {isLoading ? <BlockingLoader /> : null}
      {error ? <QueryError error={error} type={'danger'} /> : null}
      <DashboardSectionTitle
        title='Downloads'
        buttons={[
          <ReportSwitcherContainer
            key='report-switcher'
            initiativeId={initiativeId}
            selectedSurveyId={surveyId}
            onChange={onReportChange}
            showAutomaticReport={false}
            disabled={!hasSurvey}
          />,
        ]}
      />
      <DashboardSection title='Sustainability report' headingStyle={4}>
        <LoadingPlaceholder height={77} isLoading={!list} count={1} className='mt-4'>
          <div className='download-container border-0 py-3 px-4'>
            <div className='mx-2 pt-2 d-flex align-items-start flex-wrap justify-content-between'>
              {renderSustainabilityReports()}
              {renderForCTStarter()}
              {renderForESGenome()}
            </div>
          </div>
        </LoadingPlaceholder>
        {currentSurvey ? (
          <LoadingPlaceholder height={77} isLoading={!list} count={1}>
            <h4>Standards, Frameworks & Policies</h4>
            {renderStandardsFrameworksPolicies()}
          </LoadingPlaceholder>
        ) : null}
      </DashboardSection>
    </Dashboard>
  );
}
