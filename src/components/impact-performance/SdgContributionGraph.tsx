/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { Component, RefObject } from 'react';
import 'react-circular-progressbar/dist/styles.css';
import { ScorecardState } from '../../types/scorecard';
import { SdgChart } from './SdgChart';
import { DashboardSection } from '../dashboard';
import { Button } from 'reactstrap';
import * as htmlToImage from 'html-to-image';
import { SdgContributionShareModal } from './SdgContributionShareModal';
import { SdgChangeFn } from './SdgCharProps';
import { getAnalytics } from '../../services/analytics/AnalyticsService';
import { AnalyticsEvents } from '../../services/analytics/AnalyticsEvents';
import config from '../../config';
import { SdgContributionHeader } from './SdgContributionHeader';
import { AINarrative } from '../../apps/common/components/ai/AINarrative';
import './styles.scss';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';

interface Props {
  readOnly?: boolean;
  showAITools?: boolean;
  surveyId?: string;
  reportSwitcher?: JSX.Element;
  sdgCode?: string;
  showDetail?: boolean;
  handleChangeSDG?: SdgChangeFn;
  initiativeId: string;
  toggleMaterialityBtn?: JSX.Element;
  canAccess: boolean;
  scorecardState: ScorecardState;
}

interface State {
  ref: RefObject<any>;
  chartImagePrepare: boolean;
  shareModal: boolean;
}

export class SdgContributionGraph extends Component<Props, State> {

  state: State = {
    ref: React.createRef(),
    chartImagePrepare: false,
    shareModal: false,
  };

  toggleShareModal = () => {
    this.setState({ shareModal: !this.state.shareModal });
  }

  componentDidMount() {
    if (this.props.sdgCode) {
      this.scrollToRef(this.props.sdgCode);
    }
  }

  componentDidUpdate(prevProps: Readonly<Props>, prevState: Readonly<State>) {
    if (prevProps.sdgCode !== this.props.sdgCode) {
      this.scrollToRef(this.props.sdgCode);
    }
  }

  scrollToRef = (sdgCode?: string) => {
    if (!sdgCode) {
      return;
    }
    const { ref } = this.state;
    if (ref.current) {
      ref.current.scrollIntoView({
        behavior: 'smooth',
        inline: 'nearest',
      });
    }
  }

  render() {
    const {
      scorecardState,
      sdgCode,
      surveyId,
      reportSwitcher,
      initiativeId,
      showDetail,
      canAccess,
      showAITools = false,
    } = this.props;
    if (scorecardState.errored) {
      return null;
    }

    if (!scorecardState.loaded || !scorecardState.main.loaded) {
      return <DashboardSection>
        <LoadingPlaceholder height={505} className='d-block background-ThemeTextWhite' />
        <span ref={this.state.ref} />
      </DashboardSection>;
    }

    const scorecard = scorecardState.main.data;
    return (
      <div className='impactPerformanceContainer d-none d-lg-block'>
        <SdgContributionHeader title={'SDGs Contributions'} score={scorecard.scorecard.actual} />
        <DashboardSection padding={2} className={this.state.chartImagePrepare ? 'pre-screenshot-class' : ''}>
          {surveyId && showAITools ? (
            <div className='px-3'>
              <AINarrative initiativeId={initiativeId} surveyId={surveyId} />
            </div>
          ) : null}

          {reportSwitcher ? reportSwitcher : null}

          <SdgChart
            ref={this.state.ref}
            scorecard={scorecard}
            initiativeId={initiativeId}
            sdgCode={sdgCode}
            surveyId={surveyId}
            showDetail={showDetail}
            handleChangeSDG={this.props.handleChangeSDG}
            toggleMaterialityBtn={this.props.toggleMaterialityBtn}
          />
          <div className='d-flex justify-content-between chart-action-buttons'>
            <Button color='link-secondary' onClick={() => window.open(config.faqsURL, '_blank')}>
              FAQs
            </Button>
            {canAccess ? (
              <>
                <div className='text-right'>
                  <Button color='link-secondary' onClick={this.toggleShareModal}>
                    Share<i className='ml-2 fa fa-share' />
                  </Button>
                </div>
                <SdgContributionShareModal
                  handleDownloadImage={() => {
                    this.setState({ chartImagePrepare: true }, () => {
                      htmlToImage
                        .toBlob(this.state.ref.current, { backgroundColor: '#FFFFFF' })
                        .then((blob) => {
                          if (blob) {
                            window.saveAs(blob, 'sdg-contribution-graph.png');
                            getAnalytics().track(AnalyticsEvents.SDGGraphDownload, { initiativeId, surveyId });
                          }
                        })
                        .then(() => this.setState({ chartImagePrepare: false }));
                    });
                  }}
                  isOpen={this.state.shareModal}
                  toggle={this.toggleShareModal}
                />
              </>
            ) : (
              <></>
            )}
          </div>
        </DashboardSection>
      </div>
    );
  }
}
