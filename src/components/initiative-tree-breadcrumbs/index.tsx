/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import { Button } from 'reactstrap';
import { InitiativePlain } from '../../types/initiative';
import { ChildrenTree, Tree } from '../../types/tree';
import { DashboardRow } from '../dashboard';
import './styles.scss';

interface InitiativeTreeBreadcrumbsProps {
  initiativeTree: Tree;
  currentInitiative: InitiativePlain;
  handleClick: (id: string) => void;
}

const InitiativeTreeBreadcrumbs = ({ initiativeTree, handleClick, currentInitiative }: InitiativeTreeBreadcrumbsProps) => {

  const breadcrumbs: (Tree | ChildrenTree)[] = [];

  const findInitiativeInTree = (tree: Tree | ChildrenTree, id: string): Tree | ChildrenTree | undefined => {
    if (tree.id === id) {
      return tree;
    }
    if (tree.children && tree.children.length > 0) {
      return tree.children.find(c => {
        const found = findInitiativeInTree(c, id);
        if (found) {
          breadcrumbs.push(c);
        }
        return found;
      })
    }
    return undefined;
  }

  const currentTree = findInitiativeInTree(initiativeTree, currentInitiative._id);

  if (!currentTree || breadcrumbs.length <= 1) {
    return <></>;
  }

  return <DashboardRow className='breadcrumbs'>
    {breadcrumbs.reverse().map((t, i) => (
      <span key={`initiative-breadcrumbs-${currentInitiative._id}-${i}`}>
        <i className={`fa ${i === 0 ? 'fa-folder ml-2' : 'fa-caret-right'} mr-2`} />
        <Button
          color='link'
          size='sm'
          className='pl-0'
          active={i === breadcrumbs.length - 1}
          disabled={i === breadcrumbs.length - 1}
          onClick={() => {
            const [id] = t.id.split('_');
            handleClick(id);
          }}>
          {t.name}</Button>
      </span>
    ))}
  </DashboardRow>;
}

export default InitiativeTreeBreadcrumbs;
