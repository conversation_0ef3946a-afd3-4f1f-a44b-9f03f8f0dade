/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { SurveyListItem, SurveyType } from '../../types/survey';
import { getFormattedSurveyDate, isAggregatedSurvey } from '@utils/survey';
import { Option, SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { SURVEY } from '@constants/terminology';
import { getFilterOption } from '../../molecules/select/utils';
import { sortEffectiveDateDesc } from '@utils/sort';

export interface SurveyListClasses {
  container?: string;
  select?: string;
}

interface SurveyListDropdownProps {
  selectedItem?: SurveyListItem;
  handleDropdownSelect?: (item: SurveyListItem) => void;
  showAutomaticReport?: boolean;
  surveyList: SurveyListItem[];
  disabled?: boolean;
  classes?: SurveyListClasses;
  getItemDisplayText?: (item: SurveyListItem) => string;
}

const ReportListMap = {
  combinedReports: `Combined ${SURVEY.CAPITALIZED_PLURAL}`,
  reports: `${SURVEY.CAPITALIZED_PLURAL}`,
};

type ReportListMapKey = keyof typeof ReportListMap;

const getIcon = (key: SurveyType | undefined, completedDate?: string) => {
  if (isAggregatedSurvey(key)) {
    return 'fa fa-object-exclude icon mr-2';
  }
  return completedDate ? 'fa fa-check-circle mr-2' : 'fa fa-circle mr-2';
};

type SurveyListDropdownType = {
  [key in ReportListMapKey]: SurveyListItem[];
};

export const SurveyListDropdown = (props: SurveyListDropdownProps) => {
  const {
    selectedItem,
    showAutomaticReport = true,
    handleDropdownSelect = () => {},
    disabled = false,
    classes,
    getItemDisplayText = getFormattedSurveyDate,
  } = props;

  const list = props.surveyList;
  if (list.length === 0 || !selectedItem) {
    return null;
  }

  const filteredReports = list.reduce(
    (acc, survey) => {
      if (isAggregatedSurvey(survey.type)) {
        acc.combinedReports.push(survey);
        return acc;
      }
      if (!survey.completedDate && survey._id) {
        acc.reports.push(survey);
      }
      if (survey.completedDate) {
        acc.reports.unshift(survey);
      }
      return acc;
    },
    { reports: [], combinedReports: [] } as SurveyListDropdownType
  );

  const currentTotal = list.find((data) => !data._id);
  const selectedDisplay =
    currentTotal?._id === selectedItem._id ? selectedItem.name : getFormattedSurveyDate(selectedItem);

  const getSurveyOptions = () => {
    const options = [];
    if (currentTotal && showAutomaticReport) {
      options.push({
        searchString: 'Current Total',
        label: (
          <span>
            <i className={'fas fa-shapes mr-2'} />
            Current Total
          </span>
        ),
        value: '',
      });
    }
    const reports = Object.keys(filteredReports).reduce((acc, key) => {
      const reportType = key as ReportListMapKey;
      const reportList = filteredReports[reportType].sort(sortEffectiveDateDesc);
      if (reportList.length) {
        acc.push(
          { label: ReportListMap[reportType], value: 'survey-type', isDisabled: true },
          ...reportList.map((report) => {
            const displayText = getItemDisplayText(report);
            return {
              searchString: displayText,
              label: (
                <div className='text-truncate'>
                  <i className={getIcon(report.type, report.completedDate)} />
                  {displayText}
                </div>
              ),
              value: report._id,
              tooltip: displayText,
            };
          })
        );
      }
      return acc;
    }, [] as Option[]);
    options.push(...reports);
    return options;
  };

  const onSelect = (op: Option | null) => {
    const item = list.find((report) => report._id === op?.value);
    if (item) {
      handleDropdownSelect(item);
    }
  };

  return (
    <SimpleTooltip text={disabled ? `You need to complete at least one ${SURVEY.SINGULAR}.` : ''}>
      <SelectFactory
        value={{
          label: (
            <span className={`${classes?.select}`}>
              <i className={getIcon(selectedItem.type, selectedItem.completedDate)} />
              {selectedDisplay}
            </span>
          ),
          value: selectedItem._id,
        }}
        selectType={SelectTypes.SingleSelect}
        placeholder={`Select ${SURVEY.SINGULAR}`}
        options={getSurveyOptions()}
        onChange={(option) => onSelect(option)}
        isDisabled={disabled}
        isSearchable
        isTransparent
        minWidth={250}
        filterOption={getFilterOption<Option<string | null> | null>()}
      />
    </SimpleTooltip>
  );
};
