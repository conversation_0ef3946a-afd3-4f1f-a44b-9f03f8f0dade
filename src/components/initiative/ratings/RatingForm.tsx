/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import React, { useEffect, useMemo, useState } from 'react';
import { InitiativeRating, RatingAgency, RatingAgencyRating } from '../../../types/initiative';
import { SubmitButton } from '../../button/SubmitButton';
import G17Client from '../../../services/G17Client';
import { SelectFactory, SelectTypes } from '../../../molecules/select/SelectFactory';
import { FormGenerator, FieldProps } from '@g17eco/molecules/form';
import { Label } from 'reactstrap';
import { naturalSort } from '../../../utils';
import { getMonth, getYear, setDate } from '../../../utils/date';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';

export interface RatingFormProps {
  initiativeId: string;
  ratings: RatingAgencyRating[],
  itemCode: string;
  itemId?: string;
  onSubmit?: () => Promise<any>;
}

interface Form {
  _id?: string;
  code: string;
  rating: string;
  link?: string;
  linkText?: string;
  year?: null | number | string;

  submitting: boolean
  errored?: boolean;
  errorMessage: string;
}

const getNoOptionsMessage = () => 'No Rating Providers available';

interface AgenciesState {
  loaded: boolean,
  data: RatingAgency[],
  errorMessage?: string
}

const getInitialState = (): Form => ({
  errored: false,
  errorMessage: '',
  code: '',
  rating: '',
  year: null,
  submitting: false
});

export const RatingForm = (props: RatingFormProps) => {

  const {
    ratings = [],
    itemCode,
    itemId,
    initiativeId,
    onSubmit = async () => {}
  } = props;

  const [agencies, setAgencies] = useState<AgenciesState>({ loaded: false, data: [] });
  const [formData, setFormData] = useState<Form>(getInitialState());

  useEffect(() => {
    G17Client.getRatingAgencies()
      .then((data) => setAgencies({ data, loaded: true }))
      .catch(e => setAgencies({ data: [], errorMessage: e.message, loaded: true }))
  }, [setAgencies]);

  const isEditMode = !!itemCode || !!itemId;

  const agenciesOptions = useMemo(() => agencies.data.map(i => ({
    value: i.code,
    label: i.title
  })), [agencies]);

  const selectedAgency = agenciesOptions.find(o => o.value === formData.code) ?? null;
  const selectedValue = selectedAgency?.value;

  const formFields: FieldProps<Form>[] = useMemo(() => {
    return [
      {
        code: 'rating',
        type: 'text',
        required: true,
        label: selectedValue === 'iso' ? 'ISO number' : 'Rating',
      },
      {
        code: 'year',
        type: 'number',
        label: 'Year of certification',
        placeholder: 'Year of certification',
        min: 1980,
        max: (new Date().getFullYear()),
        isValid: (fieldsForm) => {
          const value = fieldsForm.year;
          if (!value) {
            return { message: '' };
          }

          const year = Number(value);
          if (year >= 1980 && year <= (new Date().getFullYear())) {
            return { valid: true, message: '' };
          }

          return { valid: false, message: 'Please provide valid value'};
        },
      },
      {
        code: 'link',
        type: 'text',
        label: 'Link',
      },
      {
        code: 'linkText',
        type: 'textarea',
        label: 'Link description',
        placeholder: 'Short link description',
      },
    ] as FieldProps<Form>[];
  }, [selectedValue]);


  useEffect(() => {
    if (itemId || itemCode) {
      const item = ratings.find((r) => itemId ? r._id === itemId : r.code === itemCode) ?? { date: undefined };
      setFormData({
        ...getInitialState(),
        ...item,
        year: item.date ? getYear(item.date) : null
      });
    } else {
      setFormData(getInitialState())
    }

  }, [itemCode, itemId, ratings])

  const onChange = (e: React.ChangeEvent<any>) => setFormData({
    ...formData,
    [e.target.name]: e.target.value,
  });


  const handleError = (e: Error) => {
    setFormData({
      ...formData,
      errored: true,
      errorMessage: e.message,
      submitting: false
    })
  };

  const getActionPromise = function (data: InitiativeRating) {
    if (isEditMode) {
      return G17Client.editRating(initiativeId, data);
    }
    return G17Client.updateRatings(initiativeId, [...ratings, data]);
  };

  const onSuccess = () => {
    setFormData({ ...formData, submitting: false });
    onSubmit();
  };

  const handleSubmit = () => {

    const data: InitiativeRating = {
      _id: formData._id,
      code: formData.code,
      link: formData.link,
      linkText: formData.linkText,
      rating: formData.rating,
      date: formData.year ? setDate({
        year: formData.year,
        month: getMonth()
      }).toISOString() : undefined,
    }

    setFormData({
      ...formData,
      errored: false,
      errorMessage: '',
      submitting: true
    })

    return getActionPromise(data).then(onSuccess).catch(handleError)
  }

  const selectInputId = 'ratingSelectInput';


  const handleDeleteSubmit = () => {
    return G17Client.deleteRating(initiativeId, itemCode, itemId).then(onSuccess).catch(handleError)
  };

  const isFormValid = !formData.code || formFields.some((f) => f.required && !formData[f.code as keyof Form]);
  return (
    <fieldset disabled={formData.submitting}>
      <Label for={selectInputId}>Rating provider</Label>
      {agencies.loaded ?
        <SelectFactory<string>
          selectType={SelectTypes.SingleSelect}
          id={selectInputId}
          placeholder={'Rating Provider'}
          isDisabled={isEditMode || formData.submitting}
          className='w-100 valueList-react-select'
          onChange={(option) => option ? setFormData({
            ...formData,
            code: option.value
          }) : undefined}
          noOptionsMessage={getNoOptionsMessage}
          value={selectedAgency}
          isSearchable={true}
          options={agenciesOptions.sort((a, b) => naturalSort(a.label, b.label))}
        /> : <LoadingPlaceholder height={36} />}
      <br />
      <FormGenerator fields={formFields} form={formData} updateForm={onChange} />

      <div className={`mt-5 d-flex ${isEditMode ? 'justify-content-between' : 'justify-content-end'}`}>
        {isEditMode ? <SubmitButton className='mr-3' color='danger' onClick={handleDeleteSubmit}>
          Delete
        </SubmitButton> : null}

        <SubmitButton disabled={isFormValid} onClick={handleSubmit}>
          <>{isEditMode ? 'Update' : 'Submit'}</>
        </SubmitButton>
      </div>
    </fieldset>
  );
}
