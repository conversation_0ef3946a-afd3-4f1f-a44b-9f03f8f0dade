/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { useAppSelector } from '../../../reducers';
import { MessageFormProps } from '../types';
import { ContributorVerifierMessageForm } from './ContributorVerifierMessageForm';
import { useGetSurveyUsersQuery } from '@api/initiative-stats';
import { skipToken } from '@reduxjs/toolkit/query';
import { QueryWrapper } from '@components/query/QueryWrapper';
import { hasStakeholderRole, hasVerifierRole, UserMin } from '@constants/users';

interface SurveyMessageFormProps extends MessageFormProps {
  surveyId?: string;
}

export const SurveyMessageForm = (props: SurveyMessageFormProps) => {
  const { surveyId } = props;

  const initiativeId = useAppSelector(state => state.initiative.loaded ? state.initiative.data._id : undefined);
  const surveyUsers = useGetSurveyUsersQuery(initiativeId && surveyId ? { initiativeId, surveyId } : skipToken);

  return (
    <QueryWrapper
      query={surveyUsers}
      onSuccess={(users) => {
        const groupedUsers = users.reduce((acc, user) => {
          if (hasStakeholderRole(user.delegationRoles)) {
            acc.contributor.push(user);
          }
          if (hasVerifierRole(user.delegationRoles)) {
            acc.verifier.push(user);
          }
          return acc;
        }, { contributor: [] as UserMin[], verifier: [] as UserMin[] });

        return <ContributorVerifierMessageForm
          {...props}
          contributors={groupedUsers.contributor}
          verifiers={groupedUsers.verifier}
        />
      }}
    />
  );
}
