/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import './styles.scss';
import React, { useCallback, useEffect, useState } from 'react';
import CompanyInfo from './CompanyInfo';
import SectorInfo from './SectorInfo';
import CompleteSignUp from './CompleteSignUp';
import CompleteActivation from './CompleteActivation';
import Dashboard, { DashboardSection } from '../dashboard';
import SignUpForm from './SignUpForm';
import LoadingBar from './LoadingBar';
import G17Client from '../../services/G17Client';
import CustomStepper from './CustomStepper';
import { useLocation, useParams } from 'react-router-dom';
import SDGIcon from '../sdg-icon/sdg-icon';
import { SettingStorage } from '../../services/SettingStorage';
import { companyTrackerRouteMatcher } from '../../routes/appRootMatcher';
import { authWithCredentials } from '../../services/OktaClient';
import ReferralInfo from './ReferralInfo';
import { ConfigFormData, OnboardingStepperStateData } from './types';
import { AnalyticsEvents } from '../../services/analytics/AnalyticsEvents';
import { getAnalytics } from '../../services/analytics/AnalyticsService';
import { useUserGeoLocation } from '../../hooks/useUserGeoLocation';
import { getStepTitle, OnboardingStep, ReferralInfoType } from '../../types/onboarding';
import { AppFlavour, getDefaultConfig } from '../../config/app-config';
import { AppConfig } from '../../types/app';
import { IssuerCompanyOnboardOrNot } from './IssuerCompanyOnboardOrNot';
import { SGXReferrerCodeIssuerLookup } from './SGXReferrerCodeIssuerLookup';
import { QUESTION } from '@constants/terminology';
import { BasicAlert } from '@g17eco/molecules/alert';

export interface Address {
  line1: string,
  line2?: string,
  city?: string,
  postcode: string,
}

const SINGAPORE_COUNTRY_CODE = 'SG';

const defaultOnboardingSteps = [
  OnboardingStep.CompanyInfo,
  OnboardingStep.SectorInfo,
  OnboardingStep.ReferralInfo,
  OnboardingStep.Signup,
  OnboardingStep.Activation,
  OnboardingStep.Complete
];

interface Props {
  appConfig: AppConfig
}

export const SGXESGenomeOnboarding = ({ appConfig }: Props) => {
  const location = useLocation<ReferralInfoType>();

  const searchParams = new URLSearchParams(location.search);
  const referralInfo = {
    referralCode: searchParams.get('referralCode'),
    referralName: searchParams.get('referralName'),
  };

  const { referralCode, referralName } = location.state ?? referralInfo;
  const { activation } = useParams<{ activation?: string }>();

  const [stepperStateData, setStepperStateData] = useState<OnboardingStepperStateData>({});
  const [formData, setFormData] = useState<ConfigFormData>({
    firstName: '',
    surname: '',
    password: '',
    rPassword: '',
    email: '',
    initiativeName: '',
    initiativeCountryCode: '',
    registrationNumber: '',
    referenceCompany: referralName ?? '',
    referralCode: referralCode ?? '',
    industry: {},
    financialEndDate: { month: '-', day: '-' },
    address: {
      line1: '',
      line2: '',
      city: '',
      postcode: '',
    },
    unitConfig: {
      currency: 'SGD',
    },
  });

  const userLocation = useUserGeoLocation({});

  const updateForm = useCallback((name: string, value: any) => {
    setFormData((form) => ({
      ...form,
      [name]: value,
    }));
  }, [setFormData]);

  useEffect(() => {
    if (formData.initiativeCountryCode) {
      return;
    }

    if (userLocation) {
      return updateForm('initiativeCountryCode', userLocation)
    }

    if (getDefaultConfig().flavour === AppFlavour.SINGAPORE) {
      return updateForm('initiativeCountryCode', SINGAPORE_COUNTRY_CODE);
    }

  }, [userLocation, formData.initiativeCountryCode, updateForm])

  const [isSubmitting, setSubmitting] = useState(false);
  const [errorMessage, setMessage] = useState('');
  const [activeStep, setActiveStep] = React.useState<OnboardingStep>(appConfig.settings.onboardingSteps?.[0] ?? OnboardingStep.CompanyInfo);
  const onboardingSteps = appConfig.settings.onboardingSteps ?? defaultOnboardingSteps;


  useEffect(() => {
    // Override title with app name
    if (appConfig) {
      document.title = `${appConfig.name} - ${getStepTitle(activeStep)}`
    }
  }, [activeStep, appConfig])

  const trackStep = useCallback((nextStep: OnboardingStep) => {
    getAnalytics().track(AnalyticsEvents.OnboardingStep, {
      isCTL: true,
      step: nextStep,
      stepName: getStepTitle(nextStep),
    })
  }, []);

  useEffect(() => {
    if (activation === 'activate') {
      // It's actually complete step we want now (after activation)
      setActiveStep(OnboardingStep.Complete);
      trackStep(OnboardingStep.Complete);
    }
  }, [activation, setActiveStep, trackStep]);

  const handlePrevious = useCallback(() => {
    const currentIndex = onboardingSteps.findIndex((step) => step === activeStep);
    const prevStep = Math.max(currentIndex - 1, 0);
    setActiveStep(onboardingSteps[prevStep]);
    trackStep(onboardingSteps[prevStep]);
  }, [onboardingSteps, activeStep, setActiveStep, trackStep]);

  const handleNext = useCallback(() => {
    const currentIndex = onboardingSteps.findIndex((step) => step === activeStep);
    const nextStep = Math.min(currentIndex + 1, onboardingSteps.length - 1);
    setActiveStep(onboardingSteps[nextStep]);
    trackStep(onboardingSteps[nextStep]);
  }, [onboardingSteps, activeStep, setActiveStep, trackStep]);

  const handleSubmit = useCallback(async () => {
    setMessage('')
    setSubmitting(true)
    try {

      const { survey } = await G17Client.createSGXESGenomeSurvey(formData);
      // Pre-select organization for new user
      SettingStorage.setItem(companyTrackerRouteMatcher.storageKey, survey.initiativeId);

      const { email: username, password } = formData;
      await authWithCredentials({ username, password });

      setSubmitting(false)
      handleNext()
    } catch (e) {
      setSubmitting(false)
      setMessage(e.message)
    }
  }, [formData, setSubmitting, handleNext, setMessage]);

  return (
    <div className='company-tracker-light-onboarding' style={{ marginBottom: '100px' }}>
      <CustomStepper steps={onboardingSteps} current={activeStep} />
      <Dashboard className='mt-4'>
        <DashboardSection>
          <BasicAlert type='danger'>{errorMessage}</BasicAlert>
          {isSubmitting ?
            <LoadingBar /> :
            <OnboardingStepForm
              activeStep={activeStep}
              appConfig={appConfig}
              formData={formData}
              updateForm={updateForm}
              handleNext={handleNext}
              handlePrevious={handlePrevious}
              handleSubmit={handleSubmit}
              stepperStateData={stepperStateData}
              setStepperStateData={setStepperStateData}
            />
          }
        </DashboardSection>
        {activeStep === OnboardingStep.Complete ? <SDGImpact /> : null}
      </Dashboard>
    </div>
  );
}

interface OnboardingStepFormProps {
  activeStep: OnboardingStep;
  appConfig: AppConfig;
  formData: ConfigFormData;
  updateForm: (name: string, value: any) => void;
  handleNext: () => void;
  handlePrevious: () => void;
  handleSubmit: () => void;
  stepperStateData: OnboardingStepperStateData;
  setStepperStateData: React.Dispatch<React.SetStateAction<OnboardingStepperStateData>>;
}

const OnboardingStepForm = (props: OnboardingStepFormProps) => {
  switch (props.activeStep) {
    case OnboardingStep.CompanyInfo:
      return <CompanyInfo {...props} />
    case OnboardingStep.SGXReferrerCodeIssuerLookup:
      return <SGXReferrerCodeIssuerLookup {...props} />
    case OnboardingStep.IssuerCompanyOnboardOrNot:
      return <IssuerCompanyOnboardOrNot {...props} />
    case OnboardingStep.SectorInfo:
      return <SectorInfo {...props} />
    case OnboardingStep.ReferralInfo:
      return <ReferralInfo {...props} />
    case OnboardingStep.Signup:
      return <SignUpForm {...props} handleNext={props.handleSubmit} />
    case OnboardingStep.Activation:
      return <CompleteSignUp />
    case OnboardingStep.Complete:
      return <CompleteActivation appConfig={props.appConfig} />;
    default:
      return <BasicAlert type='danger'>
        There was a problem rendering the requested page ({props.activeStep}). Please refresh the page and try again.
      </BasicAlert>;
  }
};

const SDGImpact = () => {
  return <DashboardSection className='map-sdgs'>
    <div className='title-container mb-3'>
      <div className='h2'>Mapped to many...</div>
      <p>Our {QUESTION.PLURAL} are mapped to the 17 UN Sustainable Development Goals (SDGs). If you see several icons next to a {QUESTION.SINGULAR}, it means with one {QUESTION.SINGULAR} you are impacting several SDGs.</p>
    </div>
    <div className='mt-4'>
      {[1, 8, 10, 12, 16].map((o) => <SDGIcon className='mr-2' code={o} key={o} width={65} height={65} />)}
    </div>
  </DashboardSection>
}
