/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React, { useEffect, useMemo, useState } from 'react';
import { Redirect, useRouteMatch } from 'react-router-dom';
import { generateUrl } from '../../routes/util';
import { ROUTES } from '../../constants/routes';
import { activateUser } from '../../actions/user';
import { getCurrentUser } from '../../selectors/user';
import { AppConfig } from '../../types/app';
import G17Client from '../../services/G17Client';
import Dashboard, { DashboardSection } from '../dashboard';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { useAppSelector } from '../../reducers';
import { ActivationToken, OnboardingStep } from '../../types/onboarding';
import './styles.scss';
import { showLogin } from '../../actions/login';

interface ActivationProps {
  appConfig: AppConfig
}

export const Activation = ({ appConfig }: ActivationProps) => {
  const user = useAppSelector(getCurrentUser)
  const isActive = user?.active;
  const match = useRouteMatch<{ token: string }>();

  const [token, setToken] = useState<{ loaded: boolean, errorMessage: string, data?: ActivationToken }>({
    loaded: false,
    errorMessage: '',
  });
  const tokenId = match.params.token;

  useEffect(() => {
    if (!tokenId) {
      return;
    }
    G17Client.checkActivationToken(tokenId)
      .then((tokenData) => setToken({
        data: tokenData,
        loaded: true,
        errorMessage: ''
      }))
      .catch((e) => setToken({ loaded: false, errorMessage: e.message }));
  }, [tokenId]);

  const returnPath = useMemo(() => generateUrl(ROUTES.COMPANY_TRACKER_ONBOARDING, {
    onboardingPath: appConfig.onboardingPath,
    activeStep: OnboardingStep.EmailDomainCompanyOnboardOrNot
  }), [appConfig]);

  useEffect(() => {
    if (!user) {
      showLogin(window.location.toString())
      return
    }
    if (token.data) {
      activateUser({}, token.data.userId, token.data.token, returnPath);
    }
  }, [token.data, returnPath, user]);

  if (isActive) {
    // User is already activated, let's just move on
    return <Redirect to={returnPath} />;
  }

  if (!tokenId) {
    return <ActivationInvalid />;
  }

  return <ActivatingLoader />;
}

const ActivatingLoader = () => {
  return (
    <Dashboard className='mt-4'>
      <DashboardSection>
        <BlockingLoader />
        <section className='title-container'>
          <div className='h4 text-strong'>Activating...</div>
        </section>
      </DashboardSection>
    </Dashboard>
  );
}

const ActivationInvalid = () => {
  return (
    <Dashboard className='mt-4'>
      <DashboardSection>
        <section className='title-container'>
          <div className='h4 text-strong'>Invalid Activation</div>
          <p>Please click on your activation e-mail again or contact our support.</p>
        </section>
      </DashboardSection>
    </Dashboard>
  );
}
