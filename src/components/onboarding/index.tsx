/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { useCallback, useEffect, useMemo } from 'react';
import { useHistory, useLocation, useParams } from 'react-router-dom';
import { AppConfig } from '../../types/app';
import { getAnalytics } from '../../services/analytics/AnalyticsService';
import { AnalyticsEvents } from '../../services/analytics/AnalyticsEvents';
import Dashboard, { DashboardSection } from '../dashboard';
import { getStepTitle, OnboardingStep } from '../../types/onboarding';
import CustomStepper from './partials/CustomStepper';
import { OnboardingStepForm } from './OnboardingForm';
import { generateUrl } from '../../routes/util';
import { ROUTES } from '../../constants/routes';
import { BlockingLoader } from '@g17eco/atoms/loader';
import './styles.scss';

const defaultOnboardingSteps = [
  OnboardingStep.Signup,
  OnboardingStep.VerifyEmail,
  OnboardingStep.EmailDomainCompanyOnboardOrNot,
  OnboardingStep.ReferralInfo,
  OnboardingStep.CompanyInfo,
  OnboardingStep.Complete,
];

interface OnboardingProps {
  appConfig: AppConfig
}

export const Onboarding = ({ appConfig }: OnboardingProps) => {

  const history = useHistory();
  const location = useLocation();

  const { onboardingPath, activeStep } = useParams<{ onboardingPath: string, activeStep?: OnboardingStep }>();

  const onboardingSteps = appConfig.settings.onboardingSteps ?? defaultOnboardingSteps;
  const currentIndex = useMemo(() => onboardingSteps.findIndex((step) => step === activeStep), [onboardingSteps, activeStep]);
  const prevStep = Math.max(currentIndex - 1, 0);
  const nextStep = Math.min(currentIndex + 1, onboardingSteps.length - 1);

  const referralInfo = useMemo(() => {
    const searchParams = new URLSearchParams(location.search);
    return {
      referralCode: searchParams.get('referralCode'),
      referralName: searchParams.get('referralName'),
    }
  }, [location.search]);

  const goToStep = useCallback((step?: OnboardingStep, replaceHistory = false) => {
    const searchParams = new URLSearchParams(location.search);
    const historyMethod = replaceHistory ? history.replace : history.push;
    historyMethod({
      pathname: generateUrl(ROUTES.COMPANY_TRACKER_ONBOARDING, { onboardingPath, activeStep: step }),
      search: searchParams.toString(),
    });
    if (step) {
      getAnalytics().track(AnalyticsEvents.OnboardingStep, {
        isCTL: true,
        step: step,
        stepName: getStepTitle(step),
      })
    }
  }, [onboardingPath, history, location.search]);

  useEffect(() => {
    if (!activeStep || !onboardingSteps.includes(activeStep)) {
      if (onboardingSteps[0]) {
        goToStep(onboardingSteps[0], true);
      }
    }
  }, [activeStep, onboardingSteps, goToStep]);

  useEffect(() => {
    if (activeStep) {
      document.title = `${appConfig.name} - ${getStepTitle(activeStep)}`
    }
  }, [appConfig, activeStep]);

  const handlePrevious = useCallback(() => {
    goToStep(onboardingSteps[prevStep]);
  }, [onboardingSteps, prevStep, goToStep]);

  const handleNext = useCallback(() => {
    goToStep(onboardingSteps[nextStep]);
  }, [onboardingSteps, nextStep, goToStep]);

  if (!activeStep) {
    return <BlockingLoader />;
  }

  return (
    <div className='company-tracker-onboarding' style={{ marginBottom: '100px' }}>
      <CustomStepper steps={onboardingSteps} current={activeStep} />
      <Dashboard className='mt-4'>
        <DashboardSection>
          <OnboardingStepForm
            appConfig={appConfig}
            referralInfo={referralInfo}
            activeStep={activeStep}
            goToStep={goToStep}
            handleNext={handleNext}
            handlePrevious={currentIndex <= 0 ? undefined : handlePrevious}
          />
        </DashboardSection>
      </Dashboard>
    </div>
  );
}
