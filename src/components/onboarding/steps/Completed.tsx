/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React, { useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { Button } from 'reactstrap';
import { generateUrl } from '../../../routes/util';
import { ROUTES } from '../../../constants/routes';
import { getDefaultConfig } from '../../../config/app-config';
import { BaseStepCompProps } from '../types';
import { DashboardSection } from '../../dashboard';
import SDGIcon from '../../sdg-icon/sdg-icon';
import { getAnalytics } from '../../../services/analytics/AnalyticsService';
import { AnalyticsEvents } from '../../../services/analytics/AnalyticsEvents';
import { QUESTION, SURVEY } from '@constants/terminology';

type Props = Pick<BaseStepCompProps, 'appConfig'>;

export const Completed = ({ appConfig }: Props) => {

  const history = useHistory();

  const redirect = () => {
    const returnPath = generateUrl(ROUTES.COMPANY_TRACKER_LIST, {
      rootAppPath: appConfig.rootAppPath ?? getDefaultConfig().branding.ctlApp.rootAppPath
    });

    history.push(returnPath);
  }

  useEffect(() => {
    getAnalytics().track(AnalyticsEvents.OnboardingCompleted);
  }, []);

  return <>
    <section className='title-container'>
      <div className='h1'>Done!</div>
      <div className='h4 text-strong mb-3'>Your account is now ready!</div>
    </section>
    <div className='mt-5 text-right mr-3'>
      <Button color='primary' onClick={redirect}>Continue to your {SURVEY.SINGULAR}</Button>
    </div>
    <DashboardSection className='mt-6 map-sdgs'>
      <div className='title-container mb-3'>
        <div className='h4 text-strong'>Mapped to many...</div>
        <p>Our {QUESTION.PLURAL} are mapped to the 17 UN Sustainable Development Goals (SDGs). If you see several icons next to a {QUESTION.SINGULAR}, it means with one {QUESTION.SINGULAR} you are impacting several SDGs.</p>
        <div className='mt-4'>
          {[1, 8, 10, 12, 16].map((o) => <SDGIcon className='mr-2' code={o} key={o} width={65} height={65} />)}
        </div>
      </div>
    </DashboardSection>
  </>;
}
