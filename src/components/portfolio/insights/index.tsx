/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import React, { useEffect, useState } from 'react';
import { CircularProgressbar } from 'react-circular-progressbar';
import Dashboard, { DashboardColumn, DashboardRow } from '../../dashboard';
import TopSdgs from '../../top-sdgs';
import { SDGControlStrip } from '../../sdg-control-strip';
import { useAppDispatch, useAppSelector } from '../../../reducers';
import { useParams } from 'react-router-dom';
import { loadPortfolioById, reloadPortfolioById } from '../../../slice/portfolioSlice';
import { SdgChartModal } from '../../impact-performance/SdgChartModal';
import PortfolioScoresRadarChart from '../../charts/portfolio-scores-radar-chart';
import { loadPortfolioCompanies, reloadPortfolioCompanies } from '../../../slice/portfolioCompanySlice';
import UpdateMaterialityModal from '../../update-materiality-modal';
import { loadScorecardsByInitiativeId, reloadScorecards } from '../../../actions/scorecard';
import { reloadPortfolioTree } from '../../../slice/portfolioTreeSlice';
import { getMainScorecard } from '../../../selectors/scorecard';
import { PortfolioInsightsSidebar } from '../../../routes/summary/insights/partials/sidebar/PortfolioInsightsSidebar';
import { useSettingsSidebar } from '@routes/custom-dashboard/dashboard-settings/useSettingsSidebar';
import { usePTCustomDashboards } from '@hooks/usePTCustomDashboards';
import { CustomDashboardTitle } from '@routes/custom-dashboard/CustomDashboardTitle';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

interface SdgChartState {
  isOpen: boolean;
  sdgCode?: string | number;
}

const contributionItems: JSX.Element[] = [
  {
    text: <span>Contribution by goal</span>,
    tooltip:
      'Calculated using responses to SDG Impact questions by company within your portfolio. Individual company weightings will impact your 17 SDG scores.',
  },
  {
    text: <span>Contribution by materiality</span>,
    tooltip:
      'Calculated using responses to SDG Impact questions by company within your portfolio. Your materiality is decided by the key material topics relevant to your portfolio.',
  },
].map(({ text, tooltip }) => (
  <>
    {text}
    <SimpleTooltip text={tooltip}>
      <i className='fal fa-circle-info ml-2 text-ThemeIconSecondary' />
    </SimpleTooltip>
  </>
));

export const Insights = () => {
  const dispatch = useAppDispatch();
  const { portfolioId } = useParams<{ portfolioId: string; }>();

  const { data: portfolio, loaded, loading } = useAppSelector((state) => state.portfolio);
  const scorecardState = useAppSelector((state) => getMainScorecard(state));

  const [displaySdgChartModal, setSdgChartModal] = useState<SdgChartState>({ isOpen: false });
  const [displayMaterialityModal, toggleMaterialityModal] = useState(false);

  const handleChangeSDG = (sdgCodes?: string | number | number[]) => {
    const code = Array.isArray(sdgCodes) ? sdgCodes[0] : sdgCodes;
    setSdgChartModal((m) => ({ ...m, isOpen: Boolean(code), sdgCode: code }));
  };

  useEffect(() => {
    if (!portfolioId) {
      return;
    }
    dispatch(loadPortfolioById(portfolioId));
    dispatch(loadScorecardsByInitiativeId(portfolioId));
    dispatch(loadPortfolioCompanies(portfolioId));
  }, [dispatch, portfolioId]);

  const settingsSidebarProps = useSettingsSidebar({ initiativeId: portfolioId });
  const { handleAddNew } = settingsSidebarProps;
  const {
    currentPage,
    options,
    isFetchingDashboards,
    handleClickOption,
    handleNavigateCustom
  } = usePTCustomDashboards({ portfolioId, handleAddNew });
  const commonSidebarProps = { ...settingsSidebarProps, portfolioId, currentPage, options, handleClickOption, handleNavigateCustom };

  const isFullyLoaded = loaded && !loading && !isFetchingDashboards;

  if (!isFullyLoaded || !portfolio) {
    return (
      <Dashboard className='insights-dashboard' hasSidebar={true}>
        <PortfolioInsightsSidebar {...commonSidebarProps} />
        <LoadingPlaceholder height={600} />
      </Dashboard>
    );
  }

  const scorecard = portfolio.scorecard.scorecard;

  const handleReload = async () => {
    if (portfolioId) {
      dispatch(reloadScorecards());
      dispatch(reloadPortfolioCompanies(portfolioId));
      dispatch(reloadPortfolioById(portfolioId, true));
    }
    dispatch(reloadPortfolioTree());
  };

  return (
    <>
      <Dashboard className='insights-dashboard' hasSidebar={true}>
        <PortfolioInsightsSidebar {...commonSidebarProps} />
        <Dashboard className='mx-0'>
          <DashboardRow>
            <CustomDashboardTitle
              title={<h3 className='text-ThemeHeadingLight'>SDG Insight</h3>}
              currentPage={currentPage}
              options={options}
              handleClickOption={handleClickOption}
            />
          </DashboardRow>
          <DashboardRow>
            <DashboardColumn title='Contribution index' subtitle='Weighted-average aggregated index' flexBasisPc='50%'>
              <div className='text-right' style={{ marginTop: '-40px' }}>
                <CircularProgressbar
                  strokeWidth={10}
                  value={scorecard.actual ?? 0}
                  text={`${Math.round(scorecard.actual ?? 0)}%`}
                />
              </div>
            </DashboardColumn>
            <DashboardColumn title='Top goals' flexBasisPc='50%'>
              <div className='text-right' style={{ marginTop: '-20px' }}>
                <TopSdgs handleSDGClick={handleChangeSDG} goals={scorecard.goals} width={80} height={80} />
              </div>
            </DashboardColumn>
          </DashboardRow>
          <DashboardRow
            title='SDG materiality and contribution'
            buttons={[
              {
                icon: 'fal fa-cog',
                tooltip: 'Configure materiality',
                onClick: () => toggleMaterialityModal(true),
              },
            ]}
          >
            <SDGControlStrip
              isCompanyTrackerLight={false}
              scorecardState={scorecardState}
              initiativeId={portfolio._id}
              displayMateriality={true}
              displayProgressBars={true}
              defaultSelectedSDGs={displaySdgChartModal.sdgCode ? [displaySdgChartModal.sdgCode] : []}
              className='p-2'
              handleClick={handleChangeSDG}
            />
          </DashboardRow>
          <DashboardRow>
            {contributionItems.map((item, index) => (
              <DashboardColumn key={index} className='flex-fill' title={item}>
                <div className='d-flex align-items-center justify-content-center'>
                  <PortfolioScoresRadarChart
                    portfolio={portfolio}
                    width={220}
                    height={220}
                    showMaterialityChart={!!index}
                  />
                </div>
              </DashboardColumn>
            ))}
          </DashboardRow>
        </Dashboard>
      </Dashboard>
      {displaySdgChartModal.isOpen && portfolioId && (
        <SdgChartModal
          handleChangeSDG={(code) => code && handleChangeSDG(code)}
          initiativeId={portfolioId}
          isOpen={displaySdgChartModal.isOpen}
          toggle={() => setSdgChartModal((v) => ({ ...v, sdgCode: undefined, isOpen: !v.isOpen }))}
          sdgCode={displaySdgChartModal.sdgCode}
          scorecard={{ scorecard: portfolio.scorecard.scorecard, initiative: portfolio }}
        />
      )}
      {displayMaterialityModal && portfolioId && (
        <UpdateMaterialityModal
          initiative={portfolio}
          isOpen={displayMaterialityModal}
          toggle={() => toggleMaterialityModal(!displayMaterialityModal)}
          handleReload={handleReload}
        />
      )}
    </>
  );
};
