/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React from 'react';
import { gql, useQuery } from '@apollo/client';
import PhotosVideos from './PhotosVideos';
import PriceInfo from './PriceInfo';
import ProductDescription from './ProductDescription';
import ProductHighlights from './ProductHighlights';
import Reviews from './Reviews';
import { Footer } from '@g17eco/molecules/footer';
import Solutions from './Solutions';
import { useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from '../../reducers';
import { Loader } from '@g17eco/atoms/loader';
import './styles.scss';
import NotFound from '../not-found';
import Dashboard, { DashboardSection } from '../dashboard';
import { BasicAlert } from '@g17eco/molecules/alert';

export interface MediaProps {
  id: string;
  photosVideos?: {
    handle: string;
  };
}

export interface ReviewProps {
  id: string;
  partner: string;
  review: string;
}

interface productPagesProps {
  description?: { html: string };
  heading1: string;
  heading2: string;
  heading3: string;
  heading4: string;
  heading5: string;
  mediaProducts: MediaProps[];
  productHighlights?: { html: string };
  reviewProducts: ReviewProps[];
  productName: string;
  productSynopsis: string;
  productBenefits?: { html: string };
  slug: string;
  priceHeading: string;
  productPrice?: string;
}

interface QueryResult {
  productPages: productPagesProps[];
}

const FeedQuery = gql`
  query {
    productPages(stage: PUBLISHED) {
      heading1
      heading2
      heading3
      heading4
      heading5
      priceHeading
      productPrice
      description {
        html
      }
      mediaProducts {
        id
        photosVideos {
          handle
        }
      }
      productHighlights {
        html
      }
      reviewProducts {
        id
        partner
        review
      }
      productName
      productSynopsis
      productBenefits {
        html
      }
      slug
    }
  }
`;

export interface Params {
  slug: string;
}

const ProductPage = () => {
  const { data, error, loading } = useQuery<QueryResult>(FeedQuery);
  const params = useParams<Params>();
  const product = data?.productPages?.find((page) => page.slug === params.slug);

  const isLoggedIn = useSelector((state: RootState) => state.currentUser.loaded);

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return (
      <BasicAlert type='info'>
        <div className='loader-error'>
          <i className='fa fa-info-circle' aria-hidden='true' />
          There was an error. Please try again!
          <br />
          <br />
        </div>
      </BasicAlert>
    );
  }

  if (!product) {
    return <NotFound />;
  }

  const priceInfoList = {
    productName: product.productName,
    productSynopsis: product.productSynopsis,
    productBenefits: product.productBenefits ?? undefined,
    priceHeading: product.priceHeading,
    productPrice: product.productPrice ?? undefined,
  };

  return (
    <Dashboard className='product-page-container'>
      <DashboardSection className='product-page-content'>
        {priceInfoList.productName ? <PriceInfo priceInfoList={priceInfoList} isLoggedIn={isLoggedIn} /> : null}
        {product.heading1 ? <PhotosVideos mediaProducts={product.mediaProducts} heading={product.heading1} /> : null}
        {product.heading2 ? <ProductDescription heading={product.heading2} description={product.description} /> : null}
        {product.heading3 ? (
          <ProductHighlights productHighlights={product.productHighlights} heading={product.heading3} />
        ) : null}
        {product.heading4 ? <Reviews reviews={product.reviewProducts} heading={product.heading4} /> : null}
      </DashboardSection>
      <DashboardSection>
        <Solutions heading={product.heading5} />
      </DashboardSection>
      <Footer />
    </Dashboard>
  );
};

export default ProductPage;
