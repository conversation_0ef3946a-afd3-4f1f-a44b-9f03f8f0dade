/*
 * Copyright (c) 2023. Word Wide Generation Ltd
 */

import React from 'react';
import { Loader, BlockingLoader } from '@g17eco/atoms/loader';
import { BasicAlert } from '@g17eco/molecules/alert';

interface QueryState<T> {
  data?: T;
  error?: unknown;
  isLoading: boolean;
  isFetching: boolean;
  isUninitialized: boolean;
  isError: boolean;
}

interface Props<T> {
  query: QueryState<T>;
  onNoData?: () => JSX.Element;
  onSuccess: (data: T) => JSX.Element | null;
  onError?: (error?: unknown) => JSX.Element | null;
  onFetching?: () => JSX.Element;
  onLoading?: () => JSX.Element;
}

function isErrorWithMessage(error: unknown): error is { message: string } {
  return (
    typeof error === 'object' && error !== null && 'message' in error && typeof (error as any).message === 'string'
  );
}

export function QueryWrapper<D>(props: Props<D>): React.JSX.Element | null {
  const { query, onNoData, onSuccess, onError, onFetching, onLoading } = props;

  const LoadingLoader = onLoading ?? Loader;
  const FetchingLoader = onFetching ?? BlockingLoader;

  const { isLoading: isFirstLoad, isFetching, isUninitialized } = query;
  if (isFirstLoad || isUninitialized) {
    return <LoadingLoader />;
  }

  if (query.isError) {
    if (onError) {
      return onError(query.error);
    }

    if (isErrorWithMessage(query.error)) {
      return <BasicAlert type={'danger'}>{query.error.message}</BasicAlert>;
    }

    return <BasicAlert type={'danger'}>{query.error}</BasicAlert>;
  }

  if (!query.data || (Array.isArray(query.data) && query.data.length === 0)) {
    if (onNoData) {
      return onNoData();
    }
    return <div>No data available</div>;
  }

  return (
    <>
      {isFetching ? <FetchingLoader /> : null}
      {onSuccess(query.data)}
    </>
  );
}
