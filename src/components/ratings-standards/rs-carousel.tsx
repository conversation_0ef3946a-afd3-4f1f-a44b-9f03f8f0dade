/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import React, { Component } from 'react';
import { FlickityWrapper } from '../flickity-wrapper';
import { Loader } from '@g17eco/atoms/loader';
import { DashboardSection } from '../dashboard';
import { RatingAgencyRating } from '../../types/initiative';
import { getYear } from '../../utils/date';
import './style.scss';
import IconButton from '../button/IconButton';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

export interface RsCarouselState {
  defaultWrapperClass?: string;
  headingText: string;
  linkTooltip: string;
  loaded?: boolean;
  hasError?: boolean;
  items?: RatingAgencyRating[];
  errorMessage?: string;
  ratingModal?: any;
  selectedItem?: any;
}

export interface RsCarouselProps {
  initiativeId: string;
  surveyId?: string;
  readOnly?: boolean;
  showNoItems?: boolean;
  isManager?: boolean;
  options: RsCarouselOptions;
  flickityOptions?: RsCarouselOptions;
  standards?: {
    loaded: boolean;
    initiativeId: string;
    standards: unknown;
    data: {
      _id: string
    }[];
  };
  loadSurveyList?: (initiativeId: string) => void;
}

interface RsCarouselOptions {
  showLinkButton?: boolean;
  showDownloadButton?: boolean;
  showInfoButton?: boolean;
  [key: string]: any;
}

abstract class RsCarousel<P extends RsCarouselProps = RsCarouselProps> extends Component<P, RsCarouselState> {
  _options: RsCarouselOptions = {
    showLinkButton: true,
    showDownloadButton: false,
    showInfoButton: false,
  };
  _flickityOptions: RsCarouselOptions = {
    cellAlign: 'left',
    pageDots: true,
    draggable: true,
    groupCells: true
  };

  _isMounted = false;

  state: RsCarouselState = {
    headingText: '',
    defaultWrapperClass: '',
    items: [],
    loaded: false,
    hasError: false,
    linkTooltip: 'Visit',
  };

  componentWillUnmount() {
    this._isMounted = false;
  }

  render() {
    if (this.state.hasError) {
      return this.renderWithWrapper(<>{this.state.errorMessage}</>);
    }

    if (!this.state.loaded) {
      return this.renderWithWrapper(<Loader />)
    }

    const options = this.getOptions(this._options, this.props.options);
    const flickityOptions = this.getOptions(this._flickityOptions, this.props.flickityOptions);
    const items = this.state.items;

    if (!items || items.length === 0) {
      return this.renderNoItems();
    }

    return this.renderWithWrapper(
      <div className='rs-content'>
        <div className='ratings-standards'>
          <FlickityWrapper options={flickityOptions}>
            {items.filter(item => !!item).map((item, i) => this.renderCard(item, i, options))}
          </FlickityWrapper>
        </div>
      </div>
    );
  }

  canEdit() {
    return !this.props.readOnly && !!this.props.isManager;
  }

  renderNoItems() {
    return <></>;
  }

  renderInfoButton(item: RatingAgencyRating) {
    if (!item.additionalInformation) {
      return <></>;
    }
    return <SimpleTooltip text={item.additionalInformation}>
      <IconButton outline icon='fa-info-circle' />
    </SimpleTooltip>
  }

  /**
   * This method is overloaded in derived components
   */
  abstract renderEditButton(): JSX.Element

  renderWithWrapper(elements: JSX.Element) {

    const buttons = this.canEdit() ? [this.renderEditButton()] : [];
    return (
      <DashboardSection
        padding={2}
        title={this.state.headingText}
        className={`flickity-wrapper ${this.state.defaultWrapperClass}`}
        buttons={buttons}
      >
        {elements}
        {this.renderFooter(this.state.items ?? [])}
      </DashboardSection>
    );
  }

  getOptions(options: RsCarouselOptions, overrides?: RsCarouselOptions) {
    overrides = overrides || {};
    options = options || {};
    for (const key in overrides) {
      if (Object.hasOwn(overrides, key)) {
        options[key] = overrides[key];
      }
    }
    return options;
  }

  renderFirstButton(item: RatingAgencyRating) {
    return <></>
  }

  renderCard(item: RatingAgencyRating, i: number, options: RsCarouselOptions) {
    // let subheading = <></>;
    let linkButton = <></>;
    let infoButton = <></>;

    // if (item.subheading) {
    //   subheading = <span className="subheading">{item.subheading}</span>
    // }
    if (options.showInfoButton && this.renderInfoButton) {
      infoButton = this.renderInfoButton(item);
    }
    if (options.showLinkButton) {
      const { text, url } = this.getLinkProps(item);
      linkButton =
        <SimpleTooltip text={text}>
          <IconButton outline icon='fa-link' onClick={() => {
            window.open(url, '_blank', '')
          }} />
        </SimpleTooltip>
    }

    const subheading = item.date ? `: ${getYear(item.date)}` : '';
    const isRatingMaxLength = item.rating.length > 16;

    return (
      <div className='card edit-button-container' key={i}>
        <div className='card-body'>
          <div className='row align-items-start first-line'>
            <div className='col title-col'>
              {this.getTitle(item)}
            </div>
            <div className='col text-right'>
              {this.renderItemEditButton(item)}
              {this.renderFirstButton(item)}
              {infoButton}
              {linkButton}
            </div>
          </div>
          <div className='row second-line'>
            <div className='col-3 rs-logo'>
              <img alt={item.title + ' logo'} className='logo' src={item.logoSrc} />
            </div>
            <div className='col score'>
              <SimpleTooltip text={isRatingMaxLength ? `${item.rating} ${subheading}` : ''} className={`rating ${isRatingMaxLength ? 'text-truncate' : ''}`}>
                <span>
                  {item.rating} {subheading}
                </span>
              </SimpleTooltip>
            </div>
          </div>
        </div>
      </div>
    );
  }

  private getLinkProps(item: RatingAgencyRating) {
    if (item.link) {
      return { text: item.linkText, url: item.link };
    }
    return { text: this.state.linkTooltip, url: item.ratingAgencyUrl };
  }

  getTitle(item: RatingAgencyRating) {
    const maxTitleLength = 34;

    return <SimpleTooltip text={item.title}>
      <h3 className='title'>
        {item.title.length > maxTitleLength ? `${item.title.substr(0, maxTitleLength)}...` : item.title}
      </h3>
    </SimpleTooltip>;
  }

  renderItemEditButton(item: RatingAgencyRating) {
    return <></>;
  }

  renderFooter(items: RatingAgencyRating[]) {
    return <></>;
  }
}

export default RsCarousel;
