/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import { Collapse, Input } from 'reactstrap';
import { DashboardSection, DashboardSectionProps } from '../dashboard';
import IconButton from '../button/IconButton';
import './style.scss';

export interface SidebarProps {
  children: JSX.Element | JSX.Element[];
  className?: string;
}

export const InfoButton = ({ tooltip }: { tooltip?: JSX.Element | string }) => {
  if (!tooltip) {
    return <></>;
  }
  return <IconButton tooltip={tooltip} icon='fa-info' />
}

export const SettingsSection = (props: DashboardSectionProps) => {
  const { children, title, subtitle, icon, isVisible, className, headingStyle = 5 } = props;

  const [isOpen, setOpen] = React.useState<boolean | undefined>(undefined);

  if (isVisible === false) {
    return <></>;
  }

  const buttons = props.buttons ? [...props.buttons] : [];
  buttons.push({
    tooltip: isOpen ? 'Open' : 'Close',
    icon: isOpen ? 'fa-chevron-up' : 'fa-chevron-down',
    onClick: () => setOpen(!isOpen),
    className: 'd-xxl-none ml-2'
  });

  const collapseClassName = isOpen !== true ? 'd-none d-xxl-block' : '';

  return (
    <DashboardSection
      title={title}
      icon={icon}
      buttons={buttons}
      subtitle={<span className={collapseClassName}>{subtitle}</span>}
      padding={0}
      headingStyle={headingStyle}
      className={className}
    >
      <Collapse className={collapseClassName} isOpen={isOpen} >
        {children}
      </Collapse>
    </DashboardSection>
  );
}

interface SearchBoxOptionsInterface {
  value: string;
  subtitle?: string;
  placeholder?: string;
  handleOnChange: (e: React.FormEvent<HTMLInputElement>) => void;
}

export const SearchBox = (props: SearchBoxOptionsInterface) => {
  return (
    <div className='mt-2'>
      {props.subtitle ? <div className='text-ThemeAccentExtradark'>
        {props.subtitle}
      </div> : <></>}
      <Input placeholder={props.placeholder ?? 'Search'} onChange={(e: React.ChangeEvent<HTMLInputElement>) => props.handleOnChange(e)} value={String(props.value)} />
    </div>
  );
}

export default function SettingsSidebar(props: SidebarProps) {
  return <div className={`settings-sidebar-container ${props.className ?? ''}`}>
    <div className='sticky-sidebar'>
      {props.children}
    </div>
  </div>
}
