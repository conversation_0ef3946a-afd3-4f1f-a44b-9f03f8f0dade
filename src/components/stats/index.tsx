/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React from 'react';
import { useHistory } from 'react-router-dom';
import { Button } from 'reactstrap';
import { ROUTES } from '../../constants/routes';
import { generateUrl } from '../../routes/util';
import Dashboard, { DashboardSection } from '../dashboard';
import { QUESTION, SURVEY } from '@constants/terminology';
import { ExtraFeature, FeatureStability } from '@g17eco/molecules/feature-stability';

export const Stats = () => {

  const history = useHistory();

  return (
    <Dashboard>
      <DashboardSection
        title={<span>Reports <FeatureStability feature={ExtraFeature.StaffReports} /></span>}
        subtitle='Please select a report below'
      >

        <Button className='mr-2' onClick={() => history.push(generateUrl(ROUTES.STATS_COMPANIES))}>Companies</Button>
        <Button className='mr-2' onClick={() => history.push(generateUrl(ROUTES.STATS_SURVEY))}>{SURVEY.CAPITALIZED_SINGULAR}</Button>
        <Button className='mr-2' onClick={() => history.push(generateUrl(ROUTES.STATS_SCOPE))}>Scope</Button>
        <Button className='mr-2' onClick={() => history.push(generateUrl(ROUTES.STATS_STANDARDS))}>Standards</Button>
        <Button className='mr-2' onClick={() => history.push(generateUrl(ROUTES.STATS_QUESTIONS))}>{QUESTION.CAPITALIZED_PLURAL}</Button>
      </DashboardSection>
    </Dashboard>
  );
}
