/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import NumberFormat from '@utils/number-format';
import Dashboard, { DashboardColumn, DashboardRow, DashboardSection } from '../../dashboard';
import { Loader } from '@g17eco/atoms/loader';
import { ErrorComponent } from '@features/error';
import { formatDate, DATE } from '@utils/date';
import SettingsSidebar, { SearchBox, SettingsSection } from '../../settings-sidebar';
import { ReportTypes } from '@g17eco/types/statsTypes';
import { escapeRegexCharacters } from '@utils/string-format';
import FileSaver from 'file-saver';
import { convertBinaryToOctetStream, getWorkbook } from '@utils/file/XlsxFile';
import { useReportData } from '../hooks/useReportData';
import { useStaffFilters } from '../hooks/useStaffFilters';
import { StaffFilters } from '../partials/StaffFilters';
import { convertToDateParams } from '../utils';
import { useToggle } from '@hooks/useToggle';
import { StatsRow } from '../utils/types';
import './styles.scss';
import { CompanyDetailsModal } from './company-details-modal';
import { SURVEY } from '@constants/terminology';
import { ColumnDef, Table } from '@g17eco/molecules/table';
import { naturalSort } from '@utils/index';

export const Company = () => {
  const [hasError, setHasError] = React.useState(false);
  const [rows, setRows] = React.useState<StatsRow[] | undefined>();
  const [searchText, setSearchText] = React.useState('');

  const staffFilterProps = useStaffFilters();
  const { extraFilterApiParams, ...staffFilters } = staffFilterProps;

  const [selectedRow, setSelectedRow] = useState<StatsRow>();
  const [openCompanyDetails, toggleOpenCompanyDetails] = useToggle(false);

  const handleShowCompanyDetails = (row: StatsRow) => {
    setSelectedRow(row);
    toggleOpenCompanyDetails();
  }

  const columns: ColumnDef<StatsRow>[] = [
    {
      id: '_id',
      header: '',
      cell: ({ row }) => (
        <span className='cursor-pointer p-1' onClick={() => handleShowCompanyDetails(row.original)}>
          <i className='fa-light fa-circle-info'></i>
        </span>
      ),
    },
    {
      accessorKey: 'name',
      header: 'Company',
    },
    {
      header: 'Onboarded',
      meta: {
        cellProps: {
          className: 'text-right pr-2',
        },
      },
      accessorFn: (row) => formatDate(row.created, DATE.DEFAULT_SPACES),
      sortingFn: (a, b) => naturalSort(a.original.created, b.original.created),
    },
    {
      accessorKey: 'surveyCount',
      header: `${SURVEY.CAPITALIZED_SINGULAR} Count`,
      meta: {
        cellProps: {
          className: 'text-right pr-2',
        },
      },
      cell: ({ row }) => <NumberFormat value={row.original.surveyCount} />,
    },
    {
      accessorKey: 'userCount',
      header: 'User Count',
      meta: {
        cellProps: {
          className: 'text-right pr-2',
        },
      },
    },
  ];

  const successCallback = React.useCallback((data?: StatsRow[]) => {
    setHasError(false);
    setRows(data);
  }, []);

  const errorCallback = React.useCallback(() => {
    setHasError(true);
  }, []);

  useReportData({
    reportType: ReportTypes.Company,
    successCallback,
    errorCallback,
    extraProps: extraFilterApiParams,
    dateParams: convertToDateParams(staffFilterProps.dateRange),
  });

  const handleDownloadStatistic = () => {
    if (!rows || rows.length === 0) {
      return;
    }
    // Headers must be strings
    const rowHeaders = columns.map(col => col.header as string);
    const rowValues = rows.map(r => ([r.name, formatDate(r.created, DATE.DEFAULT_SPACES), r.surveyCount, r.userCount]));
    const data = [[rowHeaders, ...rowValues]];
    const fileName = 'Company Statistics';

    const workBook = getWorkbook(data, { title: fileName, subject: fileName, author: 'g17eco', createDate: new Date(), sheetNames: [fileName] });
    const blob = new Blob([convertBinaryToOctetStream(workBook)], { type: 'application/octet-stream' });
    FileSaver.saveAs(blob, `${fileName}.xlsx`);
  }

  const handleCloseCompanyDetails = () => {
    setSelectedRow(undefined);
    toggleOpenCompanyDetails();
  }

  const filteredRows = React.useMemo((): StatsRow[] => {
    if (!rows) {
      return [];
    }

    const filterScore = (row: StatsRow) => {
      if (searchText) {
        const escapedSearchText = escapeRegexCharacters(searchText.trim().toLowerCase());
        const searchTextRegex = new RegExp(escapedSearchText.replace(/ /g, '|'), 'g');
        const searchString = `${row.name}`.toLowerCase();
        return (searchString.match(searchTextRegex) || []).length;
      }
      return 1;
    }

    return rows.filter(filterScore);
  }, [rows, searchText]);

  if (hasError) {
    return <ErrorComponent />;
  }

  const total = {
    count: filteredRows.length,
    surveyCount: filteredRows.reduce((acc, d) => acc + d.surveyCount, 0),
    userCount: filteredRows.reduce((acc, d) => acc + d.userCount, 0)
  };

  const isLoading = rows === undefined;

  return (
    <Dashboard hasSidebar>
      <SettingsSidebar>
        <SettingsSection
          title={'Search Companies'}
          subtitle='Search for companies by name.'
          icon='fa-search'
          buttons={[
            {
              icon: 'fa-times-circle',
              tooltip: 'Clear search',
              onClick: () => setSearchText(''),
            }
          ]}>
          <SearchBox
            handleOnChange={(e) => setSearchText(String((e.target as HTMLInputElement).value))}
            value={searchText}
          />
        </SettingsSection>
        <StaffFilters {...staffFilters} reportType={ReportTypes.Company} />
      </SettingsSidebar>
      <DashboardRow>
        <DashboardColumn
          title='Number of Companies'
          flexBasisPc={'33%'}
        >
          <div className='text-right'>
            <NumberFormat value={total.count} />
          </div>
        </DashboardColumn>
        <DashboardColumn
          title={`Total ${SURVEY.CAPITALIZED_PLURAL}`}
          flexBasisPc={'33%'}
        >
          <div className='text-right'>
            <NumberFormat value={total.surveyCount} />
          </div>
        </DashboardColumn>
        <DashboardColumn
          title='Total Users'
          flexBasisPc={'33%'}
        >
          <div className='text-right'>
            <NumberFormat value={total.userCount} />
          </div>
        </DashboardColumn>
      </DashboardRow>
      <DashboardSection
        title={<span>Company Statistics <i className='fas fa-file-excel cursor-pointer ml-2' onClick={handleDownloadStatistic}></i></span>}
        icon='fa-clipboard-list'
      >
        {isLoading
          ?
          <Loader />
          :
          filteredRows.length === 0
            ?
            <>No results</>
            :
            <Table
              className='table-company-stats'
              columns={columns}
              data={filteredRows}
            />
        }
      </DashboardSection>
      <CompanyDetailsModal company={selectedRow} isOpen={openCompanyDetails} handleClose={() => handleCloseCompanyDetails()} />
    </Dashboard>
  );
}
