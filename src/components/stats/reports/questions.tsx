/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React from 'react';
import NumberFormat from '@utils/number-format';
import Dashboard, {
  DashboardColumn,
  DashboardRow,
  DashboardSection
} from '../../dashboard';
import { Loader } from '@g17eco/atoms/loader';
import { ErrorComponent } from '@features/error';
import SettingsSidebar, {
  SearchBox,
  SettingsSection
} from '../../settings-sidebar';
import { escapeRegexCharacters } from '@utils/string-format';
import { convertToDateParams } from '../utils';
import { ReportTypes } from '@g17eco/types/statsTypes';
import { useReportData } from '../hooks/useReportData';
import { StaffFilters } from '../partials/StaffFilters';
import { useStaffFilters } from '../hooks/useStaffFilters';
import { QUESTION } from '@constants/terminology';
import { ColumnDef, Table } from '@g17eco/molecules/table';

const columns: ColumnDef<StatsRow>[] = [
  {
    accessorKey: 'name',
    header: QUESTION.CAPITALIZED_SINGULAR,
    meta: {
      cellProps: {
        className: 'text-wrap',
      },
    },
  },
  {
    accessorKey: 'count',
    header: 'In Scope',
    meta: {
      cellProps: {
        className: 'text-right pr-2',
      },
    },
    cell: ({ row }) => <NumberFormat value={row.original.count} />,
  },
  {
    id: 'answered',
    header: 'Answered %',
    meta: {
      cellProps: {
        className: 'text-right pr-2',
      },
    },
    cell: ({ row }) => (
      <NumberFormat value={(100 * row.original.answered) / row.original.count} decimalPlaces={1} suffix={'%'} />
    ),
  },
  {
    id: 'verified',
    header: 'Verified %',
    meta: {
      cellProps: {
        className: 'text-right pr-2',
      },
    },
    cell: ({ row }) => (
      <NumberFormat value={(100 * row.original.verified) / row.original.count} decimalPlaces={1} suffix={'%'} />
    ),
  },
];

interface StatsData {
  _id: string;
  name: string;
  status: string;
  count: number;
}

interface StatsRow {
  name?: string;
  unique?: number;
  count: number;
  verified: number;
  answered: number;
}

const Questions = () => {
  const [searchText, setSearchText] = React.useState('');
  const [hasError, setHasError] = React.useState(false);
  const [rows, setRows] = React.useState<StatsData[] | undefined>();

  const staffFilterProps = useStaffFilters();
  const { extraFilterApiParams, ...staffFilters } = staffFilterProps;

  const successCallback = React.useCallback((data?: StatsData[]) => {
    setHasError(false);
    setRows(data);
  }, []);

  const errorCallback = React.useCallback(() => {
    setHasError(true)
  }, []);

  useReportData({
    reportType: ReportTypes.Questions,
    successCallback,
    errorCallback,
    extraProps: extraFilterApiParams,
    dateParams: convertToDateParams(staffFilterProps.dateRange),
  });

  const filteredRows = React.useMemo(() => {
    if (!rows) {
      return [];
    }

    const filterScore = (row: StatsRow) => {
      if (searchText) {
        const escapedSearchText = escapeRegexCharacters(searchText.trim().toLowerCase());
        const searchTextRegex = new RegExp(escapedSearchText.replace(/ /g, '|'), 'g');
        const searchString = `${row.name}`.toLowerCase();
        return (searchString.match(searchTextRegex) || []).length;
      }

      return 1;
    }

    const filtered: {
      [key: string]: StatsRow;
    } = {};

    rows.forEach((r) => {
      if (!filtered[r._id]) {
        filtered[r._id] = {
          name: r.name,
          count: 0,
          verified: 0,
          answered: 0
        }
      }

      filtered[r._id].count += r.count;
      if (r.status === 'verified') {
        filtered[r._id]['verified'] += r.count;
      }
      if (r.status !== 'created') {
        filtered[r._id]['answered'] += r.count;
      }
    });

    return Object.values(filtered).filter(filterScore);
  }, [rows, searchText]);

  if (hasError) {
    return <ErrorComponent />;
  }

  const total = {
    name: '',
    unique: 0,
    count: 0,
    verified: 0,
    answered: 0
  };

  filteredRows.forEach(r => {
    total.unique++;
    total.count += r.count;
    total.verified += r.verified;
    total.answered += r.answered;
  });

  const isLoading = rows === undefined;

  return (
    <Dashboard hasSidebar>
      <SettingsSidebar>
        <SettingsSection
          title={`Search ${QUESTION.CAPITALIZED_PLURAL}`}
          subtitle={`Search for specific ${QUESTION.PLURAL} by name.`}
          icon='fa-search'
          buttons={[
            {
              icon: 'fa-times-circle',
              tooltip: 'Clear search',
              onClick: () => setSearchText(''),
            }
          ]}>
          <SearchBox
            handleOnChange={(e) => setSearchText(String((e.target as HTMLInputElement).value))}
            value={searchText}
          />
        </SettingsSection>
        <StaffFilters {...staffFilters} reportType={ReportTypes.Questions}/>
      </SettingsSidebar>
      <DashboardRow>
        <DashboardColumn
          title={`Unique ${QUESTION.CAPITALIZED_PLURAL}`}
          flexBasisPc={'25%'}
        >
          <div className='text-right'>
            <NumberFormat value={total.unique} />
          </div>
        </DashboardColumn>
        <DashboardColumn
          title={`Total ${QUESTION.CAPITALIZED_PLURAL}`}
          flexBasisPc={'25%'}
        >
          <div className='text-right'>
            <NumberFormat value={total.count} />
          </div>
        </DashboardColumn>
        <DashboardColumn
          title={`Answered ${QUESTION.CAPITALIZED_PLURAL}`}
          flexBasisPc={'25%'}
        >
          <div className='text-right'>
            <NumberFormat value={100 * total.answered / total.count} decimalPlaces={1} suffix={'%'} />
          </div>
        </DashboardColumn>
        <DashboardColumn
          title={`Verified ${QUESTION.CAPITALIZED_PLURAL}`}
          flexBasisPc={'25%'}
        >
          <div className='text-right'>
            <NumberFormat value={100 * total.verified / total.count} decimalPlaces={1} suffix={'%'} />
          </div>
        </DashboardColumn>
      </DashboardRow>
      <DashboardSection
        title={`${QUESTION.CAPITALIZED_SINGULAR} Statistics`}
        icon='fa-clipboard-list'
      >
        {isLoading
          ?
          <Loader />
          :
          filteredRows.length === 0
            ?
            <>No results</>
            :
            <Table
              showRowCount={true}
              columns={columns}
              data={filteredRows}
            />
        }
      </DashboardSection>
    </Dashboard>
  );
}

export default Questions;
