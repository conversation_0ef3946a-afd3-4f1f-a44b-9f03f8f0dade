/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import {
  DashboardColumn,
  DashboardRow,
  DashboardSection
} from '../../dashboard';
import React, { useState } from 'react';
import NumberFormat from '@utils/number-format';
import { escapeRegexCharacters } from '@utils/string-format';
import {
  MatchParams,
  ReportTypes,
  ScopeStatsRow,
  StatsScopeTagsData,
} from '@g17eco/types/statsTypes';
import { Link } from 'react-router-dom';
import { ReduxReducer } from '@reducers/types';
import { Loader } from '@g17eco/atoms/loader';
import { ExtraProps, useReportData } from '../hooks/useReportData';
import { QUESTION, SURVEY } from '@constants/terminology';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { ColumnDef, Table } from '@g17eco/molecules/table';

type RowData = ScopeStatsRow & { tooltip: string };
const columns: ColumnDef<RowData>[] = [
  {
    accessorKey: 'name',
    header: 'Company name',
    meta: {
      cellProps: {
        className: 'text-wrap',
      },
    },
    cell: ({ row }) => <SimpleTooltip text={row.original.tooltip}>{row.original.name}</SimpleTooltip>,
  },
  {
    accessorKey: 'count',
    header: `${SURVEY.CAPITALIZED_SINGULAR} Count`,
    meta: {
      cellProps: {
        className: 'text-right pr-2',
      },
    },
    cell: ({ row }) => <NumberFormat value={row.original.count} />,
  },
  {
    accessorKey: 'answeredPc',
    header: 'Answered %',
    meta: {
      cellProps: {
        className: 'text-right pr-2',
      },
    },
    cell: ({ row }) => <NumberFormat value={row.original.answeredPc} decimalPlaces={1} suffix={'%'} />,
  },
  {
    accessorKey: 'verifiedPc',
    header: 'Verified %',
    meta: {
      cellProps: {
        className: 'text-right pr-2',
      },
    },
    cell: ({ row }) => <NumberFormat value={row.original.verifiedPc} decimalPlaces={1} suffix={'%'} />,
  },
];

interface Props {
  scopeTag: string;
  searchText: string;
  dateParams: MatchParams<string>;
  extraProps: ExtraProps;
}

export const ScopeDetail = ({ dateParams, searchText, scopeTag, extraProps: extraFilterProps }: Props) => {

  const [state, setState] = useState<ReduxReducer<StatsScopeTagsData>>({
    loaded: false,
    errored: false,
    data: { universalTrackerValues: [] },
  })

  const [type, code] = scopeTag.split('/');

  const successCallback = React.useCallback((data?: StatsScopeTagsData) => {
    setState((current) => ({ ...current, loaded: true, data: data ?? { universalTrackerValues: [] } }))
  }, []);

  const errorCallback = React.useCallback((e?: Error) => setState((c) => ({
    ...c,
    errored: true,
    errorMessage: e?.message
  })), []);

  const extraProps = React.useMemo(
    () => ({ scopeGroups: [{ scopeType: type, code: code }], ...extraFilterProps }),
    [code, type, extraFilterProps]
  );

  useReportData({
    reportType: ReportTypes.ScopeTag,
    successCallback,
    errorCallback,
    extraProps,
    dateParams,
  });

  const rows = state.data.universalTrackerValues;

  const filteredRows = React.useMemo(() => {
    if (!rows) {
      return [];
    }

    const filterScore = (row: { name: string }) => {
      if (!searchText) {
        return 1;
      }
      const escapedSearchText = escapeRegexCharacters(searchText.trim().toLowerCase());
      const searchTextRegex = new RegExp(escapedSearchText.replace(/ /g, '|'), 'g');
      const searchString = `${row.name}`.toLowerCase();
      return (searchString.match(searchTextRegex) || []).length;
    }

    const filtered: { [key: string]: RowData } = {};

    rows.forEach(r => {
      if (!r.scope || !r.scope?.[type]?.includes(code)) {
        return;
      }

      const tag = r.initiativeCode;
      if (!filtered[tag]) {
        filtered[tag] = {
          name: r.initiativeName,
          tooltip: `${r.initiativeCode} - ${r._id.initiativeId}`,
          count: 0,
          scopeCount: 0,
          verifiedCount: 0,
          answeredCount: 0,
          answeredPc: 0,
          verifiedPc: 0,
        }
      }

      filtered[tag].count += 1;
      filtered[tag].scopeCount += r.count;
      filtered[tag].verifiedCount += r.verifiedCount;
      filtered[tag].answeredCount += r.answeredCount;
      filtered[tag].answeredPc = 100 * filtered[tag].answeredCount / filtered[tag].scopeCount;
      filtered[tag].verifiedPc = 100 * filtered[tag].verifiedCount / filtered[tag].scopeCount;
    });

    return Object.values(filtered).filter(filterScore);
  }, [code, rows, searchText, type]);


  const total = {
    unique: 0,
    scopeCount: 0,
    verifiedCount: 0,
    answeredCount: 0
  };

  filteredRows.forEach(r => {
    total.unique++;
    total.scopeCount += r.scopeCount;
    total.verifiedCount += r.verifiedCount;
    total.answeredCount += r.answeredCount;
  });

  const renderContent = () => {
    if (!state.loaded) {
      return <Loader />
    }

    if (filteredRows.length === 0) {
      return <>No results</>
    }

    return <Table showRowCount={true} columns={columns} data={filteredRows} />
  }

  return (
    <div>
      <DashboardRow>
        <DashboardColumn title='Total Companies' flexBasisPc={`${100 / 3}%`}>
          <div className='text-right'>
            <NumberFormat value={total.unique} />
          </div>
        </DashboardColumn>
        <DashboardColumn title={`Answered ${QUESTION.CAPITALIZED_PLURAL}`} flexBasisPc={`${100 / 3}%`}>
          <div className='text-right'>
            <NumberFormat value={100 * total.answeredCount / total.scopeCount}
                          decimalPlaces={1} suffix={'%'} />
          </div>
        </DashboardColumn>
        <DashboardColumn title={`Verified ${QUESTION.CAPITALIZED_PLURAL}`} flexBasisPc={`${100 / 3}%`}>
          <div className='text-right'>
            <NumberFormat value={100 * total.verifiedCount / total.scopeCount}
                          decimalPlaces={1} suffix={'%'} />
          </div>
        </DashboardColumn>
      </DashboardRow>
      <DashboardSection
        title={`${SURVEY.CAPITALIZED_SINGULAR} Scope Statistics - ${scopeTag}`}
        icon='fa-clipboard-list'
        buttons={[
          <Link key='go-back' to={'/stats/scope'}>Go Back</Link>
        ]}
      >
        {renderContent()}
      </DashboardSection>
    </div>
  )
}
