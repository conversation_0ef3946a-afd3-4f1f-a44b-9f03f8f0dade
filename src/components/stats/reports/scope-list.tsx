/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import { DashboardColumn, DashboardRow, DashboardSection } from '../../dashboard';
import React, { useState } from 'react';
import NumberFormat from '@utils/number-format';
import { naturalSort } from '@utils/index';
import { escapeRegexCharacters } from '@utils/string-format';
import { MatchParams, ReportTypes, ScopeStatsData, ScopeStatsRow } from '@g17eco/types/statsTypes';
import { Link } from 'react-router-dom';
import { ReduxReducer } from '@reducers/types';
import { Loader } from '@g17eco/atoms/loader';
import { ExtraProps, useReportData } from '../hooks/useReportData';
import { QUESTION, SURVEY } from '@constants/terminology';
import { ColumnDef, Table } from '@g17eco/molecules/table';


interface FilteredRows extends ScopeStatsRow {
  initiatives: Set<string>
}

const columns: ColumnDef<FilteredRows>[] = [
  {
    accessorKey: 'name',
    header: 'Scope',
    meta: {
      cellProps: {
        className: 'text-wrap',
      },
    },
    cell: ({ row }) => <Link to={`/stats/scope/${encodeURIComponent(row.original.name)}`}>{row.original.name}</Link>,
  },
  {
    accessorKey: 'initiatives',
    header: 'Companies count',
    meta: {
      cellProps: {
        className: 'text-right pr-2',
      },
    },
    sortingFn: (a, b) => naturalSort(String(a.original.initiatives.size), String(b.original.initiatives.size)),
    cell: ({ row }) => <NumberFormat value={row.original.initiatives.size} />,
  },
  {
    accessorKey: 'count',
    header: `${SURVEY.CAPITALIZED_SINGULAR} Count`,
    meta: {
      cellProps: {
        className: 'text-right pr-2',
      },
    },
    cell: ({ row }) => <NumberFormat value={row.original.count} />,
  },
  {
    accessorKey: 'answeredPc',
    header: 'Answered %',
    meta: {
      cellProps: {
        className: 'text-right pr-2',
      },
    },
    cell: ({ row }) => <NumberFormat value={row.original.answeredPc} decimalPlaces={1} suffix={'%'} />,
  },
  {
    accessorKey: 'verifiedPc',
    header: 'Verified %',
    meta: {
      cellProps: {
        className: 'text-right pr-2',
      },
    },
    cell: ({ row }) => <NumberFormat value={row.original.verifiedPc} decimalPlaces={1} suffix={'%'} />,
  },
];

interface Props {
  searchText: string;
  dateParams: MatchParams<string>;
  extraProps: ExtraProps;
}

export const ScopeList = ({ searchText, dateParams, extraProps }: Props) => {

  const [state, setState] = useState<ReduxReducer<ScopeStatsData[]>>({
    loaded: false,
    errored: false,
    data: [],
  })

  const successCallback = React.useCallback((data: ScopeStatsData[]) => {
    setState((current) => {
      return ({ ...current, loaded: true, data: data ?? [] });
    })
  }, []);

  const errorCallback = React.useCallback((e?: Error) => setState((c) => ({
    ...c,
    errored: true,
    errorMessage: e?.message
  })), []);

  useReportData({
    reportType: ReportTypes.Scope,
    successCallback,
    errorCallback,
    dateParams,
    extraProps
  });


  const rows = state.data;

  const filteredRows = React.useMemo(() => {
    if (!rows) {
      return [];
    }

    const filterScore = (row: ScopeStatsRow) => {
      if (!searchText) {
        return 1;
      }
      const escapedSearchText = escapeRegexCharacters(searchText.trim().toLowerCase());
      const searchTextRegex = new RegExp(escapedSearchText.replace(/ /g, '|'), 'g');
      const searchString = `${row.name}`.toLowerCase();
      return (searchString.match(searchTextRegex) || []).length;
    }

    const filtered: { [key: string]: FilteredRows } = {};

    const addRow = (tag: string, r: ScopeStatsData) => {
      if (!filtered[tag]) {
        filtered[tag] = {
          name: tag,
          count: 0,
          scopeCount: 0,
          verifiedCount: 0,
          answeredCount: 0,
          answeredPc: 0,
          verifiedPc: 0,
          initiatives: new Set(),
        }
      }

      filtered[tag].count += 1;
      filtered[tag].scopeCount += r.scopeCount;
      filtered[tag].verifiedCount += r.verifiedCount;
      filtered[tag].answeredCount += r.answeredCount;
      filtered[tag].answeredPc = 100 * filtered[tag].answeredCount / filtered[tag].scopeCount;
      filtered[tag].verifiedPc = 100 * filtered[tag].verifiedCount / filtered[tag].scopeCount;
      filtered[tag].initiatives.add(r.initiativeId);
    }

    rows.forEach(r => {

      if (!r.scope) {
        return;
      }

      for (const [scopeGroup, tags] of Object.entries(r.scope)) {
        if (tags) {
          tags.forEach((t: string) => addRow(`${scopeGroup}/${t}`, r));
        }
      }
    });

    return Object.values(filtered).filter(filterScore);
  }, [rows, searchText]);


  const total = {
    name: '',
    unique: 0,
    scopeCount: 0,
    verifiedCount: 0,
    answeredCount: 0
  };

  filteredRows.forEach(r => {
    total.unique++;
    total.scopeCount += r.scopeCount;
    total.verifiedCount += r.verifiedCount;
    total.answeredCount += r.answeredCount;
  });

  const renderContent = () => {
    if (!state.loaded) {
      return <Loader />
    }

    if (filteredRows.length === 0) {
      return <>No results</>
    }

    return <Table showRowCount={true} columns={columns} data={filteredRows} />
  }

  return (
    <>
      <DashboardRow>
        <DashboardColumn
          title='Total Scope Groups'
          flexBasisPc={`${100 / 3}%`}
        >
          <div className='text-right'>
            <NumberFormat value={total.unique} />
          </div>
        </DashboardColumn>
        <DashboardColumn
          title={`Answered ${QUESTION.CAPITALIZED_PLURAL}`}
          flexBasisPc={`${100 / 3}%`}
        >
          <div className='text-right'>
            <NumberFormat value={100 * total.answeredCount / total.scopeCount} decimalPlaces={1} suffix={'%'} />
          </div>
        </DashboardColumn>
        <DashboardColumn
          title={`Verified ${QUESTION.CAPITALIZED_PLURAL}`}
          flexBasisPc={`${100 / 3}%`}
        >
          <div className='text-right'>
            <NumberFormat value={100 * total.verifiedCount / total.scopeCount} decimalPlaces={1} suffix={'%'} />
          </div>
        </DashboardColumn>
      </DashboardRow>
      <DashboardSection title={`${SURVEY.CAPITALIZED_SINGULAR} Scope Statistics`} icon='fa-clipboard-list'>
        {renderContent()}
      </DashboardSection>
    </>

  )
}
