/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React, { useEffect } from 'react';
import Dashboard from '../../dashboard';
import SettingsSidebar, { SearchBox, SettingsSection } from '../../settings-sidebar';

import { useParams } from 'react-router-dom';
import { ScopeList } from './scope-list';
import { ScopeDetail } from './scope-detail';

import { StaffFilters } from '../partials/StaffFilters';
import { useStaffFilters } from '../hooks/useStaffFilters';
import { convertToDateParams } from '../utils';
import { ReportTypes } from '../../../types/statsTypes';

const Scope = () => {
  const [searchText, setSearchText] = React.useState('');
  const { scopeType = '' } = useParams<{ scopeType: string }>();
  const scopeTag = decodeURIComponent(scopeType);

  const staffFilterProps = useStaffFilters();
  const { extraFilterApiParams, ...staffFilters } = staffFilterProps;
  
  const render = () => {
    if (scopeTag) {
      return <ScopeDetail scopeTag={scopeTag} extraProps={extraFilterApiParams} dateParams={convertToDateParams(staffFilterProps.dateRange)} searchText={searchText} />;
    }
    return <ScopeList dateParams={convertToDateParams(staffFilterProps.dateRange)} extraProps={extraFilterApiParams} searchText={searchText} />;
  };

  // Reset search on scopeTag change, as previous filter no longer make sense,
  // or we just entered scope_tag view, that now list companies instead
  useEffect(() => {
    setSearchText('')
  }, [scopeTag])

  return (
    <Dashboard hasSidebar>
      <SettingsSidebar>
        <SettingsSection
          title={'Search Scope'}
          subtitle='Search for specific scope groups.'
          icon='fa-search'
          buttons={[
            {
              icon: 'fa-times-circle',
              tooltip: 'Clear search',
              onClick: () => setSearchText(''),
            }
          ]}>
          <SearchBox
            handleOnChange={(e) => setSearchText(String((e.target as HTMLInputElement).value))}
            value={searchText}
          />
        </SettingsSection>
        <StaffFilters {...staffFilters} reportType={ReportTypes.Scope} />
      </SettingsSidebar>
      {render()}
    </Dashboard>
  );
}

export default Scope;
