/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React, { useMemo } from 'react';
import NumberFormat from '@utils/number-format';
import Dashboard, { DashboardColumn, DashboardRow, DashboardSection } from '../../dashboard';
import { Loader } from '@g17eco/atoms/loader';
import { ErrorComponent } from '@features/error';
import { formatDate, DATE } from '@utils/date';
import SettingsSidebar, { SearchBox, SettingsSection } from '../../settings-sidebar';
import { escapeRegexCharacters } from '@utils/string-format';
import { convertToDateParams, getDisplayCompleted, getDisplaySurveyName } from '../utils';

import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import { useHistory } from 'react-router-dom';
import { ReportTypes } from '@g17eco/types/statsTypes';

import { useReportData } from '../hooks/useReportData';
import { convertBinaryToOctetStream, getWorkbook } from '@utils/file/XlsxFile';
import FileSaver from 'file-saver';
import { StaffFilters } from '../partials/StaffFilters';
import { useStaffFilters } from '../hooks/useStaffFilters';
import { QUESTION, SURVEY } from '@constants/terminology';
import { ColumnDef, Table } from '@g17eco/molecules/table';
import { naturalSort } from '@utils/index';

const SGXTag = 'sgx_metrics';
const tagKey = 'sgx_metrics_verified';

interface StatsData {
  _id: string,
  name: string,
  scopeCount: number,
  answeredCount: number,
  verifiedCount: number,
  rejectedCount: number,
  [tagKey]: number,
  created: string;
  effectiveDate: string;
  initiativeName: string;
  initiativeId: string,
  completedDate?: string,
}

interface StatsRow extends StatsData {
  surveyName: string;
  onClick: () => void;
}

const columns: ColumnDef<StatsRow>[] = [
  {
    accessorKey: 'initiativeName',
    header: 'Company name',
  },
  {
    accessorKey: 'surveyName',
    header: `${SURVEY.CAPITALIZED_SINGULAR} name`,
    cell: ({ row }) => getDisplaySurveyName(row.original.surveyName, row.original.onClick),
  },
  {
    header: `${SURVEY.CAPITALIZED_SINGULAR} created`,
    accessorFn: (row) => formatDate(row.created, DATE.DEFAULT_SPACES),
  },
  {
    accessorKey: 'scopeCount',
    header: 'Qs In Scope',
    meta: {
      cellProps: {
        className: 'text-center',
      },
    },
    cell: ({ row }) => <NumberFormat value={row.original.scopeCount} />,
  },
  {
    accessorKey: 'answeredCount',
    header: 'Answered',
    meta: {
      cellProps: {
        className: 'text-center',
      },
    },
    cell: ({ row }) => <NumberFormat value={row.original.answeredCount} />,
  },
  {
    accessorKey: 'verifiedCount',
    header: 'Verified',
    meta: {
      cellProps: {
        className: 'text-center',
      },
    },
    cell: ({ row }) => <NumberFormat value={row.original.verifiedCount} />,
  },
  {
    accessorKey: 'rejectedCount',
    header: 'Rejected',
    meta: {
      cellProps: {
        className: 'text-center',
      },
    },
    cell: ({ row }) => <NumberFormat value={row.original.rejectedCount} />,
  },
  {
    header: 'Completed',
    accessorFn: (row) => getDisplayCompleted(row.completedDate),
    meta: {
      cellProps: {
        className: 'text-center',
      },
    },
  },
  {
    header: 'Completed Date',
    accessorFn: (row) => (row.completedDate ? formatDate(row.completedDate, DATE.DEFAULT_SPACES) : null),
    sortingFn: (a, b) => naturalSort(a.original.completedDate ?? '', b.original.completedDate ?? ''),
  },
  {
    accessorKey: tagKey,
    header: 'Verified SGX Core Qs',
    meta: {
      cellProps: {
        className: 'text-center',
      },
    },
    cell: ({ row }) => <NumberFormat value={row.original[tagKey]} />,
  },
];

const Survey = () => {
  const history = useHistory();
  const [searchText, setSearchText] = React.useState('');
  const [hasError, setHasError] = React.useState(false);
  const [rows, setRows] = React.useState<StatsData[] | undefined>();

  const staffFilterProps = useStaffFilters();
  const { extraFilterApiParams, ...staffFilters } = staffFilterProps;

  const extraProps = useMemo(
    () => ({
      scopeTag: SGXTag,
      ...extraFilterApiParams,
    }),
    [extraFilterApiParams]
  );

  const successCallback = React.useCallback((data?: StatsData[]) => {
    setHasError(false);
    setRows(data);
  }, []);

  const errorCallback = React.useCallback(() => {
    setHasError(true)
  }, []);

  useReportData({
    reportType: ReportTypes.Survey,
    successCallback,
    errorCallback,
    extraProps,
    dateParams: convertToDateParams(staffFilterProps.dateRange),
  });

  const handleDownloadStatistic = () => {
    if (!rows || rows.length === 0) {
      return;
    }
    // Headers must be strings
    const rowHeaders = columns.map((col) => col.header as string);
    const rowValues = filteredRows.map((r) => [
      r.initiativeName,
      r.surveyName,
      formatDate(r.created, DATE.DEFAULT_SPACES),
      r.scopeCount,
      r.answeredCount,
      r.verifiedCount,
      r.rejectedCount,
      getDisplayCompleted(r.completedDate),
      r.completedDate ? formatDate(r.completedDate, DATE.DEFAULT_SPACES) : '',
      r[tagKey],
    ]);
    const data = [[rowHeaders, ...rowValues]];
    const fileName = `${SURVEY.SINGULAR}-statistics_${formatDate(new Date(), DATE.FILE_NAME)}`;

    const workBook = getWorkbook(data, {
      title: fileName,
      subject: fileName,
      author: 'g17eco',
      createDate: new Date(),
      sheetNames: [`${SURVEY.CAPITALIZED_SINGULAR} Statistics`],
    });
    const blob = new Blob([convertBinaryToOctetStream(workBook)], { type: 'application/octet-stream' });
    FileSaver.saveAs(blob, `${fileName}.xlsx`);
  };

  const filteredRows = React.useMemo(() => {
    if (!rows) {
      return [];
    }

    const filterScore = (row: StatsRow) => {
      if (searchText) {
        const escapedSearchText = escapeRegexCharacters(searchText.trim().toLowerCase());
        const searchTextRegex = new RegExp(escapedSearchText.replace(/ /g, '|'), 'g');
        const searchString = `${row.surveyName}`.toLowerCase();
        return (searchString.match(searchTextRegex) || []).length;
      }
      return 1;
    }

    return rows
      .map(r => {
        return {
          ...r,
          surveyName: r.name || `${r.initiativeName} ${formatDate(r.effectiveDate, DATE.MONTH_YEAR, true)}`,
          onClick: () => history.push(
            generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, { initiativeId: r.initiativeId, surveyId: r._id, page: 'overview' })
          )
        }
      })
      .filter(filterScore);
  }, [rows, searchText, history]);

  if (hasError) {
    return <ErrorComponent />;
  }

  const total = {
    count: filteredRows.length,
    scopeCount: filteredRows.reduce((acc, d) => acc + d.scopeCount, 0),
    answeredCount: filteredRows.reduce((acc, d) => acc + d.answeredCount, 0),
    verifiedCount: filteredRows.reduce((acc, d) => acc + d.verifiedCount, 0),
    completedCount: filteredRows.reduce((acc, d) => acc + (d.completedDate ? 1 : 0), 0)
  };

  const isLoading = rows === undefined;

  return (
    <Dashboard hasSidebar>
      <SettingsSidebar>
        <SettingsSection
          title={`Search ${SURVEY.CAPITALIZED_PLURAL}`}
          subtitle={`Search for specific ${SURVEY.PLURAL} by name.`}
          icon='fa-search'
          buttons={[
            {
              icon: 'fa-times-circle',
              tooltip: 'Clear search',
              onClick: () => setSearchText(''),
            }
          ]}>
          <SearchBox
            handleOnChange={(e) => setSearchText(String((e.target as HTMLInputElement).value))}
            value={searchText}
          />
        </SettingsSection>
        <StaffFilters {...staffFilters} reportType={ReportTypes.Survey} />
      </SettingsSidebar>
      <DashboardRow>
        <DashboardColumn
          title={`Number of ${SURVEY.CAPITALIZED_PLURAL}`}
          flexBasisPc={'20%'}
        >
          <div className='text-right'>
            <NumberFormat value={total.count} />
          </div>
        </DashboardColumn>
        <DashboardColumn
          title={`Completed ${SURVEY.CAPITALIZED_PLURAL}`}
          flexBasisPc={'20%'}
        >
          <div className='text-right'>
            <NumberFormat value={100 * total.completedCount / total.count} decimalPlaces={1} suffix={'%'} />
          </div>
        </DashboardColumn>
        <DashboardColumn
          title={`Total ${QUESTION.CAPITALIZED_PLURAL}`}
          flexBasisPc={'20%'}
        >
          <div className='text-right'>
            <NumberFormat value={total.scopeCount} />
          </div>
        </DashboardColumn>
        <DashboardColumn
          title={`Answered ${QUESTION.CAPITALIZED_PLURAL}`}
          flexBasisPc={'20%'}
        >
          <div className='text-right'>
            <NumberFormat value={100 * total.answeredCount / total.scopeCount} decimalPlaces={1} suffix={'%'} />
          </div>
        </DashboardColumn>
        <DashboardColumn
          title={`Verified ${QUESTION.CAPITALIZED_PLURAL}`}
          flexBasisPc={'20%'}
        >
          <div className='text-right'>
            <NumberFormat value={100 * total.verifiedCount / total.scopeCount} decimalPlaces={1} suffix={'%'} />
          </div>
        </DashboardColumn>
      </DashboardRow>
      <DashboardSection
        title={<span>{SURVEY.CAPITALIZED_SINGULAR} Statistics <i className='fas fa-file-excel cursor-pointer ml-2' onClick={handleDownloadStatistic}></i></span>}
        icon='fa-clipboard-list'
      >
        {isLoading
          ?
          <Loader />
          :
          filteredRows.length === 0
            ?
            <>No results</>
            :
            <Table<StatsRow>
              columns={columns}
              data={filteredRows}
            />
        }
      </DashboardSection>
    </Dashboard>
  );
}

export default Survey;
