/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { Button, Form, FormGroup } from 'reactstrap';
import { getUtcDateUnit, isSame } from '@utils/date';
import { updateFn } from '@g17eco/molecules/form';
import { loadConfigById, updateConfigById } from '@g17eco/slices/surveyConfig';
import { processSelfOnboarding } from '@actions/api';
import { uniqueSurveyDatesBySurveyListSummary } from '@constants/initiative';
import { loadSurveyListSummaryByInitiativeId, reloadSurvey, reloadSurveyListSummary } from '@actions/survey';
import { ROUTES } from '@constants/routes';
import { AnalyticsEvents } from '@services/analytics/AnalyticsEvents';
import { getAnalytics } from '@services/analytics/AnalyticsService';
import { DashboardSection } from '../dashboard';
import { reloadSurveyList } from '@g17eco/slices/initiativeSurveyListSlice';
import { useAppDispatch, useAppSelector } from '@reducers/index';
import { generateUrl } from '@routes/util';
import { SurveyContext } from '../survey-container/SurveyContainer';
import { Loader } from '@g17eco/atoms/loader';
import { SurveyUnitsAndCurrencyCollapse } from '@features/units-currency-settings';
import { DataPeriods } from '@g17eco/types/universalTracker';
import { SurveySettings } from './partials/SurveySettings';
import { SurveySettingsMenu } from './partials/SurveySettingsMenu';
import { SurveySettingsHeader } from './partials/SurveySettingsHeader';
import NotAuthorised from '@routes/not-authorised';
import { blueprintDefaultUnitConfig, FormScheduledDate, UnitConfig } from '@models/surveyData';
import { SurveyType } from '@g17eco/types/survey';
import './SurveyConfiguration.scss';
import { ConfigFormData, DefaultBlueprintCode, ErrorState } from './types';
import { getCurrentUser, isStaff } from '@selectors/user';
import { SurveyPermissions } from '@services/permissions/SurveyPermissions';
import { InitiativePermissions } from '@services/permissions/InitiativePermissions';
import { useGetDefaultSurveyConfigQuery } from '@api/default-survey-config';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { hasSurveyConflict } from '@utils/survey';
import { NoteInstructionCollapseContainer } from '@features/note-instructions/NoteInstructionContainer';
import { SurveyDeadlineReminderCollapse } from '@features/survey-deadline';
import {
  compareScheduledDates,
  createEffectiveDate,
  editRequiredFields,
  ERRORS,
  isConfirmButtonDisabled,
  isValidDeadlineScheduledDates,
  requiredFields,
  toFormScheduledDates
} from './utils';
import { QUESTION, SURVEY } from '@constants/terminology';
import { hasEditorStateChanged } from '@utils/lexical';
import { BasicAlert } from '@g17eco/molecules/alert';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { CompanySettingsPages } from '@apps/company-tracker/company-tracker-types';

const analytics = getAnalytics();

const alertReportConflict = <div>
  <b>WARNING:</b> A Report already exists for the selected Organization, Level
  and Period. Please update your settings.
</div>;

const alertReportLimit = <div>
  <b>WARNING:</b> You have reached your limit of Report {SURVEY.CAPITALIZED_PLURAL} for this organization. Please contact your account manager for more information.
</div>;

const SurveyConfigurationError = ({
  errorState,
  deadlineDate,
  scheduledDates,
}: {
  errorState: ErrorState;
  deadlineDate?: string;
  scheduledDates?: FormScheduledDate[];
}) => {
  if (errorState.fields.length > 0) {
    return <div className='text-sm text-ThemeDangerMedium text-right mt-2'>Complete required fields</div>;
  }

  if (!isValidDeadlineScheduledDates({ deadlineDate, scheduledDates })) {
    return <div className='text-sm text-ThemeDangerMedium text-right mt-2'>Error: Invalid deadline reminder</div>;
  }

  return null;
};

const initialState: ConfigFormData = {
  verificationRequired: true,
  evidenceRequired: true,
  noteRequired: false,
  isPrivate: false,
  sourceName: DefaultBlueprintCode,
};

export const SurveyConfiguration = () => {
  const { surveyData, disabled } = useContext(SurveyContext);

  const dispatch = useAppDispatch();
  const surveyId = surveyData?._id ?? undefined;
  const history = useHistory();
  const { initiativeId = '' } = useParams<{ initiativeId: string }>();

  const [state, setState] = useState({
    isLoading: false,
    message: '',
    error: false
  });
  const { isLoading, message } = state;
  const canSetNoteInstructions = useAppSelector(FeaturePermissions.canSetNoteInstructions);

  const {
    loaded: surveyConfigLoaded,
    data: surveyConfig,
    errorMessage,
    errored,
  } = useAppSelector((state) => state.surveyConfig);

  const currentSurveyConfig = surveyId && surveyId === surveyConfig?._id ? surveyConfig : undefined;

  const [errorState, setErrorState] = useState<ErrorState>({
    error: ERRORS.NO_ERROR,
    fields: []
  });
  const updateErrorState = (key: keyof ErrorState, value: ErrorState['error'] | ErrorState['fields']) => {
    setErrorState((prev) => ({ ...prev, [key]: value }));
  };

  const [unitConfig, setUnitConfig] = useState(surveyConfig?.unitConfig);
  const [form, setForm] = useState<ConfigFormData>({
    ...initialState,
    _id: currentSurveyConfig?._id,
    noteInstructionsEditorState: currentSurveyConfig?.noteInstructionsEditorState,
    noteInstructions: currentSurveyConfig?.noteInstructions,
    initiativeId
  });

  const updateForm: updateFn = useCallback((update) => {
    if (update.code === 'deadlineDate' && !update.value) {
      setForm((f) => ({ ...f, deadlineDate: undefined, scheduledDates: [] }));
      return;
    }
    if (requiredFields.includes(update.code as keyof ConfigFormData)) {
      updateErrorState(
        'fields',
        errorState.fields.filter((f) => f !== update.code)
      );
    }
    if (isEditMode && editRequiredFields.includes(update.code as keyof ConfigFormData)) {
      updateErrorState(
        'fields',
        errorState.fields.filter((f) => f !== update.code)
      );
    }
    setForm((f) => ({ ...f, [update.code]: update.value }));
  }, []);

  const initiativeState = useAppSelector((state) => state.initiative);
  const surveyListSummaryState = useAppSelector((state) => state.surveyListSummary);
  const currentUser = useAppSelector(getCurrentUser);
  const limitSurveys = useAppSelector(FeaturePermissions.getLimitSurveys);

  const canManage = useMemo(() => {
    if (!currentUser) {
      return false;
    }
    if (surveyData.initiativeId) {
      return SurveyPermissions.canManage({ initiativeId: surveyData.initiativeId }, currentUser);
    }
    return InitiativePermissions.canManageInitiative(currentUser, initiativeId);
  }, [currentUser, surveyData, initiativeId]);

  const isLoaded = !isLoading && initiativeState.loaded && surveyListSummaryState.loaded && (!surveyId || surveyConfigLoaded);

  // Either we do not have surveyId, or we do, and it's the one we are have in the form;
  // Only then we consider form fully loaded with the right data
  const isFormFullyLoaded = !surveyId || (surveyId === form._id);

  const isEditMode = Boolean(currentSurveyConfig);
  const existingSurveys: Set<string> = useMemo(() => uniqueSurveyDatesBySurveyListSummary(surveyListSummaryState.data), [surveyListSummaryState]);

  const isCombinedReport = useMemo(() => surveyData?.type === SurveyType.Aggregation, [surveyData]);

  const hasChanged = useMemo(() => {
    if (form.name) {
      return true;
    }
    if (currentSurveyConfig?.period !== form.period) {
      return true;
    }
    if (currentSurveyConfig?.evidenceRequired !== form.evidenceRequired) {
      return true;
    }
    if (currentSurveyConfig?.noteRequired !== form.noteRequired) {
      return true;
    }
    if (currentSurveyConfig?.verificationRequired !== form.verificationRequired) {
      return true;
    }
    if (currentSurveyConfig?.isPrivate !== form.isPrivate) {
      return true;
    }
    if (getUtcDateUnit('year', currentSurveyConfig?.effectiveDate) !== form.year) {
      return true;
    }
    if (getUtcDateUnit('month', currentSurveyConfig?.effectiveDate) !== form.month) {
      return true;
    }
    if (currentSurveyConfig?.noteInstructions !== form.noteInstructions) {
      return true;
    }

    if (hasEditorStateChanged({
      current: currentSurveyConfig?.noteInstructionsEditorState,
      previous: form.noteInstructionsEditorState
    })) {
      return true;
    }

    if (!isSame(currentSurveyConfig?.deadlineDate, form.deadlineDate)) {
      return true;
    }
    if (!compareScheduledDates(currentSurveyConfig?.scheduledDates, form.scheduledDates)) {
      return true;
    }
    const unitFields = Object.keys(blueprintDefaultUnitConfig) as (keyof UnitConfig)[];
    if (unitConfig) {
      return unitFields.some(unitCode => unitConfig[unitCode] !== surveyData.unitConfig?.[unitCode]);
    }
    return false;
  }, [form, currentSurveyConfig, unitConfig, surveyData]);

  const isCompleted = Boolean(surveyData.completedDate);
  const isConfirmDisabled = isCompleted || !isLoaded || isConfirmButtonDisabled(form, errorState) || !hasChanged;

  useEffect(() => {
    if (initiativeId) {
      dispatch(loadSurveyListSummaryByInitiativeId(initiativeId));
    }
  }, [dispatch, initiativeId]);

  useEffect(() => {
    if (surveyId) {
      dispatch(loadConfigById(surveyId))
    }
  }, [dispatch, surveyId])

  useEffect(() => {
    if (currentSurveyConfig) {
      const {
        evidenceRequired,
        noteRequired,
        verificationRequired,
        isPrivate,
        initiative,
        effectiveDate,
        period,
        sourceName,
        unitConfig,
        noteInstructions,
        deadlineDate,
        scheduledDates,
      } = currentSurveyConfig;

      setForm((form) => ({
        ...form,
        _id: currentSurveyConfig._id,
        unitConfig,
        evidenceRequired,
        noteRequired,
        verificationRequired,
        isPrivate,
        initiative,
        sourceName,
        initiativeId: initiative?._id,
        period: period ?? DataPeriods.Yearly,
        year: getUtcDateUnit('year', effectiveDate),
        month: getUtcDateUnit('month', effectiveDate),
        noteInstructions,
        noteInstructionsEditorState: currentSurveyConfig.noteInstructionsEditorState,
        deadlineDate,
        scheduledDates: toFormScheduledDates(scheduledDates),
      }));

      setUnitConfig(unitConfig);
    }
  }, [currentSurveyConfig]);

  useEffect(() => {
    if (!form.initiativeId) {
      return;
    }

    const isEditMode = currentSurveyConfig?._id;
    if (isEditMode) {
      // Don't care about survey limits for existing surveys
      if (errorState.error !== ERRORS.NO_ERROR) {
        updateErrorState('error', ERRORS.NO_ERROR);
      }
      return;
    }

    if (FeaturePermissions.hasReachedLimit({ current: existingSurveys.size, limit: limitSurveys })) {
      updateErrorState('error', ERRORS.REPORT_LIMIT);
      return;
    }

    if (errorState.error !== ERRORS.NO_ERROR) {
      updateErrorState('error', ERRORS.NO_ERROR);
    }
  }, [limitSurveys, form, errorState, currentSurveyConfig, existingSurveys.size])

  useEffect(() => {
    const id = form.initiativeId;
    const hasConflict = hasSurveyConflict(
      surveyListSummaryState.data,
      {
        initiativeId: id,
        year: form.year,
        month: form.month,
        type: (surveyData.type ?? SurveyType.Default),
        sourceName: form.sourceName,
        period: form.period,
      },
      surveyId
    );

    if (hasConflict) {
      updateErrorState('error', ERRORS.REPORT_CONFLICT);
    } else if (errorState.error === ERRORS.REPORT_CONFLICT) {
      updateErrorState('error', ERRORS.NO_ERROR);
    }
  }, [
    existingSurveys,
    form.initiativeId,
    form.year,
    form.month,
    form.period,
    form.sourceName,
    surveyListSummaryState.data,
    errorState,
    surveyId,
    surveyData.type
  ]);

  const { data: defaultSurveyConfig, isLoading: isLoadingSurveyConfig } = useGetDefaultSurveyConfigQuery({
    initiativeId,
  });

  useEffect(() => {
    if (isEditMode || isLoadingSurveyConfig || !defaultSurveyConfig) {
      return;
    }

    const { unitConfig, verificationRequired, isPrivate, evidenceRequired, noteRequired } = defaultSurveyConfig;
    setUnitConfig(unitConfig);
    setForm((prev) => ({ ...prev, verificationRequired, isPrivate, evidenceRequired, noteRequired }));
  }, [isEditMode, isLoadingSurveyConfig, defaultSurveyConfig]);

  const handleCreateSubmit = async () => {
    setState({ ...state, isLoading: true });
    const effectiveDate = createEffectiveDate(form) ?? new Date();
    const createdSurvey = await processSelfOnboarding({ ...form, effectiveDate, unitConfig });
    await analytics.track(AnalyticsEvents.SurveyCreated, {
      surveyId: createdSurvey._id,
      initiativeId: createdSurvey.initiativeId
    });
    dispatch(reloadSurveyList())
    dispatch(reloadSurveyListSummary());
    setState({ ...state, isLoading: false, message: 'Success' });

    if (createdSurvey._id) {
      history.push(
        generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, {
          initiativeId: createdSurvey.initiativeId,
          surveyId: createdSurvey._id,
          page: 'scope'
        })
      );
    }
  };

  const isValidForm = () => {
    const invalidFields = (isEditMode ? editRequiredFields : requiredFields).filter((f) => undefined === form[f] || form[f] === '');
    if (invalidFields.length) {
      updateErrorState('fields', invalidFields);
      return false;
    }
    return true;
  };

  const handleSubmit = () => {
    if (!isValidForm()) {
      return;
    }

    if (!currentSurveyConfig?._id) {
      handleCreateSubmit().catch(e => setError(e));
      return;
    }

    setState({ ...state, isLoading: true });
    const {
      evidenceRequired,
      noteRequired,
      verificationRequired,
      isPrivate,
      period,
      name,
      sourceName,
      noteInstructions,
      deadlineDate,
      scheduledDates,
    } = form;

    dispatch(updateConfigById(currentSurveyConfig._id, {
      name,
      period,
      evidenceRequired,
      noteRequired,
      verificationRequired,
      isPrivate,
      sourceName,
      effectiveDate: createEffectiveDate(form),
      unitConfig,
      noteInstructions,
      noteInstructionsEditorState: form.noteInstructionsEditorState,
      deadlineDate,
      scheduledDates,
    })).then(() => {
      dispatch(reloadSurvey(currentSurveyConfig._id));
      dispatch(reloadSurveyListSummary()); // Need to reload, as it blocks view otherwise
      dispatch(reloadSurveyList());
      setState({ ...state, error: false, isLoading: false, message: 'Success' });
    }).catch((e: Error) => setError(e));
  };

  const setError = (e: Error) => setState({ ...state, error: true, isLoading: false, message: e.message });

  const isUserStaff = useAppSelector(isStaff);

  if (!canManage) {
    return isLoaded ? <NotAuthorised /> : <Loader />;
  }

  const goBack = () => {
    if (surveyId) {
      return history.push(generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, { initiativeId, surveyId, page: 'overview' }));
    }
    return history.push(generateUrl(ROUTES.COMPANY_TRACKER_LIST, { initiativeId }));
  }

  const goToDefaultSettings = () => {
    return history.push(generateUrl(ROUTES.ACCOUNT_SETTINGS, { rootAppPath: 'company-tracker', initiativeId, page: CompanySettingsPages.ReportSettings }));
  }

  const title = `${SURVEY.CAPITALIZED_SINGULAR} details${isEditMode ? '' : `: Create new ${SURVEY.SINGULAR}`}`;
  const additionalUnitConfig = {
    ...surveyData?.config?.unitConfig ?? {},
    ...surveyData?.unitConfig ?? {},
  };
  const disabledCurrencyOption = {
    currency: {
      disabled: true,
      canEnable: isUserStaff
    },
  };

  return (
    <>
      <SurveySettingsHeader />
      <DashboardSection title={title}>
        <SurveySettingsMenu />
        <BasicAlert type={state.error || errored ? 'danger' : 'success'}>{errorMessage || message}</BasicAlert>
        {!isLoaded ? <Loader /> : <></>}
        <LoadingPlaceholder className='mt-4' count={1} height={800} isLoading={!isLoaded || !isFormFullyLoaded}>
          <Form className='survey-config-form'>
            {!isEditMode && (
              <BasicAlert hide={errorState.error !== ERRORS.REPORT_LIMIT} type={'warning'} className='mt-2'>
                {alertReportLimit}
              </BasicAlert>
            )}
            <BasicAlert hide={errorState.error !== ERRORS.REPORT_CONFLICT} type={'warning'}>
              {alertReportConflict}
            </BasicAlert>

            <SurveySettings updateForm={updateForm} form={form} errorState={errorState} />

            {isCombinedReport ? null : (
              <FormGroup disabled={errorState.error === ERRORS.REPORT_LIMIT}>
                <SurveyUnitsAndCurrencyCollapse
                  isCreating={!isEditMode}
                  unitConfig={unitConfig}
                  setUnitConfig={setUnitConfig}
                  isDisabled={Boolean(surveyData.completedDate) || disabled}
                  additionalUnitConfig={additionalUnitConfig}
                  warningText={
                    <>
                      <b>WARNING:</b> Updating default units will only affect unanswered {QUESTION.PLURAL}. Changing the
                      currency will update all {QUESTION.PLURAL} but any inputed financial values will not be converted.
                    </>
                  }
                  fieldOptions={disabledCurrencyOption}
                />
              </FormGroup>
            )}
            <NoteInstructionCollapseContainer
              canSetNoteInstructions={canSetNoteInstructions}
              editorId={surveyConfig?._id ?? 'new-config'}
              updateForm={updateForm}
              disabled={Boolean(surveyData.completedDate) || disabled}
              noteInstructionsEditorState={form.noteInstructionsEditorState}
              noteInstructions={form.noteInstructions}
            />
            <SurveyDeadlineReminderCollapse
              deadlineDate={form.deadlineDate}
              scheduledDates={form.scheduledDates}
              updateForm={updateForm}
            />
            <div className='d-flex justify-content-between align-items-center mt-5'>
              {isEditMode ? (
                <Button color='secondary' onClick={goToDefaultSettings} className='mr-3'>
                  <i className='fal fa-sliders text-ThemeIconSecondary mr-2' />
                  Default {SURVEY.SINGULAR} settings
                </Button>
              ) : (
                // Show span for creating mode to keep content between for buttons
                <span />
              )}
              <div>
                <div>
                  <Button color='link-secondary' onClick={goBack} className='mr-3'>
                    Cancel
                  </Button>
                  <Button
                    color='primary'
                    className='px-3'
                    disabled={isConfirmDisabled}
                    onClick={handleSubmit}
                    data-testid='survey-configs-submit-btn'
                  >
                    {isEditMode ? <><i className='fal fa-floppy-disk' /> Save</> : `Create ${SURVEY.SINGULAR}`}
                  </Button>
                </div>
                <SurveyConfigurationError
                  errorState={errorState}
                  deadlineDate={form.deadlineDate}
                  scheduledDates={form.scheduledDates}
                />
              </div>
            </div>
          </Form>
        </LoadingPlaceholder>
      </DashboardSection>
    </>
  );
}
