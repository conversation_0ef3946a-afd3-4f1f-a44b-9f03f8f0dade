/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { useHistory, useParams } from 'react-router-dom';
import { Button } from 'reactstrap';
import { ROUTES } from '../../../constants/routes';
import { generateUrl } from '../../../routes/util';
import { DashboardRow } from '../../dashboard';
import { SURVEY } from '@constants/terminology';

export const SurveySettingsHeader = () => {
  const { initiativeId, surveyId } = useParams<{ initiativeId: string, surveyId: string }>();
  const history = useHistory();

  const goBack = () => {
    if (surveyId) {
      return history.push(generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, { initiativeId, surveyId, page: 'overview' }));
    }
    return history.push(generateUrl(ROUTES.COMPANY_TRACKER_LIST, { initiativeId }));
  }

  return (
    <DashboardRow>
      <div className='text-left'>
        <Button color='link' onClick={goBack}>
          <i className='fa fa-arrow-circle-left mr-2' />
          {surveyId ? `Go back to ${SURVEY.SINGULAR} overview` : `Go back to ${SURVEY.PLURAL} list`}
        </Button>
      </div>
    </DashboardRow>
  );
}
