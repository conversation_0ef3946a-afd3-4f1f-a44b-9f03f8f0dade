/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory, useLocation, useParams } from 'react-router-dom';
import * as actions from '../../actions/survey';
import { loadDelegationUsers, reloadDelegationUsers } from '../../slice/surveyDelegationUsers';
import { isSurveyIdValid } from '../../utils/surveyData';
import { CardGridViewMode } from '../survey-scope/CardGrid';
import { createFilterUrl, useBreadcrumbs } from '../survey-settings-toolbar/useBreadcrumbs';
import { Breadcrumb, ScopeQuestion, ScopeQuestionOptionalValue, UNSDGMap } from '../../types/surveyScope';
import { loadUNSDGMap } from '../../actions/common';
import { getCurrentUser } from '../../selectors/user';
import { UserMin } from '../../constants/users';
import { getAsScopeQuestionNoValue, loadBlueprintQuestions } from '../../slice/blueprintQuestions';
import { BreadcrumbsRouteParams } from '../survey-settings-toolbar/breadcrumbPath';
import { RootState, useAppDispatch, useAppSelector } from '../../reducers';
import {
  isSurveyActionData,
  SurveyActionData,
  NonStaticGroupConfig,
  GroupType,
  GroupData,
} from '../../model/surveyData';
import { MetricGroup } from '../../types/metricGroup';
import { ROUTES } from '../../constants/routes';
import SurveyOverviewHeader from './SurveyOverviewHeader';
import Dashboard, { DashboardSection } from '../dashboard';
import { LG_BREAKPOINT } from '../../utils';
import queryString from 'query-string';
import { generateUrl } from '../../routes/util';
import { Loader } from '@g17eco/atoms/loader';
import { BlueprintContributions, SurveySettings } from '../../types/survey';
import { useScopeFilters } from '../survey/utils/useScopeFilters';
import { ViewValues } from '../survey-overview-sidebar/viewOptions';
import { MaterialityObject } from '../survey/utils/filters';
import './style.scss';
import { getQuestionListMap } from '../../selectors/survey';
import { isAggregatedSurvey, isAutoAggregatedSurvey } from '../../utils/survey';
import { AggregatedSurveyOverviewHeader } from '@components/auto-aggregated-survey/AggregatedSurveyOverviewHeader';
import { useAppSettings } from '@hooks/app/useAppSettings';
import { SURVEY } from '@constants/terminology';
import { useGetBlueprintsQuery } from '@api/blueprints';
import { useSearchParams } from 'react-router-dom-v5-compat';
import { isValidObjectId } from '@utils/string';
import { skipToken } from '@reduxjs/toolkit/query';
import { SurveyPermissions } from '@services/permissions/SurveyPermissions';

interface SurveySettingsProps {
  children: any;
}

export type InitialFilters = Omit<SurveySettings, 'groupBy' | 'viewLayout'>;

export const initialFilters: InitialFilters = {
  filterByStatus: [],
  filterByDelegationStatus: [],
  filterByMateriality: [],
  filterByGoal: [],
  filterByRegulatory: [],
  filterByModules: [],
  filterByRole: [],
  filterByUser: [],
  filterByTag: [],
  searchText: '',
  filterByBookmarks: false,
};

const isMobile = window.innerWidth <= LG_BREAKPOINT;

const initialSidebar: SurveySettings = {
  groupBy: ['sdg'],
  viewLayout: isMobile ? CardGridViewMode.list : CardGridViewMode.gallery,
  ...initialFilters,
};

type SettingValueTypes = string[] | boolean | string | CardGridViewMode;

export type HandleChangeSettingsType = (key: keyof SurveySettings, setting: SettingValueTypes, route?: string) => void;
export interface SurveyContextCommonProps {
  isLoaded: boolean;
  disabled: boolean;
  isReadOnly: boolean;
  surveyData: SurveyActionData;
  reloadSurvey: (showLoading?: boolean) => Promise<void>;
  handleChangeSettings: HandleChangeSettingsType;
  sidebarSettings: SurveySettings;
  breadcrumbs: Breadcrumb[];
  setBreadcrumbs: (breadcrumbs: Breadcrumb[], groupBy?: string) => void;
  questions: (ScopeQuestion | ScopeQuestionOptionalValue)[];
  UNSDGMap?: UNSDGMap;
  blueprint?: BlueprintContributions;
  metricGroups: MetricGroup[];
  users: UserMin[];
  isUserManager: boolean;
  utrvId: string | undefined;
  removeUtrvIdFromSearchParams: () => void;
}

interface SurveyContextUnloadedProps extends Omit<SurveyContextCommonProps, 'surveyData'> {
  isLoaded: false;
  surveyData: Partial<SurveyActionData>;
}

export interface SurveyContextLoadedProps extends SurveyContextCommonProps {
  isLoaded: true;
  surveyData: SurveyActionData;
  UNSDGMap: UNSDGMap;
  materiality?: MaterialityObject;
  blueprint: BlueprintContributions;
  selectedView: ViewValues;
  setSelectedView: (view: ViewValues) => void;
}

export type SurveyContextProps = SurveyContextUnloadedProps | SurveyContextLoadedProps;

const initialState: SurveyContextUnloadedProps = {
  isLoaded: false,
  isReadOnly: false,
  surveyData: {},
  metricGroups: [],
  disabled: false,
  breadcrumbs: [],
  reloadSurvey: () => Promise.resolve(),
  handleChangeSettings: () => {},
  setBreadcrumbs: () => {},
  sidebarSettings: initialSidebar,
  questions: [],
  users: [],
  isUserManager: false,
  utrvId: undefined,
  removeUtrvIdFromSearchParams: () => {},
};

const queryOption: {
  [key: string]: 'bracket';
} = { arrayFormat: 'bracket' };

const filterKeys = Object.keys(initialFilters) as (keyof InitialFilters)[];

const getInitialFiltersFromQuery = (searchParams: string) => {
  const query = queryString.parse(searchParams, queryOption);

  // Only allow valid keys
  Object.keys(query).forEach((k) => {
    if (!filterKeys.includes(k as keyof InitialFilters)) {
      delete query[k];
    }
  });

  return query;
};

export const SurveyContext = React.createContext<SurveyContextProps>(initialState);

export const createQueryFilters = (filterKeys: (keyof InitialFilters)[], update: InitialFilters) => {
  return filterKeys.reduce((a, k) => {
    switch (k) {
      case 'searchText':
        if (update.searchText !== '') {
          a.searchText = update.searchText;
        }
        break;
      case 'filterByBookmarks':
        if (update.filterByBookmarks) {
          a.filterByBookmarks = true;
        }
        break;
      default:
        a[k] = update[k];
        break;
    }

    return a;
  }, {} as Partial<InitialFilters>);
};

export default function SurveyContainer(props: SurveySettingsProps) {
  const params = useParams<BreadcrumbsRouteParams>();
  const location = useLocation();
  const isDelegationPage = location.pathname.endsWith('/delegation');
  const { canViewAllPacks } = useAppSettings();

  const getView = (view: string) => {
    if (!view) {
      if (isDelegationPage) {
        return ViewValues.Users;
      }

      return canViewAllPacks ? ViewValues.AllPacks : ViewValues.QuestionPacks;
    }
    if (view === ViewValues.AllPacks && !canViewAllPacks) {
      return ViewValues.QuestionPacks;
    }
    return view;
  };

  const { initiativeId = '', surveyId = '' } = params;
  const view = getView(params.view);
  const page = initiativeId === 'create' || surveyId === 'create' ? 'create' : (params.page ?? '');

  const history = useHistory();
  const dispatch = useAppDispatch();
  const mountedRef = useRef(true);

  const [selectedView, setSelectedView] = useState<ViewValues>(ViewValues.StandardsAndFrameworks);
  const [disabled, setDisabled] = useState(false);
  const [sidebarSettings, setSettings] = useState(() => {
    const initialFiltersFromQuery = getInitialFiltersFromQuery(history.location.search);
    return {
      ...initialSidebar,
      groupBy: [view],
      ...initialFiltersFromQuery,
      filterByBookmarks: initialFiltersFromQuery.filterByBookmarks === 'true',
    };
  });

  const [searchParams, setSearchParams] = useSearchParams();
  const utrvId = searchParams.get('utrvId');

  const removeUtrvIdFromSearchParams = () => {
    if (!utrvId) {
      return;
    }
    setSearchParams((prev) => {
      prev.delete('utrvId');
      return prev;
    });
  };

  const originalQuestionList = useSelector(getQuestionListMap);
  const baseScopeQuestion = useSelector(getAsScopeQuestionNoValue);
  const surveyState = useAppSelector((state) => state.survey);
  const { errored: surveyError, surveyId: surveyStateId } = surveyState;
  const UNSDGMapState = useAppSelector((state) => state.UNSDGMap);
  const initiativeState = useAppSelector((state) => state.initiative);
  const { loaded: blueprintQuestionsLoaded } = useAppSelector((state) => state.blueprintQuestions);
  const isUserLoaded = useAppSelector((state) => state.currentUser.loaded);
  const isUserManager = useAppSelector((state) => {
    const currentUser = getCurrentUser(state);
    if (!currentUser || !state.survey.loaded) {
      return false;
    }

    return SurveyPermissions.canManage(state.survey.data, currentUser);
  });

  const { data: blueprint = {}, isSuccess: isLoadedBlueprint } = useGetBlueprintsQuery(
    surveyState.loaded ? surveyState.data.sourceName : skipToken,
  );

  const surveyLoaded = surveyState.loaded;
  const isValidId = isSurveyIdValid(surveyId);

  useEffect(() => {
    if (surveyState.loaded) {
      if (!surveyState.data.sourceName) {
        console.error('Missing sourceName for this survey so will not load blueprint');
      }
      dispatch(loadUNSDGMap());
      if (isUserManager) {
        dispatch(loadBlueprintQuestions(surveyState.data._id));
        dispatch(loadDelegationUsers(surveyState.data._id));
      }
    }
  }, [isUserManager, surveyId, surveyState, dispatch]);

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    if (!mountedRef.current) return;
    setSettings((s) => ({ ...s, groupBy: [view] }));
  }, [view, mountedRef]);

  useEffect(() => {
    if (!page) {
      history.push({
        pathname: generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, { initiativeId, surveyId, page: 'overview' }),
        search: location.search,
      });
    }
  });

  useEffect(() => {
    if (!mountedRef.current) return;
    const surveyChanged = surveyLoaded && surveyStateId !== surveyId;

    if (surveyStateId === surveyId && surveyError) {
      return;
    }

    if (isValidId && (!surveyState.loaded || surveyChanged)) {
      setDisabled(true);
      dispatch(actions.loadSurvey(surveyId));
    } else {
      setDisabled(false);
    }
  }, [dispatch, surveyId, surveyLoaded, surveyState, isValidId, surveyError, surveyStateId, mountedRef]);

  const reloadSurvey = async (showLoading: boolean = true) => {
    setDisabled(showLoading);
    dispatch(actions.reloadSurvey(surveyId));
    await dispatch(reloadDelegationUsers(surveyId));
    setDisabled(false);
  };

  // @TODO - replace because this might come from another endpoint?
  const metricGroupToCustomGroupConverter = (metricGroup: MetricGroup): NonStaticGroupConfig => {
    return {
      type: GroupType.Custom,
      groupId: metricGroup._id ?? '',
      groupName: metricGroup.groupName,
      groupData: metricGroup.groupData as GroupData,
      utrCodes: [],
    };
  };

  const surveyData = isSurveyActionData(surveyState.data) ? surveyState.data : undefined;
  const metricGroups = surveyData?.customMetricGroups ?? [];
  const nonManagerLoaded = surveyLoaded && UNSDGMapState.loaded && initiativeState.loaded && isLoadedBlueprint;
  const managerLoaded = nonManagerLoaded && blueprintQuestionsLoaded;
  const everythingLoaded = isUserManager ? managerLoaded : nonManagerLoaded;

  const isLoaded = !disabled && (!isValidId || everythingLoaded);

  const isAggregate = isAggregatedSurvey(surveyData?.type);
  const combinedCustomGroups = surveyLoaded
    ? [...(surveyData?.config?.customGroups ?? []), ...metricGroups.map(metricGroupToCustomGroupConverter)]
    : [];
  const users: UserMin[] = useSelector((state: RootState) => state.surveyDelegationUsers.data.users ?? []);
  const breadcrumbs = useBreadcrumbs(params, users, combinedCustomGroups);
  const setBreadcrumbs = ([first, second, leaf, leafChild]: Breadcrumb[], groupBy?: string) => {
    const path = createFilterUrl({
      surveyId,
      initiativeId: surveyData?.initiativeId ?? params.initiativeId,
      page,
      view: groupBy ? groupBy : view,
      cardCategory: first?.cardCategory,
      cardGroup: first?.cardGroup,
      cardSubCategory: second?.cardCategory,
      cardSubGroup: second?.cardGroup,
      leafCategory: leaf?.cardCategory,
      leafGroup: leaf?.cardGroup,
      leafChildCategory: leafChild?.cardCategory,
      leafChildGroup: leafChild?.cardGroup,
    });
    history.push({ pathname: path, search: history.location.search });
  };

  const materiality = initiativeState.data?.materiality ?? {};

  const questions = useScopeFilters({
    page,
    view: view as ViewValues,
    breadcrumbs,
    blueprint,
    metricGroups,
    materiality,
    originalQuestionList,
    baseScopeQuestion,
  });

  if (!isLoaded || !surveyData || !initiativeState.loaded || !UNSDGMapState.loaded || !isLoadedBlueprint) {
    return <Loader />;
  }

  const isReadOnly = isAggregate || Boolean(surveyData.completedDate);
  const UNSDGMap = UNSDGMapState.data;

  const handleChangeSettings = (key: keyof SurveySettings, setting: any) => {
    const update: SurveySettings = { ...sidebarSettings, [key]: setting };
    setSettings(update);
    if (key === 'groupBy') {
      history.push({
        pathname: createFilterUrl({
          surveyId,
          initiativeId: surveyData.initiativeId ?? params.initiativeId,
          page,
          view: setting[0],
        }),
        search: history.location.search,
      });
    } else if (filterKeys.includes(key as keyof InitialFilters)) {
      history.push({
        pathname: history.location.pathname,
        search: queryString.stringify(createQueryFilters(filterKeys, update), queryOption),
      });
    }
  };

  if (!isValidId) {
    history.push(generateUrl(ROUTES.COMPANY_TRACKER));
    return <></>;
  }

  if (isUserLoaded && surveyLoaded && !isUserManager && page !== 'overview') {
    history.push(
      generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, { initiativeId: surveyData.initiativeId, surveyId, page: 'overview' }),
    );
    return <></>;
  }

  const contextValue: SurveyContextLoadedProps = {
    isLoaded: true,
    disabled,
    isReadOnly,
    surveyData,
    reloadSurvey,
    sidebarSettings,
    handleChangeSettings,
    breadcrumbs,
    metricGroups,
    setBreadcrumbs,
    questions,
    UNSDGMap,
    blueprint,
    users,
    isUserManager,
    materiality,
    selectedView,
    setSelectedView,
    utrvId: isValidObjectId(utrvId) ? utrvId : undefined,
    removeUtrvIdFromSearchParams,
  };

  const showHeader = ['overview'].includes(page);

  return (
    <SurveyContext.Provider value={contextValue}>
      <Dashboard className='survey-settings-container px-3 mt-4'>
        {surveyError ? (
          <DashboardSection
            icon='fa-exclamation-triangle'
            title='Permission Denied'
            subtitle={`You do not have permission to access this ${SURVEY.SINGULAR}`}
          />
        ) : (
          <>
            {showHeader ? (
              isAutoAggregatedSurvey(surveyData?.type) ? (
                <AggregatedSurveyOverviewHeader
                  surveyId={surveyData._id}
                  lastUpdated={surveyData.aggregatedDate ?? surveyData.created}
                  initiativeId={initiativeId}
                />
              ) : (
                <SurveyOverviewHeader />
              )
            ) : null}
            <div className='w-100 mt-4'>{props.children}</div>
          </>
        )}
      </Dashboard>
    </SurveyContext.Provider>
  );
}
