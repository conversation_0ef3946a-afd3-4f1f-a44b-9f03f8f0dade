/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { Fragment, useContext, useState } from 'react';
import { Link } from 'react-router-dom';
import { SurveyContext, SurveyContextLoadedProps } from '../survey-container/SurveyContainer';
import {
  addDelegationScopeUser,
  addSurveyRoleUser,
  removeDelegationScopeUser,
  removeSurveyRoleUser,
  RequestScope
} from '../../actions/api';
import CardGrid, {
  CardGridButtonProps,
  CardGridItem,
  CardGridItemProps
} from '../survey-scope/CardGrid';
import {
  Breadcrumb,
  CardProps,
  handleDrilldownInterface,
} from '../../types/surveyScope';
import { useSelector } from 'react-redux';
import { RootState, useAppDispatch, useAppSelector } from '../../reducers';
import { ViewValues } from '../survey-overview-sidebar/viewOptions';
import DelegationModal from './delegation-modal';
import { SurveyDelegationSearchModal } from '../search-modal';
import { ScopeGroups, SurveyModelMinData } from '../../model/surveyData';
import { roles } from '../../constants/roles';
import { SurveyUserRoles } from '../../constants/users';
import { ROUTES } from '../../constants/routes';
import { ScopeQuestionList, shouldShowScopeQuestions } from '../survey-scope-question-list/ScopeQuestionList';
import { isInDelegationScope, isSurveyDelegator } from '../survey-scope/scopeSelection';
import { StakeholderGroup } from '../../model/stakeholderGroup';
import { getAnalytics } from '../../services/analytics/AnalyticsService';
import { AnalyticsEvents } from '../../services/analytics/AnalyticsEvents';
import { getDelegationCardGroups } from '../survey-scope/cardGroups';
import { DashboardSection } from '../dashboard';
import { frameworks, standards } from '@g17eco/core';
import { generateUrl } from '../../routes/util';
import { Sort, sortItems } from '../../utils/sort';
import { addSiteAlert } from '../../slice/siteAlertsSlice';
import SurveyGroupHeader from '../survey-scope/SurveyGroupHeader';
import SurveySettingsToolbar from '../survey-settings-toolbar';
import IconButton from '../button/IconButton';
import { SurveySettingsMenu } from '../survey-configuration/partials/SurveySettingsMenu';
import { convertToFilters } from '../survey/utils/useScopeFilters';
import { SurveySettingsHeader } from '../survey-configuration/partials/SurveySettingsHeader';
import { isUserManager } from '../../selectors/user';
import NotAuthorised from '../../routes/not-authorised';
import { useAppSettings } from '../../hooks/app/useAppSettings';
import { Button } from 'reactstrap';
import './style.scss';
import { FeaturePermissions } from '../../services/permissions/FeaturePermissions';
import { QUESTION, SURVEY } from '@constants/terminology';
import { BasicAlert } from '@g17eco/molecules/alert';
import { Column, Row, TrackingList } from '@g17eco/molecules/tracking-list';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { ViewDropdown } from '@apps/company-tracker/components/delegation/survey/ViewDropdown';
import { SurveyDelegation as NewSurveyDelegation } from '@apps/company-tracker/components/delegation';

const analytics = getAnalytics();

const isUserView = (breadcrumbs: Breadcrumb[], filter: string) => {
  const lastBreadcrumbs = breadcrumbs[breadcrumbs.length - 1];
  const userCountGroups = [ViewValues.Users, ViewValues.Roles];

  // if root level user view
  if (!lastBreadcrumbs && userCountGroups.includes(filter as ViewValues)) {
    return true;
  }

  const group = lastBreadcrumbs?.cardGroup;
  return userCountGroups.includes(group);
};

const disabledVerificationTooltip = 'Upgrade to access verification';

/** @deprecated use @apps/company-tracker/components/delegation */
export default function SurveyDelegation() {
  const {
    isLoaded: surveyDataLoaded,
    materiality,
    surveyData,
    breadcrumbs,
    setBreadcrumbs,
    reloadSurvey,
    sidebarSettings,
    questions,
    UNSDGMap,
    blueprint,
    metricGroups,
    users,
    handleChangeSettings,
  } = useContext(SurveyContext) as SurveyContextLoadedProps;

  const { viewLayout, groupBy } = sidebarSettings;
  const dispatch = useAppDispatch();
  const initialDelegationModalState: { isOpen: boolean; surveyRole: SurveyUserRoles; scopeTags: undefined | string[] } =
    {
      isOpen: false,
      surveyRole: SurveyUserRoles.Stakeholder,
      scopeTags: undefined,
    };

  const isDisabled = Boolean(surveyData.completedDate);

  const isManager = useAppSelector(isUserManager);
  const canAccessVerification = useAppSelector(FeaturePermissions.canAccessVerification);
  const [currentSort, setSort] = useState(Sort.TitleAsc);
  const [delegationModal, setDelegationModal] = useState(initialDelegationModalState);
  const [viewAsQuestionList, setQuestionListView] = useState(false);
  const view = groupBy[0] as ViewValues;
  const [first] = breadcrumbs;
  const scopeTitle = first?.title;
  const userBreadcrumb = breadcrumbs.find((b) => b.cardGroup === ViewValues.Users);
  const userRole = userBreadcrumb?.title;
  const userRoleCode = userBreadcrumb?.cardCategory as SurveyUserRoles;
  const scopeTags = first?.cardGroup && first.cardGroup !== ViewValues.Users ? [first.cardCategory] : [];
  const scopeType = Object.values(ScopeGroups).includes(view as string as ScopeGroups)
    ? (view as string as ScopeGroups)
    : ScopeGroups.Custom;
  const cardGroup = breadcrumbs.length > 0 ? breadcrumbs[breadcrumbs.length - 1].cardGroup : view;
  const appSettings = useAppSettings();

  const toggleDelegationModal = (
    e: React.MouseEvent<HTMLButtonElement>,
    surveyRole: SurveyUserRoles,
    scopeTags?: string[],
  ) => {
    e.preventDefault();
    setDelegationModal({ isOpen: !delegationModal.isOpen, surveyRole, scopeTags });
  };

  React.useEffect(() => {
    setQuestionListView(cardGroup === ViewValues.QuestionList);
  }, [cardGroup]);

  const { errored, loaded } = useSelector((state: RootState) => state.surveyDelegationUsers);

  const [{ isLoading }, setLoadingState] = useState({ errorMessage: '', isLoading: false });

  if (errored) {
    return <BasicAlert type={'danger'}>Was not able to load users</BasicAlert>;
  }

  const isLoaded = surveyDataLoaded && !isLoading && loaded;

  const handleDrilldown: handleDrilldownInterface = (cardGroup, cardCategory, title, groupBy) => {
    if (groupBy) {
      setBreadcrumbs([{ cardGroup, cardCategory, title }], groupBy);
    } else {
      setBreadcrumbs([...breadcrumbs, { cardGroup, cardCategory, title }]);
    }
  };

  const getApiCall = (
    method: 'add' | 'remove',
    userId: string,
    surveyRole: SurveyUserRoles,
    scopeTags: string[],
    onboardingEmail?: string,
  ) => {
    if ([SurveyUserRoles.Admin, SurveyUserRoles.Monitor].includes(surveyRole) || !scopeTags || scopeTags.length === 0) {
      const apiCall = method === 'add' ? addSurveyRoleUser : removeSurveyRoleUser;
      return apiCall(surveyData._id, surveyRole, userId, scopeTags, onboardingEmail);
    }

    const filters = convertToFilters(view, breadcrumbs);
    const [firstB, ...rest] = filters.filter((b) => !['roles', 'users'].includes(b.type));

    const getScopeType = () => {
      if ([ViewValues.AssignedMetrics, ViewValues.Custom].includes(view)) {
        return ViewValues.Custom;
      }

      if (firstB.value) {
        if (standards[firstB.value]) {
          return ViewValues.Standards;
        }
        if (frameworks[firstB.value]) {
          return ViewValues.Frameworks;
        }
      }

      return view;
    };

    let scopeGroups: RequestScope[] = [];
    if (firstB?.value) {
      scopeGroups = [
        {
          code: firstB.value,
          scopeType: getScopeType(),
          scopeTags: rest?.map((c) => c.value || '').filter(Boolean),
        },
      ];
    }

    const apiCall = method === 'add' ? addDelegationScopeUser : removeDelegationScopeUser;
    return apiCall({
      id: surveyData._id,
      role: surveyRole,
      userId,
      scopeGroups,
      onboardingEmail,
    });
  };

  const updateScope = (method: 'add' | 'remove', userId: string, role: SurveyUserRoles, scopeTags: string[]) => {
    setLoadingState({ errorMessage: '', isLoading: true });
    getApiCall(method, userId, role, scopeTags)
      .then(async (resp) => {
        await analytics.track(
          method === 'remove' ? AnalyticsEvents.SurveyDelegationRemoved : AnalyticsEvents.SurveyDelegationAdded,
          {
            initiativeId: surveyData.initiativeId,
            surveyId: surveyData._id,
            scopeLevel: 'survey',
            scopeGroup: 'survey',
            stakeholderType: role,
          },
        );
        if (resp?.data?.data?._id) {
          await analytics.track(AnalyticsEvents.UserInvited, {
            initiativeId: surveyData.initiativeId,
            surveyId: surveyData._id,
            onboardId: resp.data.data._id,
          });
        }
        setLoadingState({ errorMessage: '', isLoading: false });
        reloadSurvey();
      })
      .catch((e: Error) =>
        setLoadingState({
          errorMessage: e.message,
          isLoading: false,
        }),
      );
  };

  const handleAddUser: (
    props: { userId: string; email?: string },
    scopeTags: string[],
    surveyRole: SurveyUserRoles,
  ) => void = (props, scopeTags, surveyRole) => {
    getApiCall('add', props.userId, surveyRole, scopeTags, props.email)
      .then(() => {
        setDelegationModal({ isOpen: false, surveyRole, scopeTags });
        reloadSurvey();
      })
      .catch(() => {
        dispatch(
          addSiteAlert({
            content: 'Unable to add user. Please try again',
            timeout: 5000,
          }),
        );
      });
  };

  const isUserInScope = (userId: string, userRole: string) => {
    return isInDelegationScope(userId, surveyData, scopeType, breadcrumbs, userRole as keyof StakeholderGroup);
  };

  const isUserDelegated = (userId: string, userRole?: string) => {
    const role = (userRole ? userRole : userRoleCode) as keyof StakeholderGroup;
    const isSurveyLevelDelegator = isSurveyDelegator(userId, surveyData, role);
    if (isSurveyLevelDelegator) {
      return true;
    }
    return isUserInScope(userId, role);
  };

  const addBtn: (value: string, userName?: string) => CardGridButtonProps = (value, userName) => {
    if (![ViewValues.Users, ViewValues.Roles].includes(cardGroup)) {
      return {
        tooltip: 'View User Roles in this scope',
        icon: <i className={'fas fa-users text-primary'} />,
        onClick: () => handleDrilldown(ViewValues.Roles, value, ''),
      };
    }

    const inScope = isUserInScope(value, userRoleCode);

    const onClick = () => {
      const action = inScope ? 'remove' : 'add';
      updateScope(action, value, userRoleCode, scopeTags);
    };

    return {
      tooltip: userName ? `${inScope ? 'Remove' : 'Add'} ${userName} as a ${userRole} for ${scopeTitle}` : undefined,
      button: (
        <IconButton
          outline
          color={inScope ? 'danger' : 'primary'}
          className={'card-grid-button'}
          onClick={onClick}
          icon={inScope ? 'fa-times' : 'fa-plus'}
        />
      ),
    };
  };

  const renderAddButtons = (
    showText = false,
    addScopeTags: undefined | string[] = undefined,
    filterByRole: string | undefined = undefined,
  ) => {
    const buttons = [
      {
        type: SurveyUserRoles.Admin,
        icon: 'fa-user-cog text-ThemeHeadingLight',
        isDisabled: !canAccessVerification,
        tooltip: canAccessVerification ? '' : disabledVerificationTooltip,
      },
      {
        type: SurveyUserRoles.Stakeholder,
        icon: 'fa-user-edit text-ThemeAccentMedium',
      },
      {
        type: SurveyUserRoles.Verifier,
        icon: 'fa-user-check text-ThemeSuccessMedium',
        isDisabled: !canAccessVerification,
        tooltip: canAccessVerification ? '' : disabledVerificationTooltip,
      },
    ];

    return buttons
      .filter((button) => !filterByRole || filterByRole === button.type)
      .map((button) => {
        return (
          <SimpleTooltip text={button.tooltip} key={`add-user-${button.type}-${showText ? '-text' : ''}`}>
            <Button
              outline
              size='sm'
              disabled={isDisabled || button.isDisabled}
              onClick={(e) => toggleDelegationModal(e, button.type, addScopeTags)}
              className='ml-1'
            >
              <i className={`fa ${button.icon} mr-2`} />
              Add a {roles[button.type].shortName}
            </Button>
          </SimpleTooltip>
        );
      });
  };

  const renderDelegationModal = () => {
    if (!delegationModal.isOpen) {
      return <></>;
    }

    const hasScope = Array.isArray(delegationModal.scopeTags) && delegationModal.scopeTags.length > 0;
    const roleName = roles[delegationModal.surveyRole].shortName;

    const headerTitle = hasScope ? `${roleName} for ${scopeTitle}` : `${roleName} for all ${QUESTION.PLURAL}`;
    const description = `Users added here will be ${roleName}s to ${hasScope ? scopeTitle : 'ALL'} ${QUESTION.PLURAL}`;
    const title = `Add a ${roles[delegationModal.surveyRole].shortName}`;

    if (delegationModal.scopeTags?.length) {
      const ignoredIds = users.filter((u) => isUserInScope(u._id, delegationModal.surveyRole)).map((u) => u._id);
      return (
        <SurveyDelegationSearchModal
          isOpen={true}
          surveyId={surveyData._id}
          ignoredIds={ignoredIds}
          handleSubmit={(props) => handleAddUser(props, scopeTags, delegationModal.surveyRole)}
          toggle={(e: React.MouseEvent<HTMLButtonElement>) => toggleDelegationModal(e, delegationModal.surveyRole)}
          title={title}
        />
      );
    }

    return (
      <DelegationModal
        isOpen={true}
        survey={surveyData}
        initiativeId={surveyData.initiativeId}
        surveyRole={delegationModal.surveyRole}
        headerTitle={headerTitle}
        description={description}
        handleSubmit={reloadSurvey}
        toggle={(e: React.MouseEvent<HTMLButtonElement>) => toggleDelegationModal(e, delegationModal.surveyRole)}
      />
    );
  };

  const cardGroupProps: CardProps = {
    view,
    materiality,
    surveyData,
    appSettings,
    surveyStakeholders: surveyData?.stakeholders,
    isUserDelegated,
    cardGroup,
    questionList: questions,
    UNSDGMap,
    blueprint,
    metricGroups,
    users,
    handleDrilldown,
    breadcrumbs,
    addBtn,
    handleToggleDelegation: toggleDelegationModal,
    scopeType,
  };

  const cardGroups = !isLoaded || viewAsQuestionList ? [] : getDelegationCardGroups(cardGroupProps);
  const questionCount = isUserView(breadcrumbs, view) ? users.length : questions.length;
  const emptySurvey = questionCount === 0;

  const renderEmptySurvey = () => {
    if (surveyData && surveyData.initiativeId) {
      return (
        <TrackingList className='mt-3'>
          <Row>
            <Column stretch className='text-center'>
              You have no {QUESTION.PLURAL} in scope. Please update your {SURVEY.SINGULAR} scope{' '}
              <Link
                to={generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, {
                  initiativeId: surveyData.initiativeId,
                  surveyId: surveyData._id,
                  page: 'scope',
                })}
              >
                <u>here</u>
              </Link>
            </Column>
          </Row>
        </TrackingList>
      );
    }
  };

  const cardCategory =
    cardGroup === ViewValues.Users && breadcrumbs.length > 0 ? breadcrumbs[breadcrumbs.length - 1].cardCategory : '';
  const renderFooterAddButtons = () => {
    if (cardGroup !== ViewValues.Users) {
      return <></>;
    }

    return (
      <div className='border-top pt-3 text-right add-user-buttons'>
        {renderAddButtons(true, scopeTags, cardCategory)}
      </div>
    );
  };

  const showStandardQuestionList = shouldShowScopeQuestions(cardGroup);
  const isInScope = (card: CardGridItemProps) => {
    const showOnlyInScope = [
      ViewValues.Frameworks,
      ViewValues.Materiality,
      ViewValues.QuestionList,
      ViewValues.Sdg,
      ViewValues.Standards,
      ViewValues.StandardsAndFrameworks,
      ViewValues.Regulatory,
      ViewValues.Ratings,
      ViewValues.Custom,
    ];
    if (!showOnlyInScope.includes(cardGroup)) {
      return true;
    }

    return card.inScope || card.isPartial;
  };
  const getClassName = (card: Pick<CardGridItemProps, 'className' | 'inScope' | 'isPartial'>) => {
    if (cardGroup === ViewValues.Users) {
      return card.className;
    }

    return `${card.className ?? ''} ${card.inScope ? 'inScope' : card.isPartial ? 'partialScope' : 'notInScope'}`;
  };

  if (!isManager) {
    return <NotAuthorised />;
  }

  return (
    <>
      <SurveySettingsHeader />
      <DashboardSection className='survey-scope' icon='fal fa-cog' title={`${SURVEY.CAPITALIZED_SINGULAR} settings`}>
        <SurveySettingsMenu />
        {view === ViewValues.Users ? (
          <NewSurveyDelegation
            view={view}
            onChangeView={(view) => handleChangeSettings('groupBy', [view])}
            utrvIds={questions.map((q) => q.utrv?._id).filter(Boolean) as string[]}
            surveyId={surveyData._id}
            isReadOnly={isDisabled}
          />
        ) : (
          <>
            <div className='mt-3'>
              <LoadingPlaceholder count={1} height={39} isLoading={!isLoaded}>
                {emptySurvey ? (
                  renderEmptySurvey()
                ) : (
                  <SurveySettingsToolbar
                    view={view}
                    breadcrumbs={breadcrumbs}
                    handleDrillback={setBreadcrumbs}
                    currentSort={currentSort}
                    handleSort={setSort}
                  >
                    <div className='mr-2'>View: </div>
                    <div className='mr-auto'>
                      <ViewDropdown
                        view={view}
                        onChangeView={(view) => handleChangeSettings('groupBy', [view])}
                      />
                    </div>
                  </SurveySettingsToolbar>
                )}
              </LoadingPlaceholder>
            </div>
            <LoadingPlaceholder className='mt-3' count={1} height={115} isLoading={!isLoaded}>
              <div className='mt-3'>
                {viewAsQuestionList || showStandardQuestionList ? (
                  <ScopeQuestionList
                    surveyData={surveyData as SurveyModelMinData}
                    questions={questions}
                    view={view}
                    breadcrumbs={breadcrumbs}
                  />
                ) : (
                  <>
                    {cardGroups.map((group, i) => {
                      const cardList = sortItems(group.cards, currentSort);
                      return (
                        <Fragment key={`survey-scope-cardgroup-${i}`}>
                          <SurveyGroupHeader group={group} />
                          <CardGrid viewLayout={viewLayout} emptyMessage={i === 0 ? 'No users in this group' : ''}>
                            {cardList.filter(isInScope).map(({ key, ...cardProps }: CardGridItemProps) => (
                              <CardGridItem
                                key={key}
                                {...cardProps}
                                unitName={QUESTION.CAPITALIZED_PLURAL}
                                className={getClassName(cardProps)}
                              />
                            ))}
                          </CardGrid>
                        </Fragment>
                      );
                    })}
                  </>
                )}
              </div>
              <div>{renderFooterAddButtons()}</div>
            </LoadingPlaceholder>
          </>
        )}

        {renderDelegationModal()}
      </DashboardSection>
    </>
  );
}
