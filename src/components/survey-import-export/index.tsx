/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useEffect, useState } from 'react';
import { DashboardSection } from '../dashboard';
import { Loader } from '@g17eco/atoms/loader';
import G17Client from '../../services/G17Client';
import { ReduxReducer } from '@reducers/types';
import { FileMappingData } from '../../utils/file/columnMapping';
import { SurveySettingsHeader } from '../survey-configuration/partials/SurveySettingsHeader';
import '../../css/common/import-export-template.scss';
import { SURVEY } from '@constants/terminology';

const XlsxImport = React.lazy(() => import('./xlsx/XlsxImport'));

export default function SurveyImportExport() {

  const [mappingState, setMapping] = useState<ReduxReducer<FileMappingData | undefined>>({
    loaded: false,
    data: undefined,
    errored: false
  });

  useEffect(() => {
    G17Client.getSurveyFileMapping().then((result) => {
      setMapping((c) => ({
        ...c,
        data: result,
        loaded: true,
        errored: false,
        errorMessage: ''
      }))
    }).catch((e) => {
      setMapping((c) => ({ ...c, errored: true, errorMessage: e.message }))
    })

  }, []);

  return (
    <>
      <SurveySettingsHeader />
      <DashboardSection
        className='import-export-container'
        icon='fa-file-import'
        headingStyle={4}
        title={`Import ${SURVEY.SINGULAR} data`}
        subtitle={`The import tool allows you to upload data to your ${SURVEY.SINGULAR} via our excel template.`}
      >
        <div className='border-bottom' />
        <React.Suspense fallback={<div style={{ minHeight: '400px', width: '100%' }}><Loader /></div>}>
          <XlsxImport mappingData={mappingState.data} />
        </React.Suspense>
      </DashboardSection>
    </>
  );
}
