import { CSVFormat } from '@g17eco/types/survey';
import { UniversalTrackerValuePlain } from '@g17eco/types/surveyScope';
import { UniversalTrackerPlain, UtrValueType } from '@g17eco/types/universalTracker';
import { ValueListPlain } from '@g17eco/types/valueList';
import { SurveyActionData, SurveyModelMinimalUtrv } from '@models/surveyData';
import { TransformMapping } from '@utils/file/columnMapping';
import { getValueListData, getValueListMultiData } from '@utils/universalTracker';
import { ParseData } from '../parserTypes';

export type CellValueTypes = string | number | undefined;
export type ReviewValue = string | number | boolean | undefined;
export type ExpandedData = Record<string, unknown>;
export type SheetData = Record<string, CellValueTypes>;
export interface DataImportRow extends CSVFormat {
  skipped?: boolean;
  utr?: UniversalTrackerPlain;
  utrv?: UniversalTrackerValuePlain;
  originalValue?: ReviewValue;
}

export interface ReviewDataProps {
  surveyData: SurveyActionData;
  parsedData: ParseData;
  rows: ExpandedData[];
  setMessage: (msg: string) => unknown;
  setData: (data: ExpandedData[]) => void;
}

export const isValidData = (results: unknown[], isValid: boolean): results is SheetData[] => isValid;

export const isValidValueData = (utr: Pick<UniversalTrackerPlain, 'valueType'>) =>
  [UtrValueType.Date, UtrValueType.Text, UtrValueType.ValueList].includes(utr.valueType as UtrValueType);

interface GetDataPointValueParams {
  utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueValidation'> | undefined;
  utrv: Pick<SurveyModelMinimalUtrv, 'value' | 'valueData'> | undefined;
  valueListCode?: CellValueTypes;
  valueListMap: Map<string, ValueListPlain>;
}

export const getDataPointValue = ({ utr, utrv, valueListCode, valueListMap }: GetDataPointValueParams): ReviewValue => {
  if (!utr || !utrv) {
    return undefined;
  }

  switch (utr.valueType) {
    case UtrValueType.Date:
    case UtrValueType.Text:
    case UtrValueType.Number:
    case UtrValueType.Percentage: {
      const isValueDataType = isValidValueData(utr);
      return isValueDataType ? utrv.valueData?.data : utrv.value;
    }
    case UtrValueType.ValueList: {
      const options = utr.valueValidation?.valueList?.listId
        ? (valueListMap.get(utr.valueValidation.valueList.listId)?.options ?? [])
        : [];
      return getValueListData(utrv.valueData?.data, options);
    }
    case UtrValueType.ValueListMulti: {
      const options = utr.valueValidation?.valueList?.listId
        ? (valueListMap.get(utr.valueValidation.valueList.listId)?.options ?? [])
        : [];
      return getValueListMultiData(utrv.valueData?.data, options).join(', ') as ReviewValue;
    }
    case UtrValueType.TextValueList:
    case UtrValueType.NumericValueList: {
      if (!valueListCode) {
        return undefined;
      }
      return utrv.valueData?.data?.[valueListCode] as ReviewValue;
    }
    case UtrValueType.Table: {
      if (!valueListCode) {
        return undefined;
      }

      // Multi-row table compare only value of first row, so it is similar to single row table
      // Some column code have trailing spaces, so we need to trim them (e.g. "gri/agri/2022-13.10.5 ")
      const columnValue = utrv.valueData?.table?.[0]?.find((col) => col.code.trim() === valueListCode)?.value;
      const columnListId = utr.valueValidation?.table?.columns?.find((c) => c.code === valueListCode)?.listId;
      const options = columnListId ? (valueListMap.get(columnListId)?.options ?? []) : [];

      return (
        typeof columnValue === 'number'
          ? columnValue
          : columnListId
            ? getValueListData(columnValue, options)
            : columnValue
      ) as ReviewValue;
    }
    default:
      return undefined;
  }
};
