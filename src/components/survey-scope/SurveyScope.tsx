/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useContext } from 'react';
import { SurveyContext, SurveyContextLoadedProps } from '../survey-container/SurveyContainer';
import { DashboardSection } from '../dashboard';
import { viewOptions, ViewValues } from '../survey-overview-sidebar/viewOptions';
import { SurveySettingsMenu } from '../survey-configuration/partials/SurveySettingsMenu';
import { SurveySettingsHeader } from '../survey-configuration/partials/SurveySettingsHeader';
import NotAuthorised from '../../routes/not-authorised';
import { useAppDispatch, useAppSelector } from '../../reducers';
import { isUserManager } from '../../selectors/user';
import './style.scss';
import { convertToFilters } from '../survey/utils/useScopeFilters';
import { processScopeData } from './scopeSelection';
import { Action } from '../../constants/action';
import { removeSurveyScope, updateSurveyScope } from '../../actions/api';
import { getAnalytics } from '../../services/analytics/AnalyticsService';
import { AnalyticsEvents } from '../../services/analytics/AnalyticsEvents';
import { loadScorecardsBySurveyId } from '../../actions/scorecard';
import { addSiteAlert } from '../../slice/siteAlertsSlice';
import { unloadSurveyListSummary } from '../../actions/survey';
import SurveyScopeCardGrid from './SurveyScopeCardGrid';
import { FeaturePermissions } from '../../services/permissions/FeaturePermissions';
import { SURVEY_DATA_TAG, surveyScopeUpdateApi } from '../../api/surveys';
import { INSIGHT_DASHBOARDS_TAG, insightDashboardsApi } from '../../api/insight-dashboards';
import { SURVEY } from '@constants/terminology';

export default function SurveyScope() {
  const surveyContext = useContext(SurveyContext) as SurveyContextLoadedProps;
  const { surveyData, breadcrumbs, sidebarSettings, reloadSurvey, materiality } = surveyContext;

  const { groupBy } = sidebarSettings;
  const dispatch = useAppDispatch();
  const isManager = useAppSelector(isUserManager);
  const canAccessScopePacks = useAppSelector(FeaturePermissions.canAccessScopePacks);
  const view = groupBy[0] as ViewValues;

  const scopeType = viewOptions.map((v) => v.value).includes(view) ? view : ViewValues.QuestionPacks;
  const cardGroup = breadcrumbs.length > 0 ? breadcrumbs[breadcrumbs.length - 1].cardGroup : scopeType;
  const [isLoading, setLoading] = React.useState(false);

  // Could display upgrade path in case of no scope access here.
  // but we should always have it, as all appConfig do have it for now.
  if (!isManager || !canAccessScopePacks) {
    return <NotAuthorised />;
  }

  const updateScope = (data: string[], method: Action) => {
    const filters = convertToFilters(scopeType, breadcrumbs);
    const scopeGroups = processScopeData(scopeType, data, filters, materiality);
    const apiCall = method === Action.Add ? updateSurveyScope : removeSurveyScope;
    setLoading(true);
    apiCall(surveyData._id, scopeGroups)
      .then(() => {
        const analytics = getAnalytics();
        return analytics.track(AnalyticsEvents.SurveyScopeUpdated, {
          initiativeId: surveyData.initiativeId,
          surveyId: surveyData._id,
          data,
          scopeType,
        });
      })
      .then(() => reloadSurvey(false))
      .then(() => {
        dispatch(loadScorecardsBySurveyId(surveyData.initiativeId, surveyData._id, false, undefined, true));
        dispatch(unloadSurveyListSummary());
        // Updating scopes may affect data in insight dashboards
        dispatch(surveyScopeUpdateApi.util.invalidateTags([SURVEY_DATA_TAG]));
        dispatch(insightDashboardsApi.util.invalidateTags([INSIGHT_DASHBOARDS_TAG]));
        setLoading(false);
      })
      .catch((e: Error) => {
        dispatch(
          addSiteAlert({
            content: e.message,
            timeout: 5000,
          }),
        );
        setLoading(false);
      });
  };

  return (
    <>
      <SurveySettingsHeader />
      <DashboardSection className='survey-scope' icon='fal fa-cog' title={`${SURVEY.CAPITALIZED_SINGULAR} settings`}>
        <SurveySettingsMenu />
        <SurveyScopeCardGrid
          view={view}
          scopeType={scopeType}
          cardGroup={cardGroup}
          {...surveyContext}
          isLoading={isLoading}
          updateScope={updateScope}
          sidebarSettings={sidebarSettings}
        />
      </DashboardSection>
    </>
  );
}
