/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import UniversalTracker from '../../../../model/UniversalTracker';
import { DisplayCheckBox, TableDataInfo, ValueData } from '../../question/questionInterfaces';
import { UnitConfig } from '../../../../model/surveyData';
import { ColumnInputProps } from './table/InputInterface';
import { UniversalTrackerValuePlain } from '@g17eco/types/surveyScope';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import { UserMin } from '@constants/users';
import { ProviderFactory } from '@g17eco/types/collaboration';

export type HandleValueChangeProps = {
  value: any;
  min?: number;
  max?: number;
  isReadOnly?: boolean;
};

export type HandleValueChange = ({
  value,
  min,
  max,
  isReadOnly,
}: HandleValueChangeProps) => void;

export type ValueDataDataWithTable = ValueData['data'] | ValueData['table']
export type ErrorMessageType = { [key: string]: string | undefined };
export type Addon = { code: string; rowIndex?: number; element: React.ReactNode };
export type Addons = {
  before?: Addon[];
  after?: Addon[];
}

export interface BaseInputProps {
  prefix: any;
  suffix: any;
  index: number;
  saving: boolean;
  universalTracker: UniversalTracker;
  initiativeUtr?: InitiativeUniversalTracker;
  status: UniversalTrackerValuePlain['status'];
  placeholder?: string;
  displayCheckbox: DisplayCheckBox;
  scrollToRef: () => void;
  inputMessage?: ErrorMessageType;
  isDisabled?: (props?: Partial<BaseInputProps> | Partial<ColumnInputProps>) => boolean;
  isInvalid?: boolean;
  unitConfig?: UnitConfig;

  // Draft
  isDraft?: boolean;
  providerFactory?: ProviderFactory;
  currentUser?: UserMin;
  documentId?: string;
  /** Represent users for comments **/
  users?: UserMin[];

  // Input
  questionValue: number | undefined;
  valueDataData: ValueDataDataWithTable;
  unit: string | undefined;
  numberScale: string | undefined;

  // Multi-row table
  table: TableDataInfo;
  updateTable: (table: Partial<TableDataInfo>, error?: ErrorMessageType) => any;
  isDownloadBtnBottom?: boolean;

  // Handlers
  handleFocus?: () => any;
  handleValueDataChange: (valueDataData: any) => any;
  handleCheckboxChange: (inputName: string, value: any) => any;
  handleError: (message: string) => any;
  handleValueChange: HandleValueChange;
  handleUnitChange: (unit: string) => any;
  handleNumberScaleChange: (numberScale: string) => any;

  handleNA?: () => void;
  handleNR?: () => void;
  handleReject?: () => void;
  handleComments?: (comments: string) => void;

  ref?: any;
  hasValueChanged?: boolean;

  // A single input (Date, Text, Number, ValueList) will take the first element for addon
  // Multiple inputs (Numeric/Text value list) an array of addons is used
  // A single row table uses an array located by rowIndex = 0
  // A multiple rows table uses an array located by rowIndex and code (columnCode)
  addons?: Addons;
  isReadOnlyOptions?: boolean;
}

export interface InputProps extends BaseInputProps {
  valueDataData: ValueData['data'];
}
