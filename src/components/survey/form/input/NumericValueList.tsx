/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useMemo } from 'react';
import { FormGroup, Input, Label } from 'reactstrap';
import ValueSliderGroup, { SliderInterface } from './ValueSliderGroup';
import { validateMinMax } from '../../../../utils/universalTrackerValue';
import { renderInputGroup } from './InputGroup';
import { InputGroupPrePend } from './InputGroupPrePend';
import { BaseInputProps } from './InputProps';
import { ValueDataData, ValueDataObject } from '../../question/questionInterfaces';
import { InputHelperText } from './InputHelperText';
import { getUtrNumberValue, getUtrDecimal } from '@utils/universalTracker';
import { findAddon } from './addonUtils';
import classnames from 'classnames';
import { isDefined } from '@utils/index';
import { isNumericString } from '@utils/string';
import { getInputWarningMessage } from '@components/survey/utils/input';
import { MappedConnectionWrapper, usePopulateInputFromConnection } from '@features/assistant';
import { useVariationContext } from '@components/survey/question/variation/VariationContext';

interface handleChangeProps extends Pick<BaseInputProps, 'valueDataData' | 'handleValueDataChange' | 'handleError'> {
  name: string;
  value: string | number;
  min?: number;
  max?: number;
}

const handleChange = ({
  name,
  value,
  min,
  max,
  valueDataData,
  handleValueDataChange,
  handleError,
}: handleChangeProps) => {
  const { errored, message } = validateMinMax(value, min, max);
  if (errored) {
    return handleError(message);
  }

  if (typeof valueDataData !== 'object' || Array.isArray(valueDataData)) {
    valueDataData = {};
  }
  valueDataData[name] = value === '' ? undefined : value;
  handleValueDataChange(valueDataData);
};
interface NumericValueListItemProps extends BaseInputProps {
  label?: string;
  inputName: string;
  value?: number | string;
  isTotal: boolean;
  checkboxId: string;
  warningMessage?: React.ReactNode;
}
const NumericValueListItem = (props: NumericValueListItemProps) => {
  const {
    inputName,
    value,
    isTotal = false,
    displayCheckbox,
    universalTracker,
    valueDataData,
    placeholder,
    hasValueChanged,
    checkboxId,
    inputMessage,
    warningMessage,
    addons,
    isDisabled,
    handleFocus,
    handleError,
    handleUnitChange,
    handleCheckboxChange,
    handleValueDataChange,
  } = props;

  const min = universalTracker.getMin();
  const max = universalTracker.getMax();
  const checked = isTotal || (inputName !== '' && Boolean(displayCheckbox[inputName]));
  const label = props.label + (checked ? '' : ': N/A');
  const disabled = Boolean(isDisabled?.(props));
  const invalidMessage = inputMessage?.[inputName];
  const isInvalid = !!invalidMessage;

  const decimal = getUtrDecimal(universalTracker);
  const valueWithDecimal = getUtrNumberValue({ value, decimal, hasValueChanged });
  const { beforeAddon, afterAddon } = findAddon(addons, inputName);

  usePopulateInputFromConnection({
    inputChangeHandler: ({ value }) => {
      handleCheckboxChange(inputName, true);
      handleChange({
        name: inputName,
        value,
        min,
        max,
        valueDataData,
        handleValueDataChange,
        handleError,
      });
    },
    valueListCode: inputName,
  });

  return (
    <MappedConnectionWrapper valueListCode={inputName}>
      <div className='inputGroupContainer my-2'>
        <FormGroup check>
          {!isTotal ? (
            <Input
              type='checkbox'
              id={checkboxId}
              checked={checked}
              disabled={disabled}
              onChange={(e) => handleCheckboxChange(inputName, e.target.checked)}
            />
          ) : null}
          <Label check htmlFor={checkboxId} className='strong'>
            {label}
          </Label>
        </FormGroup>
      </div>
      <div data-testid='numeric-value-list-wrapper' className={!checked ? 'd-none mb-3' : ' mb-3'}>
        <div
          className={classnames('position-relative input-group mb-3', {
            'has-validation': isDefined(decimal) || (isTotal && !!warningMessage),
          })}
        >
          {beforeAddon?.element || null}
          {InputGroupPrePend({ ...props, isInvalid })}
          {isTotal ? (
            <Input
              type='text'
              data-testid='numeric-value-list-total'
              className='styled-readonly'
              name={inputName}
              value={valueWithDecimal}
              aria-describedby='suffix'
              disabled={isTotal}
            />
          ) : (
            <Input
              type='number'
              data-testid='numeric-value-list-input'
              placeholder={placeholder}
              autoComplete='off'
              className={isInvalid ? '' : 'styled-input'}
              name={inputName}
              value={valueWithDecimal}
              aria-describedby='suffix'
              disabled={disabled}
              required={true}
              onFocus={handleFocus}
              onClick={handleFocus}
              invalid={isInvalid}
              onWheel={(e) => e.currentTarget.blur()}
              onChange={(e) => {
                e.preventDefault();
                handleChange({
                  name: inputName,
                  value: e.target.value,
                  min,
                  max,
                  valueDataData,
                  handleValueDataChange,
                  handleError,
                });
              }}
            />
          )}
          {renderInputGroup({
            ...props,
            isInvalid,
            handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) =>
              handleUnitChange(e.target.value),
            isDisabled: (props) => !isTotal || disabled,
          })}
          {disabled ? null : (
            <InputHelperText
              invalidMessage={invalidMessage}
              universalTracker={universalTracker}
              warningMessage={isTotal ? warningMessage : undefined}
            />
          )}
          {afterAddon?.element || null}
        </div>
      </div>
    </MappedConnectionWrapper>
  );
};
interface sliderChangeProps extends Pick<BaseInputProps, 'handleValueDataChange' | 'handleCheckboxChange'> {
  data: SliderInterface[];
}
const handleSliderChange = ({ data, handleValueDataChange, handleCheckboxChange }: sliderChangeProps) => {
  const valueData: ValueDataObject = {};
  data.forEach((d) => {
    valueData[d.code] = d.value;
    handleCheckboxChange(d.code, true); // No checkboxes, so auto-enable
  });
  handleValueDataChange(valueData);
};

export default function NumericValueList(props: BaseInputProps) {
  const {
    universalTracker,
    valueDataData,
    questionValue,
    handleCheckboxChange,
    handleValueDataChange,
    addons,
    unit,
    numberScale,
    initiativeUtr,
    index,
  } = props;

  // 1 time generated id to avoid re-render and conflict actions if 2 identical inputs are rendered at the same page.
  const inputId = useMemo(() => Math.random().toString(), []);

  const valueList = universalTracker.getValueListOptions();
  const defaultValue = typeof valueDataData !== 'object' ? ({} as ValueDataData) : valueDataData;
  const unitWarningMessage = getInputWarningMessage({ utr: universalTracker, unit, numberScale, initiativeUtr });
  const { getVariationWarningMessage } = useVariationContext();

  if (universalTracker.hasMinAndMax()) {
    const sliders: SliderInterface[] = [];
    valueList.forEach((item) => {
      const value = defaultValue[item.code as keyof typeof valueDataData];
      const standardizedValue = isNumericString(value) ? parseFloat(value) : undefined;
      const slider = {
        code: item.code,
        name: item.name,
        value: standardizedValue,
      };
      sliders.push(slider);
    });

    if (sliders === undefined || sliders.length === 0) {
      return <></>;
    }
    return (
      <ValueSliderGroup
        sliders={sliders}
        universalTracker={universalTracker}
        handleChange={(data) => handleSliderChange({ data, handleValueDataChange, handleCheckboxChange })}
        addons={addons}
        numberScale={props.numberScale}
        handleNumberScaleChange={props.handleNumberScaleChange}
        status={props.status}
        initiativeUtr={props.initiativeUtr}
        warningMessage={unitWarningMessage}
      />
    );
  }

  return (
    <div className='questionWithFields'>
      {valueList.map((item) => {
        const value = defaultValue[item.code as keyof typeof valueDataData];
        const checkboxId = `valueCheckbox-${item.code}-${index}-${universalTracker.getId()}-${inputId}}`;
        const warningMessage = unitWarningMessage || getVariationWarningMessage(item.code);

        return (
          <NumericValueListItem
            key={checkboxId}
            {...props}
            warningMessage={warningMessage}
            label={item.name}
            inputName={item.code}
            value={value}
            isTotal={false}
            checkboxId={checkboxId}
          />
        );
      })}
      <div className='display-on-focus'>
        <NumericValueListItem
          {...props}
          warningMessage={unitWarningMessage}
          label={'Total'}
          value={questionValue}
          isTotal={true}
          inputName=''
          checkboxId={inputId}
        />
      </div>
    </div>
  );
}
