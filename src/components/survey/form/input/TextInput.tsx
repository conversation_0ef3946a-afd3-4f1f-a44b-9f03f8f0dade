/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import { Input } from 'reactstrap';
import { BaseInputProps } from './InputProps';
import { TextAreaWithAIDraft } from '../../../../apps/common/components/TextAreaWithAIDraft/TextAreaWithAiDraft';
import G17Client from '../../../../services/G17Client';
import { getAddonSingleElement } from './addonUtils';
import { usePopulateInputFromConnection } from '@features/assistant';

interface textInputProps
  extends Pick<
    BaseInputProps,
    | 'isDisabled'
    | 'universalTracker'
    | 'valueDataData'
    | 'handleFocus'
    | 'handleValueDataChange'
    | 'placeholder'
    | 'addons'
  > {
  valueDataData: any;
}

export default function TextInput(props: textInputProps) {
  const {
    isDisabled,
    universalTracker,
    valueDataData,
    handleFocus,
    handleValueDataChange,
    placeholder,
    addons
  } = props;

  const defaultValue = valueDataData === undefined ? '' : valueDataData;

  const onChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    e.preventDefault();
    return handleValueDataChange(e.target.value);
  };

  usePopulateInputFromConnection({
    inputChangeHandler: ({ value }) => {
      handleValueDataChange(value);
    },
  });

  const disabled = isDisabled?.(props);
  const maxLength = universalTracker.getMax();
  const utrId = universalTracker.getId();
  const { beforeElement, afterElement } = getAddonSingleElement(addons);

  if (maxLength && maxLength > 1 && maxLength < 100) {
    return (
      <div className='position-relative input-group mb-3'>
        {beforeElement || null}
        <Input
          autoComplete='off'
          type='text'
          className='styled-input'
          name={utrId}
          value={defaultValue}
          placeholder={placeholder}
          disabled={disabled}
          required={true}
          onFocus={handleFocus}
          onClick={handleFocus}
          onChange={onChange}
        />
        {afterElement || null}
      </div>
    );
  } else {
    return (
      <div className='position-relative input-group mb-3'>
        {beforeElement || null}
        <TextAreaWithAIDraft
          key={`inputfactor-textarea-ai-${utrId}`}
          rows={5}
          className='styled-input'
          name={utrId}
          value={defaultValue}
          placeholder={placeholder}
          disabled={disabled}
          required={true}
          onFocus={handleFocus}
          onClick={handleFocus}
          onChange={handleValueDataChange}
          getDraft={() => G17Client.getQuestionInputFieldDraft({ utrId })}
        />
        {afterElement || null}
      </div>
    );
  }
}
