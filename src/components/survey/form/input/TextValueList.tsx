/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import { ValueData, ValueDataData } from '../../question/questionInterfaces';
import { BaseInputProps } from './InputProps';
import { TableValueDataData } from './table/InputInterface';
import { findAddon } from './addonUtils';
import { Input } from 'reactstrap';
import { MappedConnectionWrapper, usePopulateInputFromConnection } from '@features/assistant';

type TextValueListBaseProps = Pick<
  BaseInputProps,
  | 'isDisabled'
  | 'index'
  | 'universalTracker'
  | 'valueDataData'
  | 'handleFocus'
  | 'handleCheckboxChange'
  | 'handleValueDataChange'
  | 'displayCheckbox'
  | 'placeholder'
  | 'addons'
>;
interface ChangeProps extends Pick<TextValueListBaseProps, 'valueDataData' | 'handleValueDataChange'> {
  name: string;
  value: string;
  valueDataData: ValueDataData | TableValueDataData;
}
const handleChange = ({ name, value, valueDataData, handleValueDataChange }: ChangeProps) => {
  handleValueDataChange({
    ...(typeof valueDataData === 'object' ? valueDataData : {}),
    [name]: value === '' ? undefined : value,
  });
};

interface TextValueListItemProps extends TextValueListBaseProps {
  label?: string;
  inputName: string;
  checkboxId: string;
}

const TextValueListItem = (props: TextValueListItemProps) => {
  const {
    label,
    inputName,
    valueDataData,
    displayCheckbox,
    universalTracker,
    checkboxId,
    addons,
    placeholder,
    handleFocus,
    handleCheckboxChange,
    handleValueDataChange,
    isDisabled,
  } = props;
  const valueDataObj = valueDataData ?? {};
  const currentValue = valueDataData
    ? (valueDataData[inputName as keyof typeof valueDataData] as string | number | undefined)
    : '';
  const checked = !!displayCheckbox[inputName];
  const disabled = isDisabled?.(props);
  const maxLength = Number(universalTracker.getValueValidation().max);
  const { beforeAddon, afterAddon } = findAddon(addons, inputName);

  usePopulateInputFromConnection({
    inputChangeHandler: ({ value }) => {
      handleCheckboxChange(inputName, true);
      handleChange({
        name: inputName,
        value: String(value),
        valueDataData: valueDataObj,
        handleValueDataChange,
      });
    },
    valueListCode: inputName,
  });

  return (
    <MappedConnectionWrapper valueListCode={inputName}>
      <div className='inputGroupContainer form-check my-2'>
        <input
          type='checkbox'
          id={checkboxId}
          className='form-check-input'
          checked={checked}
          disabled={disabled}
          onChange={(e) => handleCheckboxChange(inputName, e.target.checked)}
        />
        <label htmlFor={checkboxId} className='strong form-check-label'>
          {label}
        </label>
      </div>
      <div data-testid='text-value-list-wrapper' className={!checked ? 'd-none mb-3' : ' mb-3'}>
        <div className='position-relative input-group mb-3'>
          {beforeAddon?.element || null}
          {maxLength && maxLength > 1 && maxLength < 100 ? (
            <Input
              type='text'
              autoComplete='off'
              className='styled-input'
              name={inputName}
              value={currentValue}
              placeholder={placeholder}
              aria-describedby='suffix'
              disabled={disabled}
              required={true}
              onFocus={handleFocus}
              onClick={handleFocus}
              onChange={(e) => {
                e.preventDefault();
                handleChange({
                  name: inputName,
                  value: e.target.value,
                  valueDataData: valueDataObj,
                  handleValueDataChange,
                });
              }}
              data-testid='text-value-list-input'
            />
          ) : (
            <Input
              type='textarea'
              autoComplete='off'
              className='styled-input'
              rows={5}
              name={inputName}
              value={currentValue}
              placeholder={placeholder}
              aria-describedby='suffix'
              disabled={disabled}
              required={true}
              onFocus={handleFocus}
              onClick={handleFocus}
              onChange={(e) => {
                e.preventDefault();
                handleChange({
                  name: inputName,
                  value: e.target.value,
                  valueDataData: valueDataObj,
                  handleValueDataChange,
                });
              }}
              data-testid='text-value-list-textarea'
            />
          )}
          {afterAddon?.element || null}
        </div>
      </div>
    </MappedConnectionWrapper>
  );
};

interface TextValueListProps extends TextValueListBaseProps {
  valueDataData: ValueData['data'];
}
export default function TextValueList(props: TextValueListProps) {
  const { universalTracker, index } = props;
  const valueList = universalTracker.getValueListOptions();

  return (
    <div className='questionWithFields'>
      {valueList.map((item) => {
        const checkboxId = `valueCheckbox-${item.code}-${index}-${universalTracker.getId()}`;
        return (
          <TextValueListItem
            key={checkboxId}
            {...props}
            label={item.name}
            inputName={item.code}
            checkboxId={checkboxId}
          />
        );
      })}
    </div>
  );
}
