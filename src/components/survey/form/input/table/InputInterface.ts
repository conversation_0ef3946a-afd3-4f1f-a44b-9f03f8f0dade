/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { BaseInputProps } from '../InputProps';
import { TableColumnType, Variation } from '@g17eco/types/universalTracker';

export interface Option {
  code: string;
  name: string;
}


export interface TableColumn {
  type: TableColumnType;
  code: string;
  name: string;
  shortName?: string;
  options?: Option[];
  unit?: string;
  listId?: string;
  unitType?: string;
  numberScale?: string;
  unitInput?: string;
  numberScaleInput?: string;
  unitLocked?: boolean;
  numberScaleLocked?: boolean;
  instructions?: string;
  calculation?: {
    formula: string;
  };
  visibilityRule?: {
    formula: string;
  }
  validation?: {
    max?: number;
    min?: number;
    decimal?: number;
    variations?: Variation[];
    required?: boolean;
    allowCustomOptions?: boolean;
  };
}

export interface InputColumnValue {
  value?: number | string | string[]
}
export interface InputColumn extends InputColumnValue {
  code: string;
  unit?: string;
  numberScale?: string;
}

export type TableValueDataData = InputColumn[][];
export interface ColumnInputProps extends BaseInputProps {
  column: TableColumn;
  inputColumn: InputColumn;
  placeholder?: string,
  label?: string | JSX.Element;
  isAutoCalculated: boolean;
  valueDataData: TableValueDataData;
  multi?: boolean;
  updateColumn: (update: InputColumn) => void;
}

export enum TableAggregationType {
  Group = 'group',
}

/**
 * Allow to group using list of columns
 */
export interface TableGroupColumn {
  /** Column code to group by **/
  code: string,
}

interface TableAggregation {
  type: TableAggregationType.Group,
  columns: TableGroupColumn[]
}

export interface ValueTable {
  validation?: {
    maxRows: number,
  };
  columns: TableColumn[];
  aggregation?: TableAggregation;
}

export interface TableInputProps extends BaseInputProps {
  tableConfiguration: ValueTable;
  calculationColumns: TableColumn[];
  visibilityRuleColumns: TableColumn[];
  placeholder?: string,
  valueDataData: TableValueDataData;
  handleValueDataChange: (update: InputColumn[][]) => void;
  handleCheckboxChange: (checkboxId: string, checked: boolean) => void;
  scrollToRef: () => void,
  ref?: any,

  handleNA?: () => void;
  handleNR?: () => void;
  handleReject?: () => void;
  handleComments?: (comments: string) => void;
}
