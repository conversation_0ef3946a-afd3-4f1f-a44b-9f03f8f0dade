/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { getTableConfiguration } from '../../../../../utils/universalTracker'
import { ValueTable } from './InputInterface';
import { TableDataInfo } from '../../../question/questionInterfaces';
import { ColumnMap, convertInputData, createConversionParams } from '@utils/valueDataTable';

interface TableSubmitValues {
  submitValues: any;
  table?: TableDataInfo;
  universalTracker: any;
}

export const updateTableDataSubmitValues = (submitData: TableSubmitValues) => {

  const { submitValues, table, universalTracker } = submitData;

  const tableConfig: ValueTable | undefined = getTableConfiguration(universalTracker);

  if (!tableConfig || !table || !Array.isArray(table.rows)) {
    return submitValues;
  }

  const { columns } = tableConfig;
  const mapCode: ColumnMap = new Map(columns.map(c => [c.code, c]));

  const rowData = table.rows.filter(r => !r.isRemoved).map(r => r.data);

  submitValues.valueData.input.table = rowData.map((rowColumns) => {
    return rowColumns.map((col) => {
      const colMetadata = mapCode.get(col.code);
      if (!colMetadata) {
        return col;
      }
      const { isCurrency, defaultUnit, defaultNumberScale } = createConversionParams(colMetadata, col);
      const unit = isCurrency ? undefined : col.unit ?? defaultUnit;
      return {
        ...col,
        numberScale: col.numberScale ?? defaultNumberScale,
        unit,
      };
    });
  });

  submitValues.valueData.table = convertInputData(rowData, mapCode);
  return submitValues;
}
