/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import { Input } from 'reactstrap';
import { ColumnInputProps } from '../InputInterface';
import { getSingleRowTableAddon } from '../tableUtils';
import { getFullName } from '@utils/user';
import { CollaborationEditor } from '@features/rich-text-editor';
import { MappedConnectionWrapper, usePopulateInputFromConnection } from '@features/assistant';

export default function TextInput(props: ColumnInputProps) {
  const {
    isDisabled,
    column,
    inputColumn,
    handleFocus,
    updateColumn,
    placeholder,
    label,
    currentUser,
    documentId,
  } = props;

  const currentValue = inputColumn.value || '';
  const { beforeAddon, afterAddon } = getSingleRowTableAddon({ ...props, columnCode: column.code });

  const onChange = (e: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    e.preventDefault();
    const { name, value } = e.target;
    return updateColumn({
      code: name,
      value: value === '' ? undefined : value
    });
  };

  usePopulateInputFromConnection({
    inputChangeHandler: ({ value }) => {
      updateColumn({ value, code: column.code });
    },
    valueListCode: column.code,
  });

  const disabled = isDisabled?.(props);
  const maxLength = Number(column.validation?.max);

  if (props.isDraft && props.providerFactory && documentId) {
    return (
      <div className='mb-3'>
        {label}
        <div className='position-relative w-100'>
          {beforeAddon?.element || null}
          <CollaborationEditor
            className='text-dark'
            disabled={disabled}
            username={currentUser ? getFullName(currentUser) : undefined}
            providerFactory={props.providerFactory}
            documentId={`${documentId}.column.${column.code}`}
            users={props.users}
          />
          {afterAddon?.element || null}
        </div>
      </div>
    );
  }


  if (maxLength > 1 && maxLength < 100) {
    return (
      <div className='mb-3'>
        {label}
        <div className='position-relative w-100'>
          {beforeAddon?.element || null}
          <Input
            className='styled-input'
            autoComplete='off'
            type='text'
            id={column.code}
            name={column.code}
            value={currentValue}
            placeholder={placeholder}
            disabled={!!disabled}
            required={true}
            onFocus={handleFocus}
            onClick={handleFocus}
            onChange={onChange}
          />
          {afterAddon?.element || null}
        </div>
      </div>
    );
  } else {
    return (
      <MappedConnectionWrapper valueListCode={column.code}>
        <div className='mb-3'>
          {label}
          <div className='position-relative w-100'>
            {beforeAddon?.element || null}
            <Input
              type='textarea'
              className='styled-input'
              rows={5}
              id={column.code}
              name={column.code}
              value={currentValue}
              placeholder={placeholder}
              disabled={!!disabled}
              required={true}
              onFocus={handleFocus}
              onClick={handleFocus}
              onChange={onChange}
            />
            {afterAddon?.element || null}
          </div>
        </div>
      </MappedConnectionWrapper>
    );
  }
}
