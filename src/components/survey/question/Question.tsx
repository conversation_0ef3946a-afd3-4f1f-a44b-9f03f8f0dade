/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import { connect, ConnectedProps } from 'react-redux';
import { Button } from 'reactstrap';
import { UpdateActions } from '../../../actions/universalTrackerValue';
import {
  getNaType,
  getValueDataProp,
  isAssuredLocked,
  isNa,
  isNr,
  isUtrvAssuranceComplete,
  isUtrvPartialAssurance,
  validateMinMax
} from '../../../utils/universalTrackerValue';
import InputFactory from '../form/input/InputFactory';
import { getTableConfiguration, UtrValueTypes } from '../../../utils/universalTracker';
import { NotApplicableTypes } from '../../../constants/status';
import { QuestionProps, QuestionState } from './questionInterfaces';
import { BaseInputProps, HandleValueChange } from '../form/input/InputProps';
import UniversalTracker, { UniversalTrackerType } from '../../../model/UniversalTracker';
import { loadUniversalTrackerModal } from '../../../actions/universalTrackerModal';
import { getDefaultPlaceholder } from '../../question/QuestionInput';
import { getAssuranceLockedMessage } from '../../../utils/assurance';
import { SurveyModelMinimalUtrv } from '../../../model/surveyData';
import './Question.scss';
import { QuestionInformation } from './QuestionInformation';
import { ShowAs } from '../../../reducers/universal-tracker-modal';
import { QuestionLockButton } from './QuestionLockButton';
import { generateDecimalErrorMessage } from '../utils/input';
import { UtrvAssuranceStatus } from '@g17eco/types/universalTrackerValue';
import { UtrValueType } from '@g17eco/types/universalTracker';
import classNames from 'classnames';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

interface ValueInputProps {
  utr: UniversalTracker;
  utrv: SurveyModelMinimalUtrv;
  isReadOnly: boolean;
  isOrganizationManager: boolean;
  isReadOnlyOptions: boolean;
}

class Question extends React.Component<PropsFromRedux & QuestionProps> {

  state: QuestionState = {
    errored: false,
    message: undefined,
  }

  private messageClearTimeout?: number;

  updateData = (data: any, cb?: any) => {
    this.props.update(data)
    this.setState(data, cb);
  }

  handleError = (message: string) => this.updateData({ errored: true, message })

  handleCheckboxChange = (checkboxId: string, checked: boolean) => {
    const { utr, displayCheckbox } = this.props;

    if (!utr) {
      return;
    }

    displayCheckbox[checkboxId] = checked;
    const isNumericValueList = utr.isType(UtrValueTypes.numericValueList);
    this.updateData({ displayCheckbox: displayCheckbox }, () => {
      if (isNumericValueList) {
        this.handleValueDataTotalUpdate(utr);
      }
    });
  }

  getQuestionClass(status: string, hasChanged: boolean, isReadOnly: boolean, viewStatus?: string) {
    if (viewStatus) {
      if (isReadOnly) {
        return `questionDisabledView ${viewStatus}`;
      }
      return viewStatus;
    }

    if (isReadOnly) {
      return 'questionDisabledView';
    }

    if (hasChanged) {
      return 'questionChangedView';
    }

    switch (status) {
      case UpdateActions.ACTION.UTR.REJECTED:
        return 'questionRejectedView';
      case UpdateActions.ACTION.UTR.VERIFIED:
        return 'questionVerifiedView';
      case UpdateActions.ACTION.UTR.UPDATED:
        return 'questionUpdatedView';
      default:
        return 'questionCreatedView';
    }
  }

  handleValueChange: HandleValueChange = ({ value, min, max, isReadOnly }) => {
    const { utr } = this.props;
    if (value !== undefined) {
      const { errored, message } = validateMinMax(value, min, max);
      const isValidatingInput = utr && !isReadOnly;
      const inputMessage = isValidatingInput ? generateDecimalErrorMessage({ utr, input: { [utr.getId()]: value } }) : undefined;
      if (errored || isValidatingInput) {
        return this.updateData({ value, errored, message, inputMessage });
      }
    }
    if (value === '') {
      value = undefined; // Prevent hasChanged detection from mixing up initial 'undefined' from ''
    }
    this.updateData({
      isNA: false,
      value: value,
      errored: false,
      message: undefined,
    });
  };

  handleUnitChange = (unit: string) => this.updateData({ unit });

  handleNumberScaleChange = (numberScale: string) => this.updateData({ numberScale });

  handleValueDataChange = (utr: UniversalTracker, valueDataData: any) => {
    const isNumericValueList = utr.isType(UtrValueTypes.numericValueList);
    const propName = this.getValueDataPropName(utr);

    const inputMessage = utr ? generateDecimalErrorMessage({ utr, input: valueDataData }) : undefined;

    this.updateData({ isNA: false, errored: false, message: undefined, valueData: { [propName]: valueDataData }, inputMessage }, () => {
      if (isNumericValueList) {
        this.handleValueDataTotalUpdate(utr);
      }
    });
  }

  handleValueDataTotalUpdate = (universalTracker: UniversalTracker) => {
    const { displayCheckbox, valueData } = this.props;
    const valueDataData = valueData.data;
    const valueList = universalTracker.getValueListOptions();

    if (typeof valueDataData === 'object' && !Array.isArray(valueDataData)) {
      const sumFields = valueList.reduce((acc, item) => {
        const valueDataDatum = valueDataData[item.code] || 0;
        const checkboxSelected = displayCheckbox[item.code];
        if (checkboxSelected && valueDataDatum) {
          if (acc === undefined) {
            acc = 0;
          }
          return acc + parseFloat(valueDataDatum as string);
        } else {
          return acc;
        }
      }, undefined as undefined | number);
      return this.handleValueChange({ value: sumFields, isReadOnly: true });
    }

    this.handleValueChange({ value: undefined, isReadOnly: true });
  }

  handleFocus = () => { }

  componentDidUpdate(prevProps: QuestionProps) {
    const newProps = this.props
    if (newProps.saving !== prevProps.saving && prevProps.saving) {
      this.cleanUpMessage();
    }
  }

  public componentWillUnmount(): void {
    if (this.messageClearTimeout !== undefined) {
      clearTimeout(this.messageClearTimeout);
    }
  }

  render() {
    const {
      utr,
      utrv,
      saving,
      survey,
      alternativeCode,
      isAggregate,
      isContributor,
      isOrganizationManager,
      isQuestionReadOnly = false,
      isVerifier,
    } = this.props;
    const isSurveyComplete = Boolean(survey?.completedDate);

    const openUtrModal = () => {
      if (!utrv || !survey) {
        return;
      }
      return this.props.loadUniversalTrackerModal(
        {
          universalTrackerId: utrv.universalTrackerId,
          universalTrackerType: UniversalTrackerType.Utr,
          actionValueId: utrv._id,
          params: { surveyId: survey._id },
          openAction: { activeTabId: ShowAs.Table },
          alternativeCode,
        });
    }

    const isLocked = (utrv && isAssuredLocked(utrv)) || false;
    const isReadOnly = isSurveyComplete || saving || !isContributor || isAggregate || isLocked || isQuestionReadOnly;
    const isReadOnlyOptions = isVerifier && !isContributor;

    return (
      <div className='surveyQuestion'>
        <QuestionInformation classes={{ container: 'mt-3' }} utr={utr} alternativeCode={alternativeCode} />
        {this.props.message ? <div className='mt-3'>{this.renderMessage()}</div> : null}
        <div className='mt-4 position-relative'>
          <LoadingPlaceholder height={36} className='mb-3' isLoading={saving || !utr || !utrv}>
            {!(saving || !utr || !utrv) ? this.renderValueInput({
              utr,
              utrv,
              isReadOnly,
              isReadOnlyOptions,
              isOrganizationManager
            }) : <></>}
          </LoadingPlaceholder>
        </div>

        {this.getButtonRow(openUtrModal)}
      </div>
    );
  }

  private shouldShowLockButton(assuranceStatus: UtrvAssuranceStatus | undefined) {
    return isUtrvAssuranceComplete(assuranceStatus) || isUtrvPartialAssurance(assuranceStatus);
  }

  private getButtonRow(openUtrModal: () => void) {
    const { utrv, isAggregate, isOrganizationManager, handleAssuranceAction, survey, utr } = this.props;

    if (isAggregate || !utrv || !utr) {
      return null;
    }

    const isLocked = isAssuredLocked(utrv);
    const lockButton =
      isOrganizationManager && handleAssuranceAction && this.shouldShowLockButton(utrv.assuranceStatus) ? (
        <QuestionLockButton
          isLocked={isLocked}
          handleLockAction={handleAssuranceAction}
          isDisabled={Boolean(survey?.completedDate)}
          utrv={utrv}
        />
      ) : null;

    const isMultipleRowsTable =
      utr.getValueType() === UtrValueType.Table && Number(getTableConfiguration(utr)?.validation?.maxRows) !== 1;

    return (
      <div className='d-flex flex-row-reverse justify-content-between'>
        <Button
          size='xs'
          color='link-secondary'
          disabled={!utrv}
          onClick={() => openUtrModal()}
          className={classNames({ 'mr-4': isMultipleRowsTable })}
        >
          <i className='fal fa-clock-rotate-left mr-2' />
          data history
        </Button>
        {lockButton}
      </div>
    );
  }

  renderValueInput({ utr, utrv, isReadOnly, isReadOnlyOptions, isOrganizationManager }: ValueInputProps) {
    const { hasChanged } = this.props;
    const viewStatus = isReadOnlyOptions ? 'verifierDisabledView' : undefined
    const className = this.getQuestionClass(utrv.status, hasChanged, isReadOnly, viewStatus);

    const isNA = isNa(utrv);
    const isNR = isNr(utrv);

    const combinedClass = `${className} ${isNA || isNR ? 'questionNANRView' : ''}`;
    const isLocked = isAssuredLocked(utrv);
    const inputComponent = this.renderValueInputContainer(utr, utrv, isReadOnly, isReadOnlyOptions);

    return (
      <div className={combinedClass} key={utrv._id}>
        {isLocked
          ?
          <SimpleTooltip text={getAssuranceLockedMessage({ isOrganizationManager })}>
            {inputComponent}
          </SimpleTooltip>
          :
          <>
            {inputComponent}
            {(isNA || isNR) && <p className='questionNANRView-helper text-ThemeTextMedium'>{`To change from ${isNA ? '\'Not applicable\'' : '\'Not reportable\''} - enter an answer`}</p>}
          </>
        }
      </div>
    );
  }

  getSuffixPrefix(questionUtr: UniversalTracker) {
    return {
      suffix: questionUtr.getValueType() === 'percentage' ? '%' : '',
      prefix: ''
    };
  }

  getValueDataPropName(utr: UniversalTracker) {
    return utr.getValueType() === 'table' ? 'table' : 'data';
  }

  renderValueInputContainer(utr: UniversalTracker, utrv: SurveyModelMinimalUtrv, isReadOnly: boolean, isReadOnlyOptions: boolean) {
    const {
      saving, index, value, updateTable, survey, table, valueData, displayCheckbox,
      unit, numberScale, scrollToRef, handleNA, handleNR, handleReject, handleComments,
      hasValueChanged, addons
    } = this.props;
    const { suffix, prefix } = this.getSuffixPrefix(utr);

    const valueDataData = getValueDataProp(valueData, utr.getValueType());
    const defaultPlaceholder = getDefaultPlaceholder(utr);

    // Original utrv is in NA state, but we enter a valid we allow questionValue to change
    const questionValue = getNaType({
      // if we set a value this should not be undefined
      value,
      // still need to read the original valueData, not potential valueData.input
      // that will not contain notApplicableType
      valueData: utrv.valueData,
      status: utrv.status,
    }) === NotApplicableTypes.na ? undefined : value;

    const data: BaseInputProps = {
      universalTracker: utr,
      unitConfig: survey.unitConfig,
      isDisabled: () => isReadOnly,
      handleFocus: this.handleFocus,

      handleError: this.handleError,
      handleCheckboxChange: this.handleCheckboxChange,
      handleValueDataChange: (valueData: any) => this.handleValueDataChange(utr, valueData),
      handleValueChange: this.handleValueChange,
      handleUnitChange: this.handleUnitChange,
      handleNumberScaleChange: this.handleNumberScaleChange,
      inputMessage: this.props.inputMessage,
      handleNA,
      handleNR,
      handleReject,
      handleComments,

      // Input values
      valueDataData: valueDataData,
      questionValue,

      // Multi-row table
      table,
      updateTable,

      placeholder: isNr(utrv) ? 'N/R' : isNa(utrv) ? 'N/A' : defaultPlaceholder,
      saving,
      prefix,
      suffix,
      index,
      unit,
      numberScale,
      displayCheckbox,
      scrollToRef,
      hasValueChanged,
      addons,
      isDownloadBtnBottom: true,
      status: utrv.status,
      initiativeUtr: this.props.initiativeUtr,
      isReadOnlyOptions
    };

    return InputFactory(data);
  }

  cleanUpMessage() {
    this.messageClearTimeout = window.setTimeout(() => this.updateData({ message: '' }), 10000);
  }

  renderMessage(alertClass = 'danger') {
    const { message, errored } = this.props;
    if (!message) {
      return;
    }

    alertClass = (this.props.errored || errored) ? 'danger' : 'success';

    return <span className={`d-block alert alert-${alertClass}`} role='alert'>
      {message}
    </span>
  }
}

const mapDispatchToProps = {
  loadUniversalTrackerModal,
};

const connector = connect(undefined, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>

export default connector(Question);
