/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { afterEach, describe, expect, it, vi } from 'vitest';
import { fireEvent, screen, within } from '@testing-library/react';
import QuestionContainer from './QuestionContainer';
import config from '../../../config';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { ExtendedRenderOptions, renderWithProviders } from '@fixtures/utils';
import { generateUrl } from '@routes/util';
import { createReduxLoadedState, reduxMiddleware } from '@fixtures/redux-store';
import { configureStore } from '@reduxjs/toolkit';
import { reducer } from '@reducers/index';
import { getCurrentUserState, userOne } from '@fixtures/user-factory';
import { UniversalTrackerValueExtended, UtrvHistory } from '@g17eco/types/universalTrackerValue';
import { createInitiativeUtr, customMetricNestleOverride } from '@fixtures/initiative-universal-trackers-fixture';
import {
  answeredUtrvTableMultipleVisibility,
  customMetricNestle,
  surveyDataFull,
  utrvNumberOne,
  utrvPercentageOne,
  utrvPercentageTwo,
  utrvTableMultipleRows,
  utrvTableMultipleVisibility,
  utrvTableRowGroupAggregator,
  utrvTableSingleRow
} from '@fixtures/survey-data-fixture';
import * as numberUtils from '@utils/number';
import { TableColumnType, UniversalTrackerPlain, Variation, VariationDataSource, VariationType } from '@g17eco/types/universalTracker';
import { TableColumn } from '@components/survey/form/input/table/InputInterface';
import userEvent from '@testing-library/user-event';
import { valueListTestTable } from '@fixtures/value-list-factory';
import { MatcherFunction } from '@testing-library/dom/types/matches';
import { NotApplicableTypes } from '@constants/status';
import { SurveyActionData } from '@models/surveyData';
import { UtrvConfigValue } from '@features/question-configuration';
import { faker } from '@faker-js/faker';
import { QUESTION } from '@constants/terminology';
import { CurrentUserData } from '@reducers/current-user';
import { UtrvVariation } from '@g17eco/types/question';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';

describe('QuestionContainer', () => {
  const mockHandleAddAssurance = vi.fn();

  window.HTMLElement.prototype.scrollIntoView = function() {};

  const getUrl = (url: string = '') => `${config.apiUrl}${url}`

  const initiativeId = surveyDataFull.initiativeId;
  const surveyId = surveyDataFull._id;

  const server = setupServer(
    http.get(getUrl('surveys/:surveyId'), async () => HttpResponse.json({
      success: true,
      data: surveyDataFull,
    })),
    http.get(getUrl('o/carbon-calculators'), async () => HttpResponse.json({
      success: true,
      data: [],
    })),
    http.get(getUrl(`blueprints/${surveyDataFull.sourceName}`), async () => HttpResponse.json({
      success: true,
      data: {},
    })),
    http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () => HttpResponse.json({
      success: true,
      data: [
        createInitiativeUtr({
          initiativeId,
          universalTrackerId: utrvPercentageOne.universalTracker._id,
          valueValidation: {
            decimal: 2,
          }
        }),
        createInitiativeUtr({
          initiativeId,
          universalTrackerId: utrvTableMultipleRows.universalTracker._id,
          valueValidation: {
            table: {
              columns: [
                {
                  code: 'table_multi_col_1',
                  name: 'Number Col 1',
                  type: TableColumnType.Number,
                  validation: { decimal: 2 }
                }
              ],
            },
          }
        }),
      ],
    })),
    http.get(getUrl(`initiatives/${initiativeId}/metric-groups/tag`), async () => HttpResponse.json({
      success: true,
      data: [],
    })),
    http.get(getUrl(`bookmarks/universal-tracker-value/survey/${surveyId}`), async () => HttpResponse.json({
      success: true,
      data: [],
    })),
    http.get(getUrl(`assurances/portfolio/survey/${surveyId}`), async () => HttpResponse.json({
      success: true,
      data: [],
    })),
    http.get(getUrl('o/un/sdg'), async () => HttpResponse.json({
      success: true,
      data: { goals: [] },
    })),
    http.post('https://api-eu-central-1.graphcms.com/v2/:id/master', async () => HttpResponse.json({
      data: { glossaries: [] }
    })),
    http.get(getUrl('universal-tracker-values/:utrvId/comments'), async () => HttpResponse.json({
      success: true,
      data: {
        items: [],
        users: [],
      }
    })),
    http.get(getUrl('universal-tracker-values/:utrvId/history'), async () => HttpResponse.json({
      success: true,
      data: {
        latestHistory: {
          documents: [],
          stakeholderHistory: undefined,
          verifierHistory: undefined,
        }
      } satisfies UtrvHistory
    })),
    http.get(getUrl(`initiatives/${initiativeId}/documents`), async () => HttpResponse.json({
      success: true,
      data: {
        documents: [],
        nextCursor: undefined,
        hasNextPage: false,
      },
    })),
    http.patch(getUrl('universal-tracker-values/:utrvId/update'), async () => HttpResponse.json({
      success: true,
      data: []
    })),
    http.get(getUrl('universal-tracker-values/:utrvId/connections'), async () =>
      HttpResponse.json({
        success: true,
        data: {
          connections: [],
          utrvs: [],
        },
      }),
    ),
    http.get(getUrl('universal-tracker-values/:utrvId/variations'), async () => HttpResponse.json({
      success: true,
      data: [],
    })),
  )

  beforeAll(() => server.listen())
  afterEach(() => {
    server.resetHandlers();
    vi.clearAllMocks();
  })
  afterAll(() => server.close())

  const baseRoute = { path: '/company-tracker/reports/:initiativeId/:surveyId/:page?' };
  const baseUrl = generateUrl(baseRoute, {
    initiativeId: surveyDataFull.initiativeId,
    surveyId: surveyId,
    page: 'question',
  });
  const [first, percentageOne] = surveyDataFull.fragmentUniversalTrackerValues;
  // `${path}/question/:questionId/:questionIndex?`
  const baseRoutePath = `${baseRoute.path}/:questionId/:questionIndex?`

  const getRoute = (initialEntry: string) => ({
    initialEntries: [initialEntry],
    path: baseRoutePath,
  });

  const route: ExtendedRenderOptions['route'] = getRoute( `${baseUrl}/${first._id}/0`)

  const createSurveyData = (utrvs: UniversalTrackerValueExtended[]) => {
    return {
      ...surveyDataFull,
      fragmentUniversalTrackerValues: utrvs,
      questionGroups: [
        {
          groupName: 'Group 1',
          questions: utrvs.map((utrv) => ({ utr: utrv.universalTracker })),
          list: [],
          subGroups: [],
        },
      ],
    };
  }

  const createTableOverride = (utrv: UniversalTrackerValueExtended, validation: TableColumn['validation'], extra?: Partial<TableColumn>) => {
    return createInitiativeUtr({
      initiativeId: utrv.initiativeId,
      universalTrackerId: utrv.universalTracker._id,
      valueValidation: {
        table: {
          columns: [
            {
              code: 'table_multi_col_1',
              name: 'Number Col 1',
              type: TableColumnType.Number,
              validation,
              ...extra,
            }
          ],
        },
      }
    });
  }

  const createOverride = (
    utrv: UniversalTrackerValueExtended,
    valueValidation: UniversalTrackerPlain['valueValidation'],
    overrides: Partial<InitiativeUniversalTracker> = {},
  ) => {
    return createInitiativeUtr({
      initiativeId: utrv.initiativeId,
      universalTrackerId: utrv.universalTracker._id,
      valueValidation,
      ...overrides,
    });
  };

  const createNewReduxStore = (survey: SurveyActionData = surveyDataFull, user: Partial<CurrentUserData> = {}) => {
    return configureStore({
      reducer,
      preloadedState: {
        currentUser: getCurrentUserState({ ...userOne, ...user }),
        survey: createReduxLoadedState(survey),
      },
      middleware: reduxMiddleware,
    });
  }
  const store = createNewReduxStore();

  const renderOptions = {
    route,
    store,
  }
  const containerTestId = 'question-container';
  const submitVerifyTestId = 'question-submit-verify-btn';
  const verifyTestId = 'question-verify-btn';
  const fileInputTestId = 'file-dropzone-input';

  it('renders the QuestionContainer component', async () => {
    renderWithProviders(
      <QuestionContainer handleAddAssurance={mockHandleAddAssurance} />,
      renderOptions
    );

    await screen.findByTestId(containerTestId);
    const queryAllByText = screen.queryAllByText(utrvNumberOne.universalTracker.valueLabel);
    // We have two identical entries for alternativeCode...
    expect(queryAllByText.length).greaterThanOrEqual(1);
  });

  it('navigates to the next question', async () => {
    renderWithProviders(
      <QuestionContainer handleAddAssurance={mockHandleAddAssurance} />,
      renderOptions
    );
    const nextButton = await screen.findByTestId('question-next-button');
    fireEvent.click(nextButton);

    const inputElement = screen.getByPlaceholderText<HTMLInputElement>('Key in value');
    expect(inputElement.value).toBe(utrvPercentageOne.value?.toFixed(2));
  });


  describe('Number', async () => {

    it('submit button should be disabled initially', async () => {
      renderWithProviders(
        <QuestionContainer handleAddAssurance={mockHandleAddAssurance} />,
        renderOptions
      );
      const submitButton = await screen.findByTestId(submitVerifyTestId);
      expect(submitButton).toBeDisabled();

      const inputElement = screen.getByPlaceholderText<HTMLInputElement>('Key in value');
      expect(inputElement.value).toBe(String(utrvNumberOne.value));

      // Simulate typing into the input
      fireEvent.change(inputElement, { target: { value: '20' } });

      // Check if the output displays the updated value
      expect(inputElement.value).eq('20');
      // we can now submit
      expect(screen.getByTestId(submitVerifyTestId)).not.toBeDisabled();
    });
  })

  describe('Percentage', async () => {

    // @link: https://worldwidegeneration.atlassian.net/browse/GU-5627
    it('GU-5627 - should allow to update with .00 decimal metric override', async () => {
      renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
        ...renderOptions,
        route: getRoute(`${baseUrl}/${percentageOne._id}`),
      });

      const submitButton = await screen.findByTestId(submitVerifyTestId);
      expect(submitButton).toBeDisabled();

      const inputElement = screen.getByPlaceholderText<HTMLInputElement>('Key in value');
      const value = '5.00'; // 5 with 2 decimals;

      // Check if the output displays the decimal value and value is disabled
      expect(inputElement.value).toBe(value);
      expect(screen.getByTestId(submitVerifyTestId)).toBeDisabled();

      // Simulate typing into the input
      fireEvent.change(inputElement, { target: { value: '5.11' } });
      expect(inputElement.value).eq('5.11');
      expect(screen.getByTestId(submitVerifyTestId)).not.toBeDisabled();

      // we can now submit
      fireEvent.change(inputElement, { target: { value: value } });
      expect(inputElement.value).eq(value);
      // @TODO: [DECIMAL] even though visually it is 5.00, but now it compare '5' vs '5.00'
      expect(screen.getByTestId(submitVerifyTestId)).not.toBeDisabled();
    });

    // @link: https://worldwidegeneration.atlassian.net/browse/GU-5803
    it('GU-5803 - should allow to update with negative enforced decimal', async () => {
      const user = userEvent.setup();
      renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
        ...renderOptions,
        route: getRoute(`${baseUrl}/${percentageOne._id}`),
      });

      const submitButton = await screen.findByTestId(submitVerifyTestId);
      expect(submitButton).toBeDisabled();

      const inputElement = screen.getByPlaceholderText<HTMLInputElement>('Key in value');

      // Simulate typing into the input
      await user.clear(inputElement)
      await user.type(inputElement, '-5.11');
      expect(inputElement.value).eq('-5.11');
      // we can now submit
      expect(screen.getByTestId(submitVerifyTestId)).not.toBeDisabled();
    });

    it('should not trigger decimal formatting on unanswered question', async () => {
      const survey = {
        ...surveyDataFull,
        fragmentUniversalTrackerValues: [{ ...utrvPercentageOne, value: undefined }],
        questionGroups: [
          {
            groupName: 'Group 1',
            questions: [{ utr: utrvPercentageOne.universalTracker }],
            list: [],
            subGroups: [],
          },
        ],
      };

      const store = configureStore({
        reducer,
        preloadedState: {
          currentUser: getCurrentUserState(userOne),
          survey: createReduxLoadedState(survey),
        },
        middleware: reduxMiddleware,
      });

      server.use(
        http.get(getUrl('surveys/:surveyId'), async () =>
          HttpResponse.json({
            success: true,
            data: survey,
          })
        )
      );

      const getDecimalNumberSpy = vi.spyOn(numberUtils, 'getDecimalNumber');
      renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
        store,
        route: getRoute(`${baseUrl}/${percentageOne._id}`),
      });

      const submitButton = await screen.findByTestId(submitVerifyTestId);
      expect(submitButton).toBeDisabled();
      expect(getDecimalNumberSpy).not.toHaveBeenCalled();
    });

    it('should trigger decimal formatting on first render only', async () => {
      renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
        ...renderOptions,
        route: getRoute(`${baseUrl}/${percentageOne._id}`),
      });

      const submitButton = await screen.findByTestId(submitVerifyTestId);
      expect(submitButton).toBeDisabled();

      const inputElement = screen.getByPlaceholderText<HTMLInputElement>('Key in value');
      expect(inputElement.value).toBe(utrvPercentageOne.value?.toFixed(2));

      fireEvent.change(inputElement, { target: { value: '8' } });
      expect(inputElement.value).eq('8');
      expect(screen.getByTestId(submitVerifyTestId)).toBeDisabled();
    });

    it('render correct value of each question when switching between percentage questions', async () => {
      const utrvs = [utrvPercentageOne, utrvPercentageTwo];
      const survey = {
        ...surveyDataFull,
        fragmentUniversalTrackerValues: utrvs,
        questionGroups: [
          {
            groupName: 'Group 1',
            questions: utrvs.map((utrv) => ({ utr: utrv.universalTracker })),
            list: [],
            subGroups: [],
          },
        ],
      };
      server.use(
        http.get(getUrl('surveys/:surveyId'), async () =>
          HttpResponse.json({
            success: true,
            data: survey,
          })
        )
      );

      renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
        store: createNewReduxStore(survey),
        route: getRoute(`${baseUrl}/${percentageOne._id}`),
      });

      const nextButton = await screen.findByTestId('question-next-button');
      fireEvent.click(nextButton);

      const inputElement = screen.getByPlaceholderText<HTMLInputElement>('Key in value');
      expect(inputElement.value).toBe(String(utrvPercentageTwo.value));

      const prevButton = await screen.findByTestId('question-prev-button');
      fireEvent.click(prevButton);

      const inputElement2 = screen.getByPlaceholderText<HTMLInputElement>('Key in value');
      expect(inputElement2.value).toBe(utrvPercentageOne.value?.toFixed(2));
    });
  });

  describe('Table', async () => {


    describe('single row', () => {
      const [col1, col2, col3] = utrvTableSingleRow.universalTracker.valueValidation?.table?.columns || [];

      it('renders the QuestionContainer component with table valueType', async () => {
        renderWithProviders(
          <QuestionContainer handleAddAssurance={mockHandleAddAssurance} />,
          {
            ...renderOptions,
            route: getRoute(`${baseUrl}/${utrvTableSingleRow._id}`),
          }
        );

        const submitButton = await screen.findByTestId(submitVerifyTestId);
        expect(submitButton).toBeDisabled();

        expect(screen.getByText(col1.name)).toBeInTheDocument();
        expect(screen.getByText(col2.name)).toBeInTheDocument();
        expect(screen.getByText(col3.name)).toBeInTheDocument();
      });

      // @link: https://worldwidegeneration.atlassian.net/browse/GU-5815
      it('[GU-5815] should block submit on required decimal error', async () => {

        const inputColumn = { code: col1.code, value: '10.00' };
        const newUtrv = {
          ...utrvTableSingleRow,
          status: 'updated',
          valueData: {
            table: [[inputColumn]],
            input: { table: [[inputColumn]] }
          },
        }
        const survey = createSurveyData([newUtrv]);

        const decimal = 3;
        server.use(
          http.get(getUrl('surveys/:surveyId'), async () => HttpResponse.json({
            success: true,
            data: survey,
          })),
          http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () => HttpResponse.json({
            success: true,
            data: [createTableOverride(newUtrv, { decimal }, { code: col1.code })],
          }))
        );

        renderWithProviders(
          <QuestionContainer handleAddAssurance={mockHandleAddAssurance} />,
          {
            store: createNewReduxStore(survey),
            route: getRoute(`${baseUrl}/${newUtrv._id}`),
          }
        );
        // First check for container, give more time to check for the decimal error
        expect(await screen.findByTestId('question-container')).toBeInTheDocument();
        expect(await screen.findByText('Requirement: 3 decimal places (example 83.123)')).toBeInTheDocument();

        const input = await screen.findByTestId(inputColumn.code);
        expect(input).toBeInTheDocument();

        // Input validation should be initialised, prevents submit
        const submitButton = await screen.findByTestId(submitVerifyTestId);
        expect(submitButton).toBeDisabled();
      });

      it('should allow submit with number scale override on existing question', async () => {

        const user = userEvent.setup();
        const [col1, col2] = customMetricNestle.universalTracker.valueValidation?.table?.columns || [];
        const survey = createSurveyData([customMetricNestle]);

        server.use(
          http.get(getUrl('surveys/:surveyId'), async () => HttpResponse.json({
            success: true,
            data: survey,
          })),
          http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () => HttpResponse.json({
            success: true,
            data: [customMetricNestleOverride],
          })),
          http.patch(getUrl(`universal-tracker-values/${customMetricNestle._id}/update`), async () => {
            return HttpResponse.json({
              success: true,
              data: [],
            });
          })
        );

        renderWithProviders(
          <QuestionContainer handleAddAssurance={mockHandleAddAssurance} />,
          {
            store: createNewReduxStore(survey),
            route: getRoute(`${baseUrl}/${customMetricNestle._id}`),
          }
        );
        // First check for container, give more time to check for the decimal error
        expect(await screen.findByTestId('question-container')).toBeInTheDocument();

        // Change the value and allow to submit
        const colOneInput = await screen.findByTestId(col1.code);
        await user.clear(colOneInput);
        await user.type(colOneInput, '81');

        const submitButton = await screen.findByTestId(submitVerifyTestId);
        expect(submitButton).toBeDisabled();

        // Change the value and allow to submit
        const input = await screen.findByTestId(col2.code);
        expect(input).toBeInTheDocument();
        expect(input).toHaveValue(null);
        await user.type(input, '124');

        expect(await screen.findByTestId(submitVerifyTestId)).toBeDisabled();

        // NumberScale is enforced through metric overrides
        const overrideCol2 = (customMetricNestleOverride.valueValidation?.table?.columns || []).find(
          (col) => col.code === col2.code
        );

        const options = screen.getAllByRole<HTMLInputElement>('option');
        expect(options.map(option => option.value)).to.have.same.members([col2.numberScale, overrideCol2?.numberScaleInput]);
        expect(options.find(option => option.value === col2.numberScale)).toBeDisabled();
        expect(options.find(option => option.value === overrideCol2?.numberScaleInput)).not.toBeDisabled();

        const dropdowns = await screen.findAllByRole<HTMLInputElement>('combobox');
        await user.selectOptions(dropdowns[0], 'single');
        expect(dropdowns[0].value).toBe('single');

        expect(await screen.findByTestId(submitVerifyTestId)).not.toBeDisabled();
      });

      it('should allow submit if value is zero', async () => {
        const user = userEvent.setup();
        const [col1, col2] = customMetricNestle.universalTracker.valueValidation?.table?.columns || [];
        const newUtrv = {
          ...customMetricNestle,
          _id: faker.database.mongodbObjectId(),
          valueData: {
            table: [
              [
                {
                  code: 'nestle-custom-kpi-20-1',
                  value: 0,
                  unit: 'mJ'
                },
                {
                  code: 'nestle-custom-kpi-20-2',
                  value: 0,
                  unit: 'kJ'
                },
              ],
            ],
          },
        };
        const survey = createSurveyData([newUtrv]);

        server.use(
          http.get(getUrl('surveys/:surveyId'), async () => HttpResponse.json({
            success: true,
            data: survey,
          })),
          http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () => HttpResponse.json({
            success: true,
            data: [customMetricNestleOverride],
          })),
        );

        renderWithProviders(
          <QuestionContainer handleAddAssurance={mockHandleAddAssurance} />,
          {
            store: createNewReduxStore(survey),
            route: getRoute(`${baseUrl}/${newUtrv._id}`),
          }
        );
        expect(await screen.findByTestId('question-container')).toBeInTheDocument();

        expect(await screen.findByTestId(col1.code)).toBeInTheDocument();
        expect(await screen.findByTestId(col1.code)).toHaveValue(0);
        expect(await screen.findByTestId(col2.code)).toBeInTheDocument();
        expect(await screen.findByTestId(col2.code)).toHaveValue(0);

        // Should be disabled due to input overrides
        expect(await screen.findByTestId(submitVerifyTestId)).toBeDisabled();

        const [numScaleSelect1, unitSelect1, numScaleSelect2, unitSelect2] = await screen.findAllByRole<HTMLInputElement>('combobox');
        await user.selectOptions(unitSelect1, 'GJ');
        expect(unitSelect1.value).toBe('GJ');
        await user.selectOptions(numScaleSelect1, 'single');
        expect(numScaleSelect1.value).toBe('single');
        await user.selectOptions(unitSelect2, 'MWh');
        expect(unitSelect2.value).toBe('MWh');
        await user.selectOptions(numScaleSelect2, 'single');
        expect(numScaleSelect2.value).toBe('single');

        expect(await screen.findByTestId(submitVerifyTestId)).not.toBeDisabled();
      });

      it('should display input overrides if column is not answered', async () => {
        const survey = createSurveyData([
          {
            ...customMetricNestle,
            valueData: {
              table: [
                [
                  {
                    code: 'nestle-custom-kpi-20-1',
                    unit: 'mJ'
                  },
                  {
                    code: 'nestle-custom-kpi-20-2',
                    unit: 'kJ'
                  },
                ],
              ],
            },
          },
        ]);

        server.use(
          http.get(getUrl('surveys/:surveyId'), async () => HttpResponse.json({
            success: true,
            data: survey,
          })),
          http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () => HttpResponse.json({
            success: true,
            data: [customMetricNestleOverride],
          })),
          http.patch(getUrl(`universal-tracker-values/${customMetricNestle._id}/update`), async () => {
            return HttpResponse.json({
              success: true,
              data: [],
            });
          })
        );

        renderWithProviders(
          <QuestionContainer handleAddAssurance={mockHandleAddAssurance} />,
          {
            store: createNewReduxStore(survey),
            route: getRoute(`${baseUrl}/${customMetricNestle._id}`),
          }
        );
        expect(await screen.findByTestId('question-container')).toBeInTheDocument();
        expect(await screen.findByText('GJ')).toBeInTheDocument();
        expect(await screen.findByText('MWh')).toBeInTheDocument();

        const submitButton = await screen.findByTestId(submitVerifyTestId);
        expect(submitButton).toBeDisabled();
      });
    });

    it('renders with decimal validation', async () => {
      const survey = createSurveyData([utrvTableMultipleRows]);
      const [col1] = utrvTableMultipleRows.universalTracker.valueValidation?.table?.columns || [];
      server.use(
        http.get(getUrl('surveys/:surveyId'), async () => HttpResponse.json({
          success: true,
          data: survey,
        })),
      );

      renderWithProviders(
        <QuestionContainer handleAddAssurance={mockHandleAddAssurance} />,
        {
          store: createNewReduxStore(survey),
          route: getRoute(`${baseUrl}/${utrvTableMultipleRows._id}`),
        }
      );

      const submitButton = await screen.findByTestId(submitVerifyTestId);
      expect(submitButton).toBeDisabled();

      const [columnOne] = utrvTableMultipleRows.valueData?.table?.[0] || [];

      const inputElement = screen.getByTestId<HTMLInputElement>(col1.code);
      fireEvent.change(inputElement, { target: { value: columnOne.value } });

      expect(inputElement.value).toBe(columnOne.value);
    });

    it('renders with decimal validation has changed, value still old with warning', async () => {
      const survey = createSurveyData([utrvTableMultipleRows]);
      const user = userEvent.setup();
      server.use(
        http.get(getUrl('surveys/:surveyId'), async () => HttpResponse.json({
          success: true,
          data: survey,
        })),
        http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () => HttpResponse.json({
          success: true,
          data: [createTableOverride(utrvTableMultipleRows, { decimal: 3 })],
        }))
      );

      renderWithProviders(
        <QuestionContainer handleAddAssurance={mockHandleAddAssurance} />,
        {
          store: createNewReduxStore(survey),
          route: getRoute(`${baseUrl}/${utrvTableMultipleRows._id}`),
        }
      );

      const submitButton = await screen.findByTestId(submitVerifyTestId);
      expect(submitButton).toBeDisabled();

      await user.click(screen.getByText('Edit data'));

      const [columnOne] = utrvTableMultipleRows.valueData?.table?.[0] || [];
      const inputElement = screen.getByTestId<HTMLInputElement>(columnOne.code);
      expect(inputElement.value).toBe(columnOne.value);

      // Expect to have a warning message
      expect(screen.getByText('Requirement: 3 decimal places (example 83.123)')).toBeInTheDocument();
    });

    it('should allow to type number with trailing 000', async () => {
      const newUtrv = { ...utrvTableMultipleRows, valueData: undefined, status: 'created' }
      const survey = createSurveyData([newUtrv]);

      const decimal = 2;
      server.use(
        http.get(getUrl('surveys/:surveyId'), async () => HttpResponse.json({
          success: true,
          data: survey,
        })),
        http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () => HttpResponse.json({
          success: true,
          data: [createTableOverride(newUtrv, { decimal })],
        }))
      );

      renderWithProviders(
        <QuestionContainer handleAddAssurance={mockHandleAddAssurance} />,
        {
          store: createNewReduxStore(survey),
          route: getRoute(`${baseUrl}/${newUtrv._id}`),
        }
      );

      const [columnOne] = newUtrv.universalTracker.valueValidation?.table?.columns || [];
      const inputElement = await screen.findByTestId<HTMLInputElement>(columnOne.code);
      expect(inputElement.value).toBe('');

      const addBtn = await screen.findByText(/Save to table/);
      expect(addBtn).toBeDisabled();

      // Expect to have a warning message (not actual validation)
      expect(screen.getByText('Requirement: 2 decimal places (example 83.12)')).toBeInTheDocument();

      await userEvent.type(await screen.findByTestId<HTMLInputElement>(columnOne.code), '10.005')
      const input = await screen.findByTestId<HTMLInputElement>(columnOne.code);
      expect(input.value).toBe('10.005');

      // Clear and Enter valid
      await userEvent.clear(await screen.findByTestId(columnOne.code));
      await userEvent.type(await screen.findByTestId(columnOne.code), '10.05')
      const updatedInput = await screen.findByTestId<HTMLInputElement>(columnOne.code);
      expect(updatedInput.value).toBe('10.05');

      expect(screen.queryByText((content, element) => {
        if (element?.tagName.toLowerCase() !== 'div') {
          return false;
        }
        return content === 'Requirement: 2 decimal places (example 83.12)';
      })).not.toBeInTheDocument();

      // Finally we can add it to the table
      const addToTable = screen.getByText(/Save to table/);
      expect(addToTable).not.toBeDisabled();
    });

    it('should allow to submit with conditional columns and numberScale override for unanswered question', { timeout: 5000000}, async () => {
      const [_columnOne, secondCol] = utrvTableMultipleVisibility.universalTracker.valueValidation?.table?.columns || [];

      const survey = createSurveyData([utrvTableMultipleVisibility]);
      server.use(
        http.get(getUrl('surveys/:surveyId'), async () => HttpResponse.json({
          success: true,
          data: survey,
        })),
        http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () => HttpResponse.json({
          success: true,
          data: [createTableOverride(
            utrvTableMultipleVisibility,
            { decimal: 2 },
            { code: secondCol.code, numberScaleInput: 'single', numberScaleLocked: true }
          )],
        })),
        http.get(getUrl(`value-list/${valueListTestTable._id}`), async () => HttpResponse.json({
          success: true,
          data: valueListTestTable,
        }))
      );
      const user = userEvent.setup();

      renderWithProviders(
        <QuestionContainer handleAddAssurance={mockHandleAddAssurance} />,
        {
          store: createNewReduxStore(survey),
          route: getRoute(`${baseUrl}/${utrvTableMultipleVisibility._id}`),
        }
      );

      const addBtn = await screen.findByText(/Save to table/, undefined, { timeout: 3000 });
      expect(addBtn).toBeDisabled();

      const list = screen.queryAllByText(secondCol.code);
      expect(list).lengthOf(0);

      // Change dropdown
      const dropdown = screen.getByRole('combobox');
      await user.click(dropdown);
      const option = screen.getByText('Surface Water');
      await user.click(option);

      const element = await screen.findByTestId(secondCol.code);
      expect(element).toBeInTheDocument();
      await user.type(element, '10.05')
      const updatedInput = await screen.findByTestId<HTMLInputElement>(secondCol.code);
      expect(updatedInput.value).toBe('10.05');


      // Finally we can add it to the table
      const addToTable = screen.getByText(/Save to table/);
      expect(addToTable).not.toBeDisabled();
      await user.click(addToTable);

      // Check submit button
      const submitButton = await screen.findByTestId(submitVerifyTestId);
      expect(submitButton).not.toBeDisabled();
    });

    describe('Multi-row conditional question', async () => {
      const [_, secondCol] = answeredUtrvTableMultipleVisibility.universalTracker.valueValidation?.table?.columns || [];

      const createTableOverrideSetup = (columnOverride: {
        code: string;
        numberScaleInput: string;
        numberScaleLocked: boolean;
        validation?: TableColumn['validation'];
      }) => {
        const survey = createSurveyData([answeredUtrvTableMultipleVisibility]);
        server.use(
          http.get(getUrl('surveys/:surveyId'), async () =>
            HttpResponse.json({
              success: true,
              data: survey,
            })
          ),
          http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () =>
            HttpResponse.json({
              success: true,
              data: [createTableOverride(answeredUtrvTableMultipleVisibility, {}, columnOverride)],
            })
          ),
          http.get(getUrl(`value-list/${valueListTestTable._id}`), async () =>
            HttpResponse.json({
              success: true,
              data: valueListTestTable,
            })
          )
        );
        return survey;
      };

      const updateBtnCheck = /Update row/;
      const numberScaleWarningCheck = /Warning: New submissions require the number scale/;

      it('numberScale override "thousands" for answered question', async () => {
        const survey = createTableOverrideSetup({
          code: secondCol.code,
          numberScaleInput: 'thousands',
          numberScaleLocked: true,
        });

        const user = userEvent.setup();

        renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
          store: createNewReduxStore(survey),
          route: getRoute(`${baseUrl}/${answeredUtrvTableMultipleVisibility._id}`),
        });

        // Give more time to find as it takes time to apply all useEffect...
        const rowValidationElement = await screen.findByTestId('row-validation-0', undefined, { timeout: 2000 });

        expect(rowValidationElement, 'Expected to have row validation icon').toBeInTheDocument();
        expect(await screen.findByTestId(`row-validation-0-${secondCol.code}`), 'Expected to have column icon').toBeInTheDocument();

        const actionBtn = await screen.findByTestId('table-input-view-actions-0');
        await user.click(actionBtn);
        const editOptions = screen.getAllByText('Edit data');
        expect(editOptions).lengthOf(2);
        await user.click(editOptions[0]);

        const updateBtn = await screen.findByText(updateBtnCheck);
        expect(updateBtn).toBeDisabled();
        expect(screen.getByText(numberScaleWarningCheck)).toBeInTheDocument();

        const element = await screen.findByTestId<HTMLInputElement>(secondCol.code);
        expect(element.value).toBe('111');

        const options = screen.getAllByRole<HTMLInputElement>('option');
        const [answeredOption, overriddenOption] = options;
        expect(answeredOption).toBeDisabled();
        expect(answeredOption.value).toBe('single');
        expect(overriddenOption).not.toBeDisabled();
        expect(overriddenOption.value).toBe('thousands');

        const [_valueListDropdown, numberScaleDropdown] = await screen.findAllByRole<HTMLInputElement>('combobox');
        await user.selectOptions(numberScaleDropdown, 'thousands');
        expect(numberScaleDropdown.value).toBe('thousands');
        expect(screen.queryByText(numberScaleWarningCheck)).not.toBeInTheDocument();

        // Add updated row to table
        expect(updateBtn).not.toBeDisabled();
        await user.click(updateBtn);

        // Check submit button
        expect(await screen.findByTestId(submitVerifyTestId)).not.toBeDisabled();
      });

      it('numberScale override "single" for answered question', async () => {
        const user = userEvent.setup();
        const survey = createTableOverrideSetup({
          code: secondCol.code,
          numberScaleInput: 'single',
          numberScaleLocked: true,
        });

        renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
          store: createNewReduxStore(survey),
          route: getRoute(`${baseUrl}/${answeredUtrvTableMultipleVisibility._id}`),
        });

        // Ensure container is rendered, before start searching for action button
        expect(await screen.findByTestId('question-container')).toBeInTheDocument();
        const actionBtn = await screen.findByTestId('table-input-view-actions-0');

        // Row does not have error icon, as single is allowed with undefined value
        expect(screen.queryByTestId('row-validation-0')).not.toBeInTheDocument();
        expect(screen.queryByTestId(`row-validation-0-${secondCol.code}`)).not.toBeInTheDocument();

        await user.click(actionBtn);
        const editOptions = screen.getAllByText('Edit data');
        expect(editOptions).lengthOf(2);
        await user.click(editOptions[0]);

        const element = await screen.findByTestId<HTMLInputElement>(secondCol.code);
        expect(element.value).toBe('111');
        await user.type(element, '222');

        // No warning as single is allowed with undefined value
        expect(screen.queryByText(numberScaleWarningCheck)).not.toBeInTheDocument();

        // Add updated row to table
        expect(await screen.findByText(updateBtnCheck)).not.toBeDisabled();
        await user.click(screen.getByText(updateBtnCheck));

        // Check submit button
        const submitButton = await screen.findByTestId(submitVerifyTestId);
        expect(submitButton).not.toBeDisabled();
      });


      it('decimals override for answered rows', async () => {
        const user = userEvent.setup();
        const survey = createTableOverrideSetup({
          code: secondCol.code,
          numberScaleInput: 'single',
          numberScaleLocked: true,
          validation: { decimal: 2 },
        });

        renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
          store: createNewReduxStore(survey),
          route: getRoute(`${baseUrl}/${answeredUtrvTableMultipleVisibility._id}`),
        });

        const actionBtn = await screen.findByTestId('table-input-view-actions-0');

        // Row does have error icon, as decimal validation
        expect(screen.getByTestId('row-validation-0')).toBeInTheDocument();
        expect(screen.getByTestId(`row-validation-0-${secondCol.code}`)).toBeInTheDocument();

        await user.click(actionBtn);
        const editOptions = screen.getAllByText('Edit data');
        expect(editOptions).lengthOf(2);
        await user.click(editOptions[0]);

        const element = await screen.findByTestId<HTMLInputElement>(secondCol.code);
        expect(element.value).toBe('111');

        const decimalErrorMsgCheck: MatcherFunction = (content, element) => {
          if (element?.tagName.toLowerCase() !== 'div') {
            return false;
          }
          return content === 'Requirement: 2 decimal places (example 83.12)';
        };
        expect(screen.getByText(decimalErrorMsgCheck)).toBeInTheDocument();

        expect(screen.queryByText('111.00')).not.toBeInTheDocument();

        // disabled as decimals issues are actual errors.
        expect(await screen.findByTestId(submitVerifyTestId)).toBeDisabled();

        // Fix and no longer warning text.
        await user.type(element, '111.01');
        expect(screen.queryByText(decimalErrorMsgCheck)).not.toBeInTheDocument();

        // Add updated row to table
        expect(await screen.findByText(updateBtnCheck)).not.toBeDisabled();
        await user.click(screen.getByText(updateBtnCheck));

        // Check submit button
        const submitButton = await screen.findByTestId(submitVerifyTestId);
        expect(submitButton).not.toBeDisabled();
      });
    });

  });

  describe('N/A or N/R', async () => {
    const commonData = {
      status: 'updated',
      value: undefined,
      valueData: {
        notApplicableType: NotApplicableTypes.na,
      },
      stakeholders: {
        stakeholder: [userOne._id],
        verifier: [userOne._id],
      },
    };
    const utrvConfig = {
      evidenceRequired: UtrvConfigValue.Default,
      isPrivate: UtrvConfigValue.Default,
      noteRequired: UtrvConfigValue.Default,
      verificationRequired: UtrvConfigValue.Required,
    };

    it('should allow verify for percentage question', async () => {
      const utrv: UniversalTrackerValueExtended = {
        ...utrvPercentageOne,
        ...commonData,
      };
      const survey: SurveyActionData = createSurveyData([utrv]);
      server.use(
        http.get(getUrl('surveys/:surveyId'), async () =>
          HttpResponse.json({
            success: true,
            data: survey,
          })
        ),
        http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () =>
          HttpResponse.json({
            success: true,
            data: [
              createInitiativeUtr({
                initiativeId,
                universalTrackerId: utrvPercentageOne.universalTracker._id,
                utrvConfig,
              }),
            ],
          })
        )
      );

      renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
        store: createNewReduxStore(survey),
        route: getRoute(`${baseUrl}/${utrv._id}`),
      });
      expect(await screen.findByTestId('question-container')).toBeInTheDocument();
      // Submit button should show 'Verify' initially
      expect(await screen.findByTestId(verifyTestId)).toBeEnabled();

      const value = '5';
      const inputElement = screen.getByPlaceholderText<HTMLInputElement>('N/A');
      fireEvent.change(inputElement, { target: { value } });
      expect(inputElement.value).toBe(value);

      // Button change to submit and verify
      expect(await screen.findByTestId(submitVerifyTestId)).toBeEnabled();
      expect(screen.queryByTestId(verifyTestId)).not.toBeInTheDocument();

      fireEvent.change(inputElement, { target: { value: '' } });
      expect(inputElement.value).toBe('');

      // Button change back to verify
      expect(await screen.findByTestId(verifyTestId)).toBeEnabled();
      expect(screen.queryByTestId(submitVerifyTestId)).not.toBeInTheDocument();
    });

    it('should allow verify for table question', async () => {
      const newUtrv: UniversalTrackerValueExtended = { ...utrvTableMultipleRows, ...commonData };
      const survey = createSurveyData([newUtrv]);

      server.use(
        http.get(getUrl('surveys/:surveyId'), async () =>
          HttpResponse.json({
            success: true,
            data: survey,
          })
        ),
        http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () =>
          HttpResponse.json({
            success: true,
            data: [
              createInitiativeUtr({
                initiativeId: newUtrv.initiativeId,
                universalTrackerId: newUtrv.universalTracker._id,
                valueValidation: {
                  table: {
                    columns: [
                      {
                        code: 'table_multi_col_1',
                        name: 'Number Col 1',
                        type: TableColumnType.Number,
                      },
                    ],
                  },
                },
                utrvConfig,
              }),
            ],
          })
        )
      );

      renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
        store: createNewReduxStore(survey),
        route: getRoute(`${baseUrl}/${newUtrv._id}`),
      });

      expect(await screen.findByTestId('question-container')).toBeInTheDocument();
      // Submit button should show 'Verify' initially
      expect(await screen.findByTestId(verifyTestId)).toBeEnabled();

      const [columnOne] = newUtrv.universalTracker.valueValidation?.table?.columns || [];
      const inputElement = await screen.findByTestId<HTMLInputElement>(columnOne.code);
      expect(inputElement.value).toBe('');

      const addBtn = await screen.findByText(/Save to table/);
      expect(addBtn).toBeDisabled();

      await userEvent.type(await screen.findByTestId<HTMLInputElement>(columnOne.code), '12345');
      const input = await screen.findByTestId<HTMLInputElement>(columnOne.code);
      expect(input.value).toBe('12345');

      expect(addBtn).toBeEnabled();
      await userEvent.click(addBtn);

      // Added row should make button change to submit and verify
      expect(await screen.findByTestId(submitVerifyTestId)).toBeEnabled();

      // Delete the newly added row should change button back to verify
      const deleteButton = screen.getByText('Delete row');
      fireEvent.click(deleteButton);
      expect(await screen.findByTestId(verifyTestId)).toBeEnabled();
      expect(screen.queryByTestId(submitVerifyTestId)).not.toBeInTheDocument();
    });
  });

  describe('Uploading files', async () => {
    const store = createNewReduxStore(surveyDataFull, { isStaff: true });
    it('should display add to library checkbox if user is staff and new files are added', async () => {
      renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
        ...renderOptions,
        store,
      });

      const submitButton = await screen.findByTestId(submitVerifyTestId);
      expect(submitButton).toBeDisabled();
      const inputElement = screen.getByPlaceholderText<HTMLInputElement>('Key in value');
      expect(inputElement.value).toBe(String(utrvNumberOne.value));

      // Simulate typing into the input
      fireEvent.change(inputElement, { target: { value: '20' } });

      const inputWrapper = await screen.findByRole('presentation');
      const fileInput = await within(inputWrapper).findByTestId(fileInputTestId);
      const file = new File(['file content'], 'test-file.txt', { type: 'text/plain' });
      // Simulate add files into the input
      await userEvent.upload(fileInput, file);

      expect(inputElement.value).eq('20');
      // we can now submit
      const button = screen.getByTestId(submitVerifyTestId);
      expect(button).not.toBeDisabled();
      expect(screen.getByTestId('add-to-library-checkbox')).toBeInTheDocument();
    });

    it('should not display add to library checkbox if no new files are added', async () => {
      renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
        ...renderOptions,
        ...store,
      });

      const submitButton = await screen.findByTestId(submitVerifyTestId);
      expect(submitButton).toBeDisabled();
      const inputElement = screen.getByPlaceholderText<HTMLInputElement>('Key in value');
      expect(inputElement.value).toBe(String(utrvNumberOne.value));

      // Simulate typing into the input
      fireEvent.change(inputElement, { target: { value: '20' } });

      expect(inputElement.value).eq('20');
      // we can now submit
      const button = screen.getByTestId(submitVerifyTestId);
      expect(button).not.toBeDisabled();
      expect(screen.queryByTestId('add-to-library-checkbox')).not.toBeInTheDocument();
    });
  });

  describe('Variation detect', () => {
    const createVariation = (overrides: Partial<Variation> = {}) => ({
      min: 10,
      max: 10,
      dataSource: VariationDataSource.LatestVerified,
      type: VariationType.Percentage,
      confirmationRequired: false,
      ...overrides,
    });

    const createUtrvVariation = (overrides: Partial<UtrvVariation> = {}) => ({
      ...createVariation(overrides),
      valueListCode: undefined,
      details: {
        baseline: 200,
        min: 180,
        max: 220,
        unit: undefined,
        numberScale: undefined,
        effectiveDate: '2024-12-31T23:59:59.999Z',
      },
      ...overrides,
    });

    it('should show variation warning message if input is out of range', async () => {
      const survey: SurveyActionData = createSurveyData([utrvNumberOne]);
      server.use(
        http.get(getUrl('surveys/:surveyId'), async () =>
          HttpResponse.json({
            success: true,
            data: survey,
          }),
        ),
        http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () =>
          HttpResponse.json({
            success: true,
            data: [createOverride(utrvNumberOne, { variations: [createVariation()] })],
          }),
        ),
        http.get(getUrl(`universal-tracker-values/${utrvNumberOne._id}/variations`), async () =>
          HttpResponse.json({
            success: true,
            data: [createUtrvVariation()],
          }),
        ),
      );

      renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
        store: createNewReduxStore(survey),
        route: getRoute(`${baseUrl}/${utrvNumberOne._id}`),
      });
      // First check for container, give more time to check for the variation detection
      expect(await screen.findByTestId('question-container')).toBeInTheDocument();
      const variationWarningMessage =
        'The value entered falls outside of the 10% variance limit set by the admin. ' +
        'The baseline value for this metric was 200 set in December 2024.';
      expect(screen.getByText(variationWarningMessage)).toBeInTheDocument();

      const inputElement = screen.getByPlaceholderText<HTMLInputElement>('Key in value');

      // change input to be within variance
      fireEvent.change(inputElement, { target: { value: '180' } });
      expect(inputElement.value).eq('180');
      expect(screen.queryByText(variationWarningMessage)).not.toBeInTheDocument();

      // change input to be outside of variance
      fireEvent.change(inputElement, { target: { value: '179' } });
      expect(inputElement.value).eq('179');
      expect(screen.getByText(variationWarningMessage)).toBeInTheDocument();

      // clear input, no variation warning
      fireEvent.change(inputElement, { target: { value: '' } });
      expect(inputElement.value).eq('');
      expect(screen.queryByText(variationWarningMessage)).not.toBeInTheDocument();
    });

    it('should show warning message in priority: decimal -> unit -> variation', async () => {
      const user = userEvent.setup();
      const survey: SurveyActionData = createSurveyData([utrvNumberOne]);
      server.use(
        http.get(getUrl('surveys/:surveyId'), async () =>
          HttpResponse.json({
            success: true,
            data: survey,
          }),
        ),
        http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () =>
          HttpResponse.json({
            success: true,
            data: [
              createOverride(
                utrvNumberOne,
                { variations: [createVariation()], decimal: 2 },
                { numberScaleInput: 'hundreds', numberScaleLocked: true },
              ),
            ],
          }),
        ),
        http.get(getUrl(`universal-tracker-values/${utrvNumberOne._id}/variations`), async () =>
          HttpResponse.json({
            success: true,
            data: [createUtrvVariation()],
          }),
        ),
      );

      renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
        store: createNewReduxStore(survey),
        route: getRoute(`${baseUrl}/${utrvNumberOne._id}`),
      });

      // First check for container, give more time to check for the variation detection
      expect(await screen.findByTestId('question-container')).toBeInTheDocument();
      const decimalWarningMessage = 'Requirement: 2 decimal places (example 83.12)';
      const variationWarningMessage =
        'The value entered falls outside of the 10% variance limit set by the admin. ' +
        'The baseline value for this metric was 200 set in December 2024.';
      const unitWarningMessage = /New submissions require the number scale/;

      const inputElement = screen.getByPlaceholderText<HTMLInputElement>('Key in value');
      expect(screen.getByTestId(submitVerifyTestId)).toBeDisabled();

      /**
       * input is formatted with decimal by default
       * clear input's decimal
       * decimal, unit, variation are invalid at the same time
       * show decimal warning
       */
      fireEvent.change(inputElement, { target: { value: '100' } });
      expect(inputElement.value).eq('100');

      const decimalErrorText = screen.getByText(decimalWarningMessage);
      expect(decimalErrorText).toBeInTheDocument();
      expect(decimalErrorText).toHaveClass('invalid-feedback');
      expect(screen.queryByText(unitWarningMessage)).not.toBeInTheDocument();
      expect(screen.queryByText(variationWarningMessage)).not.toBeInTheDocument();

      // change input to have valid decimal -> show unit warning
      fireEvent.change(inputElement, { target: { value: '100.00' } });
      expect(inputElement.value).eq('100.00');

      expect(screen.queryByText(decimalWarningMessage)).not.toBeInTheDocument();
      expect(screen.getByText(unitWarningMessage)).toBeInTheDocument();
      expect(screen.queryByText(variationWarningMessage)).not.toBeInTheDocument();

      // change input to have valid number scale -> show variation warning
      const numberScaleDropdown = screen.getByRole<HTMLInputElement>('combobox');
      await user.selectOptions(numberScaleDropdown, 'hundreds');
      expect(numberScaleDropdown.value).toBe('hundreds');

      expect(screen.queryByText(decimalWarningMessage)).not.toBeInTheDocument();
      expect(screen.queryByText(unitWarningMessage)).not.toBeInTheDocument();
      expect(screen.getByText(variationWarningMessage)).toBeInTheDocument();

      // decimal and numberScale are valid -> submit is enabled
      expect(screen.getByTestId(submitVerifyTestId)).not.toBeDisabled();

      // change input to be within variance -> show decimal helper text
      fireEvent.change(inputElement, { target: { value: '1.80' } });
      expect(inputElement.value).eq('1.80');

      const decimalHelperText = screen.getByText(decimalWarningMessage);
      expect(decimalHelperText).toBeInTheDocument();
      expect(decimalHelperText).toHaveClass('text-ThemeTextMedium');
      expect(screen.queryByText(unitWarningMessage)).not.toBeInTheDocument();
      expect(screen.queryByText(variationWarningMessage)).not.toBeInTheDocument();
    });

    describe('Table question', () => {
      it('should render single row table with variations', async () => {
        const survey: SurveyActionData = createSurveyData([utrvTableSingleRow]);

        const [col1, col2, col3] = utrvTableSingleRow.universalTracker.valueValidation?.table?.columns || [];
        const valueValidation = {
          table: {
            columns: [
              { ...col1, validation: { variations: [createVariation()] } },
              { ...col2, validation: { variations: [createVariation()] } },
              { ...col3 },
            ],
          },
        };

        server.use(
          http.get(getUrl('surveys/:surveyId'), async () =>
            HttpResponse.json({
              success: true,
              data: survey,
            }),
          ),
          http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () =>
            HttpResponse.json({
              success: true,
              data: [createOverride(utrvTableSingleRow, valueValidation)],
            }),
          ),
          http.get(getUrl(`universal-tracker-values/${utrvTableSingleRow._id}/variations`), async () =>
            HttpResponse.json({
              success: true,
              data: [
                createUtrvVariation({ valueListCode: col1.code }),
                createUtrvVariation({ valueListCode: col2.code }),
              ],
            }),
          ),
        );
        renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
          store: createNewReduxStore(survey),
          route: getRoute(`${baseUrl}/${utrvTableSingleRow._id}`),
        });

        const variationWarningMessage =
          'The value entered falls outside of the 10% variance limit set by the admin. ' +
          'The baseline value for this metric was 200 set in December 2024.';

        // Ensure container is rendered, before start searching for action button
        expect(await screen.findByTestId('question-container')).toBeInTheDocument();
        /**
         * answered utrv: col1: 101, col2: 102000
         * variations for both col1, col2: { min: 180, max: 220, baseline: 200, variation: 10% }
         */
        expect(screen.queryAllByText(variationWarningMessage).length).toEqual(2);

        // clear inputs should not show warning
        await userEvent.clear(await screen.findByTestId(col1.code));
        await userEvent.clear(await screen.findByTestId(col2.code));
        expect(screen.queryAllByText(variationWarningMessage).length).toEqual(0);

        // change col1's value within variance should not show warning
        await userEvent.type(await screen.findByTestId<HTMLInputElement>(col1.code), '180');
        expect(screen.queryAllByText(variationWarningMessage).length).toEqual(0);

        // change col2's value out of variance should show one warning
        await userEvent.type(await screen.findByTestId<HTMLInputElement>(col2.code), '179');
        expect(screen.queryAllByText(variationWarningMessage).length).toEqual(1);
      });

      it('should render multi row table with variations', async () => {
        const survey: SurveyActionData = createSurveyData([utrvTableRowGroupAggregator]);

        const [valueListCol, numCol] =
          utrvTableRowGroupAggregator.universalTracker.valueValidation?.table?.columns || [];
        const valueValidation = {
          table: {
            columns: [{ ...numCol, validation: { variations: [createVariation()] } }],
          },
        };

        server.use(
          http.get(getUrl('surveys/:surveyId'), async () =>
            HttpResponse.json({
              success: true,
              data: survey,
            }),
          ),
          http.get(getUrl(`value-list/${valueListTestTable._id}`), async () =>
            HttpResponse.json({
              success: true,
              data: valueListTestTable,
            }),
          ),
          http.get(getUrl(`initiative-universal-trackers/initiative/${initiativeId}`), async () =>
            HttpResponse.json({
              success: true,
              data: [createOverride(utrvTableRowGroupAggregator, valueValidation)],
            }),
          ),
          http.get(getUrl(`universal-tracker-values/${utrvTableRowGroupAggregator._id}/variations`), async () =>
            HttpResponse.json({
              success: true,
              data: [
                createUtrvVariation({
                  valueListCode: numCol.code,
                  details: {
                    baseline: 200,
                    min: 180,
                    max: 220,
                    unit: undefined,
                    numberScale: undefined,
                    effectiveDate: new Date('2024-12-31T23:59:59.999Z'),
                    aggregationColumns: [
                      {
                        code: valueListCol.code,
                        value: valueListTestTable.options[0].code,
                      },
                    ],
                  },
                }),
              ],
            }),
          ),
          http.post(
            getUrl(`universal-tracker-values/${utrvTableRowGroupAggregator._id}/aggregated-table-data`),
            async () =>
              HttpResponse.json({
                success: true,
                data: [
                  [
                    {
                      code: valueListCol.code,
                      value: valueListTestTable.options[0].code,
                    },
                    {
                      code: numCol.code,
                      value: 100,
                    },
                  ],
                ],
              }),
          ),
        );
        renderWithProviders(<QuestionContainer handleAddAssurance={mockHandleAddAssurance} />, {
          store: createNewReduxStore(survey),
          route: getRoute(`${baseUrl}/${utrvTableRowGroupAggregator._id}`),
        });

        expect(
          await screen.findByText('Some of the values entered falls outside of the variance limit set by the admin.'),
        ).toBeInTheDocument();
      });
    });
  });
});
