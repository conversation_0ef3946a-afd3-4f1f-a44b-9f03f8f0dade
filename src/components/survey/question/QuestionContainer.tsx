/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useAppDispatch, useAppSelector } from '../../../reducers';
import { getSurveyGroupsSelector } from '../../../selectors/survey';
import { getInitiativeMateriality } from '../../../selectors/initiative';
import { getBlueprintFlexSearchMap } from '../../../selectors/blueprint';
import { SurveyAddAssurance } from '../../../types/survey';
import { Loader } from '@g17eco/atoms/loader';
import { useHistory, useLocation, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { loadSurvey, unloadSurveyListSummary } from '../../../actions/survey';
import { reloadSurveyList } from '../../../slice/initiativeSurveyListSlice';
import { generateUrl } from '../../../routes/util';
import { ROUTES } from '../../../constants/routes';
import Dashboard, { DashboardRow, DashboardSection } from '../../dashboard';
import { QuestionContainerHeaderStateless } from './QuestionContainerHeaderStateless';
import { QuestionContainerFooterStateless } from './QuestionContainerFooterStateless';
import { QuestionContainerStateless } from './QuestionContainerStateless';
import { NotFoundMetric } from '../../../apps/company-tracker/components/not-found-metric';
import { getFormattedSurveyDate, isAggregatedSurvey } from '@utils/survey';
import { AggregatedQuestionBreakdown } from '@features/survey/question/aggregated-breakdown/AggregatedQuestionBreakdown';
import { isStaff } from '@selectors/user';
import { useGetInitiativeUniversalTrackersQuery } from '@api/initiative-universal-trackers';
import { getRootInitiativeMap } from '@features/question-configuration';
import { useGetBlueprintsQuery } from '@api/blueprints';
import { useToggle } from '@hooks/useToggle';
import { SURVEY } from '@constants/terminology';
import { skipToken } from '@reduxjs/toolkit/query';
import { getDraftConfig } from './draft/draftAddons';
import classNames from 'classnames';
import { UpdateActions } from '@actions/universalTrackerValue';

interface QuestionContainerProps {
  isDraft?: boolean;
  handleAddAssurance: (survey: SurveyAddAssurance) => void;
}

export default function QuestionContainer(props: QuestionContainerProps) {
  const { handleAddAssurance } = props;
  const dispatch = useAppDispatch();
  const history = useHistory();
  const location = useLocation();

  const { initiativeId, surveyId, questionId, questionIndex }: { [key: string]: string } = useParams();

  const surveyGroups = useAppSelector(getSurveyGroupsSelector);
  const materiality = useAppSelector(getInitiativeMateriality);
  const searchIndex = useAppSelector(getBlueprintFlexSearchMap)
  const surveyState = useAppSelector((state) => state.survey);
  const isUserStaff = useAppSelector(isStaff);
  const [showMapping, toggleMapping] = useToggle(false);
  const [postSubmitFlashClass, setPostSubmitFlashClass] = useState<string | undefined>(undefined);

  const { data = [], isLoading } = useGetInitiativeUniversalTrackersQuery(initiativeId);
  const rootInitiativeUtrMap = getRootInitiativeMap(data);

  const { data: blueprint = {}, isFetching: isFetchingBlueprint } = useGetBlueprintsQuery(
    surveyState.loaded ? surveyState.data.sourceName : skipToken
  );

  useEffect(() => {
    dispatch(loadSurvey(surveyId));
  }, [surveyId, dispatch]);

  const handleReload = async () => {
    return Promise.all([
      dispatch(loadSurvey(surveyId, false, true)),
      dispatch(unloadSurveyListSummary()),
      dispatch(reloadSurveyList()),
    ]);
  }

  const handleGoToQuestion = ({ id, index, changeDraftTo }: { id: string, index?: number, changeDraftTo?: boolean }) => {
    const url = `${generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, { initiativeId, surveyId, page: 'question' })}/${id}/${index ?? ''}`;
    const searchParams = new URLSearchParams(location.search);
    // Keep current url or switch to draft view
    history.push({
      pathname: (changeDraftTo ?? props.isDraft) ? `${url}/draft` : url,
      search: searchParams.toString()
    });
  };

  if (surveyState.errored) {
    return <NotFoundMetric initiativeId={initiativeId} />
  }

  if (!surveyState.loaded || isFetchingBlueprint || isLoading) {
    return <Loader />;
  }

  const survey = surveyState.data;
  const utrv = survey.fragmentUniversalTrackerValues.find(utrv => utrv._id === questionId);
  const isAggregate = isAggregatedSurvey(survey.type);

  if (!utrv) {
    return <NotFoundMetric initiativeId={initiativeId} surveyId={surveyId} />
  }

  const { addons: headerAddons, isDraftEnabled } = getDraftConfig({
    questionId,
    status: utrv.status,
    isDraft: props.isDraft,
    handleGoToQuestion,
    isUserStaff,
    questionIndex,
  });
  const postSubmit = (action: string) => {
    setPostSubmitFlashClass(UpdateActions.STATUS.REJECT === action ? 'rejected' : 'updated');
    setTimeout(() => setPostSubmitFlashClass(undefined), 500);
  };

  return (
    <Dashboard id='question-container' className='question-view' testId={'question-container'}>
      <DashboardRow>
        <QuestionContainerHeaderStateless
          toggleMapping={toggleMapping}
          initiativeId={initiativeId}
          surveyId={surveyId}
          questionId={questionId}
          questionIndex={questionIndex}
          handleAddAssurance={handleAddAssurance}
          survey={survey}
          surveyGroups={surveyGroups}
          materiality={materiality}
          blueprint={blueprint}
          searchIndex={searchIndex}
          handleReload={handleReload}
          handleGoToQuestion={handleGoToQuestion}
          rootBreadcrumb={{
            label: `${getFormattedSurveyDate(survey)} ${SURVEY.SINGULAR}`,
            url: generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, { initiativeId, surveyId, page: 'overview' }),
          }}
        />
      </DashboardRow>
      {headerAddons ? <DashboardRow>{headerAddons}</DashboardRow> : null}

      <DashboardSection
        className={classNames({ 'question-wrapper': true, 'draft-view': isDraftEnabled })}
        padding={2}
        classes={
          postSubmitFlashClass ? { whiteBoxContainer: classNames('post-submit-flash', postSubmitFlashClass) } : {}
        }
      >
        <QuestionContainerStateless
          isDraft={isDraftEnabled}
          initiativeId={initiativeId}
          config={{ enableComments: !isDraftEnabled }}
          surveyId={surveyId}
          questionId={questionId}
          questionIndex={questionIndex}
          survey={survey}
          surveyGroups={surveyGroups}
          materiality={materiality}
          blueprint={blueprint}
          searchIndex={searchIndex}
          handleReload={handleReload}
          rootInitiativeUtrMap={rootInitiativeUtrMap}
          showMapping={showMapping}
          postSubmit={postSubmit}
        />
        {isAggregate ? (
          <AggregatedQuestionBreakdown
            initiativeId={initiativeId}
            survey={survey}
            surveyGroups={surveyGroups}
            questionId={questionId}
          />
        ) : null}
      </DashboardSection>

      <QuestionContainerFooterStateless
        initiativeId={initiativeId}
        surveyId={surveyId}
        questionId={questionId}
        questionIndex={questionIndex}
        survey={survey}
        surveyGroups={surveyGroups}
        materiality={materiality}
        blueprint={blueprint}
        searchIndex={searchIndex}
      />
    </Dashboard>
  );
}
