/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import React, { useMemo, useState } from 'react';
import Dashboard, { DashboardSection } from '../../../dashboard';
import { CollapseButton, CollapseContent, CollapsePanel } from '@g17eco/molecules/collapse-panel';
import { ConnectionQuestion } from './ConnectionQuestion';
import UniversalTracker from '../../../../model/UniversalTracker';
import { useGetConnectionsQuery } from '../../../../api/utrv';
import { Loader } from '@g17eco/atoms/loader';
import { FlickityWrapper } from '../../../flickity-wrapper';
import './ConnectionContainer.scss';
import { QUESTION } from '@constants/terminology';

interface Props {
  selectedStandard: string,
  questionId: string;
}

const options = {
  cellAlign: 'left',
  pageDots: true,
  draggable: false,
  groupCells: true
};

export const ConnectionsContainer = ({ questionId, selectedStandard }: Props) => {

  const [isOpen, setIsOpen] = useState(true);

  const { isLoading, isSuccess, data: connData } = useGetConnectionsQuery(questionId);

  const data = useMemo(() => {
    if (!connData) {
      return undefined;
    }

    return {
      ...connData,
      utrvs: connData.utrvs.map(utrv => {
        return {
          utrv,
          utr: new UniversalTracker(utrv.universalTracker),
        }
      })
    }
  }, [connData])

  if (isLoading) {
    return <Loader />
  }


  if (!isSuccess || !data || data.connections.length === 0) {
    return null
  }

  const onUseData = () => {
    alert('Not supported');
  }

  return (
    <Dashboard className={'w-100 connection-container'}>
      <DashboardSection paddingInternal={0}>
        <CollapsePanel collapsed={!isOpen} onCollapse={(v) => setIsOpen(v)}>
            <CollapseButton isOpen={isOpen} disabled={isOpen}>
              <h2 className='question-title-container'>Related {QUESTION.PLURAL}</h2>
            </CollapseButton>

          <CollapseContent className={'mt-2 pb-3'}>
            <FlickityWrapper disableImagesLoaded={true} reloadOnUpdate={true} options={options}>
              {data.connections.map((conn, i) => {
                return <div key={`${conn.formula}-${i}`}>
                  <ConnectionQuestion
                    className={'mx-3'}
                    connection={conn}
                    utrvs={data.utrvs}
                    onUseData={onUseData}
                    selectedStandard={selectedStandard} />
                </div>
              })}
            </FlickityWrapper>
          </CollapseContent>
        </CollapsePanel>
      </DashboardSection>
    </Dashboard>
  );
}
