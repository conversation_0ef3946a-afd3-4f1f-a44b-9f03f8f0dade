import { Button, ButtonProps } from 'reactstrap';

interface Props extends Pick<ButtonProps, 'size'> {
  link?: string;
  label?: string;
}

export const InstructionLink = (props: Props) => {
  const { link, label, size = 'sm' } = props;

  if (!link) {
    return null;
  }

  return (
    <Button color='link' size={size} className='px-0 mt-2 d-block' onClick={() => window.open(link, '_blank')}>
      <u>{label}</u>
    </Button>
  );
};
