import { Button } from 'reactstrap';

interface ReadMoreButtonProps {
  isHidden: boolean;
  onToggle: () => void;
  className?: string;
}

export const ReadMoreButton = ({ isHidden, onToggle, className = '' }: ReadMoreButtonProps) => {
  const iconClass = `fal fa-caret-${isHidden ? 'down' : 'up'}`;

  const text = isHidden ? 'Read more' : 'Read less';

  return (
    <Button color='link-secondary' onClick={onToggle} className={className}>
      <i className={`${iconClass} mr-1`} />
      <span className='text-xs'>{text}</span>
    </Button>
  );
};
