import { ReadonlyRichTextEditor } from '@features/rich-text-editor/ReadonlyRichTextEditor';
import { EditorState } from 'lexical';
import { useRef, useState } from 'react';
import { InstructionLink } from './InstructionLink';
import { ReadMoreButton } from './ReadMoreButton';
import { useTextCollapse } from '@hooks/useTextCollapse';

interface Props {
  editorState: EditorState;
  link?: string;
  label?: string;
  size?: string;
}

export const ReadonlyInstructionsEditor = (props: Props) => {
  const { editorState, link: instructionsLink, label: instructionsText, size } = props;

  const [isHidden, setIsHidden] = useState(true);
  const toggleExpansion = () => setIsHidden((prev) => !prev);
  const instructionRef = useRef<HTMLDivElement | null>(null);

  const { isTruncated, height, className } = useTextCollapse({
    elementRef: instructionRef,
    isHidden,
  });

  return (
    <>
      <div ref={instructionRef} className={`instruction-text ${className}`.trim()} style={{ height }}>
        <ReadonlyRichTextEditor editorState={editorState} />
        <InstructionLink link={instructionsLink} label={instructionsText} size={size} />
      </div>
      {isTruncated && (
        <ReadMoreButton
          isHidden={isHidden}
          onToggle={toggleExpansion}
          className={!isHidden && instructionsLink ? 'mt-2' : ''}
        />
      )}
    </>
  );
};
