/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ExistingEvidenceFile, PrepareSubmitDataParams, QuestionSubmitData, RowStatus, TableDataInfo, ValueData, ValueDataObject } from './questionInterfaces';
import { convertDataForSubmissions, prepareSubmitData } from './questionSubmit';
import UniversalTracker from '../../../model/UniversalTracker';
import { UtrValueTypes } from '../../../utils/universalTracker';
import { NotApplicableTypes } from '../../../constants/status';
import { InputColumn, TableColumn, ValueTable } from '../form/input/table/InputInterface';
import { TableColumnType, UtrValueType } from '../../../types/universalTracker';
import { SurveyModelMinimalUtrv } from '../../../model/surveyData';
import { NewEvidenceFile } from '../../../types/file';
import { defaultUniversalTrackerFields } from '../../../__fixtures__/questions-fixture';
import { getNotReportingNote, omissionMap, OmissionReason } from './NotReportingModal';

const fakeSurveyModelMinimalUtrv = (props: Partial<SurveyModelMinimalUtrv>): SurveyModelMinimalUtrv => ({
  _id: '',
  universalTrackerId: '',
  initiativeId: '',
  status: '',
  stakeholders: { stakeholder: [], verifier: [] },
  lastUpdated: '',
  effectiveDate: '',
  ...props
});

const createData = (
  props: Partial<PrepareSubmitDataParams> = {},
  utr: object = { valueType: UtrValueTypes.percentage },
  utrv = { value: 50 }
) => {

  const requiredProps: PrepareSubmitDataParams = {
    displayCheckbox: {},
    table: { editRowId: -1, rows: [] },
    unit: undefined,
    autoVerify: false,
    comments: '',
    files: [],
    value: undefined,
    valueData: { input: {} },
    notApplicableType: '',
    numberScale: undefined,
    utr: new UniversalTracker({ ...defaultUniversalTrackerFields, ...utr }),
    utrv: fakeSurveyModelMinimalUtrv(utrv),
    ...props
  }

  return requiredProps;
};
describe('prepareSubmitData fn', () => {

  [
    NotApplicableTypes.na,
    NotApplicableTypes.nr,
  ].forEach((notApplicableType) => {
    it(`submit NA type: ${notApplicableType}`, () => {
      const requiredProps = createData({ notApplicableType });
      const result = prepareSubmitData(requiredProps)

      expect(Object.keys(result)).toEqual(['autoVerify', 'files', 'comments', 'editorState', 'valueData']);

      expect(result.valueData.notApplicableType).toEqual(notApplicableType);
    });

    it(`submit NA type with omission reason: ${notApplicableType}`, () => {
      const dataProvider = [
        { reason: OmissionReason.NA, comments: '' },
        { reason: OmissionReason.NA, comments: 'reason for not available' },
        { reason: OmissionReason.NRConstraints, comments: 'reason for not reporting' },
        { reason: OmissionReason.NRProhibitions, comments: 'reason for not reporting' },
        { reason: OmissionReason.NRUnavailable, comments: 'reason for not reporting' }
      ];

      dataProvider.forEach(({ reason, comments }) => {
        const requiredProps = createData({ notApplicableType, omissionReason: reason, comments });
        const result = prepareSubmitData(requiredProps)
        expect(Object.keys(result)).toEqual(['autoVerify', 'files', 'comments', 'editorState', 'valueData']);
        expect(result.valueData.notApplicableType).toEqual(notApplicableType);
        expect(result.comments).toEqual(getNotReportingNote(reason, comments));
      })
    });

    it(`submit NA type with comments and without omission reason: ${notApplicableType}`, () => {
      const dataProvider = [
        { comments: '' },
        { comments: 'comments for not reporting' },
      ];

      dataProvider.forEach(({ comments }) => {
        const requiredProps = createData({ notApplicableType, comments });
        const result = prepareSubmitData(requiredProps)
        expect(Object.keys(result)).toEqual(['autoVerify', 'files', 'comments', 'editorState', 'valueData']);
        expect(result.valueData.notApplicableType).toEqual(notApplicableType);
        expect(result.comments).toEqual(comments);
      })
    });
  });

  describe('simple value', function () {
    it('ensure numberScale is taken into affect', () => {
      const requiredProps = createData(
        { numberScale: 'billions', value: 1 },
        { numberScale: 'millions' },
      );
      const result = prepareSubmitData(requiredProps)
      expect(result.value).toEqual(1000);
    });
  });


  describe('submit table', () => {
    const firstColValue = 100;
    const secondColValue = 50;
    const rowCols = [
      {
        code: 'aaa',
        value: firstColValue,
        unit: 'h',
      },
      {
        code: 'bbb',
        value: secondColValue,
        unit: 'd',
      }
    ];
    const getTableSetup = (data: InputColumn[]): TableDataInfo => ({
      editRowId: 0,
      rows: [{ id: 0, rowStatus: RowStatus.original, data }
      ]
    });

    const tableSetup: TableDataInfo = getTableSetup(rowCols)

    const baseCol: TableColumn = {
      name: '',
      shortName: '',
      code: 'aaa',
      type: TableColumnType.Number,
      unitType: 'time',
      unit: 'h',
    };
    const tableMetadata: ValueTable = {
      columns: [
        baseCol,
        { ...baseCol, code: 'bbb', }
      ]
    };
    const requiredProps = createData(
      {
        table: tableSetup
      },
      {
        valueType: UtrValueTypes.table,
        valueValidation: { table: tableMetadata }
      }
    );
    it('should have valueData table', function () {
      const result = prepareSubmitData(requiredProps)
      expect(result.valueData.table).toBeTruthy();

      const expectedCol2ConvertedValue = secondColValue * 24; // days * hours

      // Have original value
      const { input, table } = result?.valueData ?? {};

      const [inputCol1, inputCol2] = input?.table?.[0] || []; // Original
      const [convertedCol1, convertedCol2] = table?.[0] || []; // Converted

      expect(convertedCol1.value).toEqual(firstColValue);
      expect(inputCol1.value).toEqual(firstColValue);

      expect(inputCol2.value).toEqual(secondColValue);
      expect(convertedCol2.value).toEqual(expectedCol2ConvertedValue);
    });

    it('should have correct unit/numberScale table column values for table with currency', function () {

      const aCol = { ...baseCol, unit: 'd' };
      const bCol = { ...baseCol, code: 'bbb', unit: 'h' };
      const cCol = { ...baseCol, code: 'ccc', unitType: 'currency', unit: 'USD', numberScale: 'millions' };

      const currencyColValue = 3000;
      const tableSetup: TableDataInfo = getTableSetup([...rowCols, {
        code: cCol.code,
        value: currencyColValue,
        unit: 'USD',
        numberScale: 'thousands',
      }])

      const requiredProps = createData(
        { table: tableSetup },
        {
          valueType: UtrValueTypes.table,
          valueValidation: {
            table: { columns: [aCol, bCol, cCol] }
          }
        }
      );

      const [col1, col2, col3] = tableSetup.rows[0].data;
      const result = prepareSubmitData(requiredProps);

      const { input, table } = result?.valueData ?? {};

      const [inputCol1, inputCol2, inputCol3] = input?.table?.[0] || []; // Original
      const [convertedCol1, convertedCol2, convertedCol3] = table?.[0] || []; // Converted

      // Input in hours expected in days
      expect(inputCol1.value).toEqual(firstColValue);
      expect(inputCol1.unit).toEqual(col1.unit);
      expect(convertedCol1.value).toEqual(firstColValue / 24);
      expect(convertedCol1.unit).toEqual(aCol.unit);

      // Input in days expect in hours
      expect(inputCol2.value).toEqual(secondColValue);
      expect(inputCol2.unit).toEqual(col2.unit);
      expect(convertedCol2.value).toEqual(secondColValue * 24);
      expect(convertedCol2.unit).toEqual(bCol.unit);

      // Input in thousands expect in millions
      expect(inputCol3.value).toEqual(currencyColValue);
      expect(inputCol3.numberScale).toEqual(col3.numberScale);
      expect(convertedCol3.value).toEqual(currencyColValue / 1000);
      expect(convertedCol3.numberScale).toEqual(cCol.numberScale);
      // Preserve input unit for currency, (otherwise will always do like GBP to USD)
      // @TODO this will need to change when we support currency conversions

      // For currency the unit will not be saved in valueData.input.table
      expect(convertedCol3.unit).toEqual(col3.unit);
      expect(inputCol3.unit).toEqual(undefined);
    });

    it('should have correct unit/numberScale table column values for non-currency numeric values', function () {

      const aCol = { ...baseCol, unit: 'd' };
      const bCol = { ...baseCol, code: 'bbb', unitType: 'energy', unit: 'mJ' };
      const cCol = { ...baseCol, code: 'ccc', unitType: 'mass', unit: 'mt' };

      const energyValue = 2000;
      const massValue = 3000;

      const tableSetup: TableDataInfo = getTableSetup([{
        code: aCol.code,
        value: firstColValue,
        unit: 'h',
      }, {
        code: bCol.code,
        value: energyValue,
        unit: 'kJ',
      }, {
        code: cCol.code,
        value: massValue,
        unit: cCol.unit,
        numberScale: '',
      }])

      const requiredProps = createData(
        { table: tableSetup },
        {
          valueType: UtrValueTypes.table,
          valueValidation: {
            table: { columns: [aCol, bCol, cCol] }
          }
        }
      );

      const [col1, col2, col3] = tableSetup.rows[0].data;
      const result = prepareSubmitData(requiredProps);

      const { input, table } = result?.valueData ?? {};

      const [inputCol1, inputCol2, inputCol3] = input?.table?.[0] || []; // Original
      const [convertedCol1, convertedCol2, convertedCol3] = table?.[0] || []; // Converted

      // Input in hours expected in days
      expect(inputCol1.value).toEqual(firstColValue);
      expect(inputCol1.unit).toEqual(col1.unit);
      expect(convertedCol1.value).toEqual(firstColValue / 24);
      expect(convertedCol1.unit).toEqual(aCol.unit);

      // Input in days expect in mJ - 1 mJ = 1000 kJ
      expect(inputCol2.value).toEqual(energyValue);
      expect(inputCol2.unit).toEqual(col2.unit);
      expect(convertedCol2.value).toEqual(energyValue / 1000);
      expect(convertedCol2.unit).toEqual(bCol.unit);
      // Input in thousands expect in mt
      expect(inputCol3.value).toEqual(massValue);
      expect(inputCol3.numberScale).toEqual(col3.numberScale);
      expect(convertedCol3.value).toEqual(massValue);
      expect(convertedCol3.numberScale).toEqual(cCol.numberScale);
      // For non currency numeric unit will be saved in valueData.input.table
      expect(convertedCol3.unit).toEqual(col3.unit);
      expect(inputCol3.unit).toEqual(col3.unit);
    });

    it('should have correct unit/numberScale table column values when no input unit or numberScale is provided', function () {

      const columnDefinition = { ...baseCol, code: 'bbb', unitType: 'energy', unit: 'mJ' };

      const energyValue = 2000;

      const tableSetup: TableDataInfo = getTableSetup([
        {
          code: columnDefinition.code,
          value: energyValue,
        },
      ]);

      const requiredProps = createData(
        { table: tableSetup },
        {
          valueType: UtrValueTypes.table,
          valueValidation: {
            table: { columns: [columnDefinition] }
          }
        }
      );

      const result = prepareSubmitData(requiredProps);

      const { input, table } = result.valueData ?? {};

      const [inputCol1] = input?.table?.[0] || []; // Original
      const [convertedCol1] = table?.[0] || []; // Converted

      // Save the default unit and numberScale in the input, no conversion should happen
      expect(inputCol1.value).toEqual(energyValue);
      expect(convertedCol1.value).toEqual(inputCol1.value);

      expect(inputCol1.unit).toEqual(columnDefinition.unit);
      expect(convertedCol1.unit).toEqual(columnDefinition.unit);

      expect(inputCol1.numberScale).toEqual(columnDefinition.numberScale);
      expect(convertedCol1.numberScale).toEqual(inputCol1.numberScale);

    });
  });

  describe('numericValueList type', () => {

    const valueData = {
      data: {
        aa: 10,
        bb: 20,
      }
    };
    const requiredProps = createData(
      {
        valueData,
        displayCheckbox: { aa: true, bb: true },
        unit: 'kWh',
      },
      {
        unit: 'MWh',
        unitType: 'energy',
        valueType: UtrValueTypes.numericValueList,
        valueValidation: {
          valueList: {
            type: 'list',
            list: [{ code: 'aa' }, { code: 'bb' },]
          }
        }
      },
    );

    it(`should have valueData ${UtrValueTypes.numericValueList}`, () => {
      const result = prepareSubmitData(requiredProps)
      expect(result.valueData.input.data).toBeTruthy();
    });

    it('should have valueData converted values', () => {
      const result = prepareSubmitData(requiredProps)
      expect(result.valueData.input.data).toBeTruthy();

      const { input, data } = (result?.valueData ?? {}) as ValueData<ValueDataObject>;

      const { aa, bb } = input?.data ?? {}; // Original
      const { aa: convertedAa, bb: convertedBb } = data ?? {}; // Converted

      // Check original values
      expect(aa).toEqual(valueData.data.aa);
      expect(bb).toEqual(valueData.data.bb);

      // Converted values
      expect(convertedAa).toEqual(valueData.data.aa / 1000);
      expect(convertedBb).toEqual(valueData.data.bb / 1000);
    });
  })

  describe('valueList type', function () {
    it('numericValueList type ', function () {
      const requiredProps = createData(
        { valueData: { data: 'yes' } },
        { valueType: UtrValueType.ValueList },
      );
      const result = prepareSubmitData(requiredProps)
      expect(result.valueData.data).toEqual('yes');
    });
  });

  describe('valueListMulti type', function () {
    it('can save value data ', function () {
      const requiredProps = createData(
        { valueData: { data: 'yes' } },
        { valueType: UtrValueType.ValueListMulti },
      );
      const result = prepareSubmitData(requiredProps)
      expect(result.valueData.data).toEqual('yes');
    });
  });

  describe('text/date type', function () {
    it('can update date type data', function () {
      const requiredProps = createData({ valueData: { data: '2020-10-10' } }, { valueType: UtrValueType.Text });
      const result = prepareSubmitData(requiredProps)
      expect(result.valueData.data).toEqual('2020-10-10');
    });

    it('can update text type data', function () {
      const requiredProps = createData({ valueData: { data: 'my text' } }, { valueType: UtrValueType.Text });
      const result = prepareSubmitData(requiredProps)
      expect(result.valueData.data).toEqual('my text');
    });
  });


});

describe('convertDataForSubmissions fn', () => {

  it('should distribute data to correct properties', () => {
    const evidence = {
      _id: '123',
      metadata: {
        name: 'meta name',
        mimetype: 'meta type',
        extension: '.meta',
      },
      public: false,
      url: '/',
      path: '/',
      size: 1024,
      userId: 'userId',
      created: new Date(),
      status: 'updated',
      type: 'link',
    } as ExistingEvidenceFile;

    const submitData: QuestionSubmitData = {
      autoVerify: 'true',
      comments: '',
      files: [
        {
          description: 'This is a sample file',
          title: 'sample file',
          file: new File(['sample'], 'sample.txt', {
            type: 'text/plain',
          }),
          type: 'file',
        },
        evidence,
        {
          description: 'This is a sample file 2',
          title: 'sample file 2',
          file: new File(['sample'], 'sample2.txt', {
            type: 'text/plain',
          }),
          type: 'file',
        },
      ],
      valueData: { input: {} },
    }
    const result = convertDataForSubmissions(submitData);
    expect(result.files).toHaveLength(2);
    expect(result.existingEvidence).toHaveLength(1);
  })

  it('should preserve the order of descriptions and files', () => {
    const submitData: QuestionSubmitData = {
      autoVerify: 'true',
      comments: '',
      files: [
        {
          description: 'This is a sample file 3',
          title: 'sample file 3',
          file: new File(['sample'], 'sample2.txt', {
            type: 'text/plain',
          }),
          type: 'file',
        },
        {
          description: 'This is a sample file',
          title: 'sample file',
          file: new File(['sample'], 'sample.txt', {
            type: 'text/plain',
          }),
          type: 'file',
        },
        {
          description: 'This is a sample file 2',
          title: 'sample file 2',
          file: new File(['sample'], 'sample2.txt', {
            type: 'text/plain',
          }),
          type: 'file',
        },
      ],
      valueData: { input: {} },
    }
    const result = convertDataForSubmissions(submitData);
    expect(result.files).toHaveLength(3);
    submitData.files.filter((file): file is NewEvidenceFile => 'file' in file).forEach((file, i) => {
      expect(result.files?.[i]).toEqual(file.file);
      expect(result.filesDescriptions?.[i]).toEqual(file.description);
    });
  });
})
