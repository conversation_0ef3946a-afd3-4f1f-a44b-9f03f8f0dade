/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import SDGIcon from '../../../sdg-icon/sdg-icon';
import { useSelector } from 'react-redux';
import { RootState, useAppDispatch } from '../../../../reducers';
import { loadUNSDGMap } from '../../../../actions/common';
import { UNSDGMapState } from '../../../../reducers/unsdg-map';
import { getSDGShortTitle } from '../../../../constants/sdg-data';
import { DashboardSection } from '../../../dashboard';
import { naturalSort } from '../../../../utils';
import { QUESTION } from '@constants/terminology';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

const getSDGTooltip = (code: string, UNSDGMap: UNSDGMapState) => {
  const defaultTooltip = <>SDG {code}</>;

  if (!UNSDGMap.loaded) {
    return defaultTooltip;
  }

  const goalMap = UNSDGMap.data?.goals;

  const bits = code.split('.');
  const goal = goalMap?.find((g) => g.code === bits[0]);
  if (!goal) {
    return defaultTooltip;
  }
  const target = bits.length === 2 ? goal.targets.find((t) => t.code === code) : undefined;

  const data = bits.length === 1 ? goal : target;
  if (!data) {
    return defaultTooltip;
  }

  const shortName = getSDGShortTitle(code);
  const title = `${bits.length === 1 ? 'Goal' : 'Target'} ${code}`;
  const description = data.description;

  return (
    <div className='p-2'>
      <div>{title}</div>
      <div>{shortName.toUpperCase()}</div>
      <div className='mt-3'>{description}</div>
    </div>
  );
};

const SdgIcons = ({ additionalCodes, blueprintInfo }: { additionalCodes: string[]; blueprintInfo?: string[] }) => {
  const dispatch = useAppDispatch();
  const UNSDGMap = useSelector((state: RootState) => state.UNSDGMap);

  React.useEffect(() => {
    dispatch(loadUNSDGMap());
  }, [dispatch]);

  const uniqueCodes = new Set<string>();
  if (blueprintInfo) {
    blueprintInfo
      .filter((c) => c.startsWith('sdg/') && c.includes('.'))
      .forEach((c) => uniqueCodes.add(c.replace('sdg/', '')));
  }

  if (additionalCodes?.length > 0) {
    additionalCodes.forEach((code) => {
      if (code.includes('.')) {
        uniqueCodes.add(code);
      }
    });
  }

  if (uniqueCodes.size === 0) {
    return <></>;
  }

  const codes = Array.from(uniqueCodes).sort(naturalSort);

  return (
    <DashboardSection className='sideicons-container mt-4'>
      <p className='text-ThemeTextMedium'>
        Answering this {QUESTION.SINGULAR} shows impact against the following Sustainable Development Goal (SDG)
        Targets:
      </p>
      <div className='sdg-icons'>
        <>
          {codes.map((code) => (
            <SimpleTooltip key={`${code}}`} placement={'top'} text={getSDGTooltip(code, UNSDGMap)}>
              <SDGIcon code={code} height={58} withText={true} />
            </SimpleTooltip>
          ))}
        </>
      </div>
    </DashboardSection>
  );
};

export default SdgIcons;
