import { generateInputVariationWarningMessage } from '@components/survey/utils/input';
import { FeatureStability } from '../../../../molecules/feature-stability';
import { UtrvVariationWarning } from '../../../../types/question';

export const VariationWarningMessage = ({ variationWarning }: { variationWarning: UtrvVariationWarning }) => {
  const message = generateInputVariationWarningMessage(variationWarning);

  return (
    <div className='text-ThemeDangerMedium'>
      <FeatureStability stability='beta' />
      <i className='fal fa-chart-waterfall mx-2' />
      <strong className='mr-1'>Warning:</strong>
      {message}
    </div>
  );
};
