import { useParams } from 'react-router-dom';
import { useGetTemplateByIdQuery } from '../../api/survey-templates';
import Dashboard, { DashboardSection } from '../dashboard';
import { Loader } from '@g17eco/atoms/loader';
import { TemplateDashboardHeader } from '../survey-templates/partials/TemplateDashboardHeader';
import { TemplateSettingsHeader } from '../survey-templates/partials/TemplateSettingsHeader';
import { TemplateHistoryTable } from './TemplateHistoryTable';
import { SURVEY } from '@constants/terminology';
import { BasicAlert } from '@g17eco/molecules/alert';

export const TemplateHistory = () => {
  const { templateId = '' } = useParams<{ templateId: string }>();
  const { data: currentTemplate, error } = useGetTemplateByIdQuery(templateId, { skip: !templateId });

  return (
    <Dashboard>
      <TemplateSettingsHeader />
      {!currentTemplate ? (
        <div style={{ minHeight: '150px' }}>
          <Loader />
        </div>
      ) : (
        <DashboardSection header={<TemplateDashboardHeader title={`${SURVEY.CAPITALIZED_SINGULAR} templates: ${currentTemplate.name}`} />}>
          <BasicAlert type={error ? 'danger' : 'success'}>{error?.message}</BasicAlert>
          <TemplateHistoryTable />
        </DashboardSection>
      )}
    </Dashboard>
  );
};
