/*
 * Copyright (c) 2023. Word Wide Generation Ltd
 */

import React, { useMemo, useState } from 'react';
import { TodoContainer } from './TodoContainer';
import { useGetSetupTaskQuery, useSetupTaskActionMutation } from '../../api/g17ecoApi';
import { useAppDispatch, useAppSelector } from '../../reducers';
import { getCurrentUser } from '../../selectors/user';
import { currentInitiative } from '../../selectors/initiative';
import { ActionTracker, TodoItemProps } from './TodoItem';
import { addSiteAlert, SiteAlertColors } from '../../slice/siteAlertsSlice';
import { InitiativePermissions } from '../../services/permissions/InitiativePermissions';
import { QueryWrapper } from '../query/QueryWrapper';
import { <PERSON><PERSON>, <PERSON>dal, <PERSON>dalBody, ModalFooter, ModalHeader } from 'reactstrap';
import { TodoList } from './TodoList';
import { SetupTaskAction, SetupTaskUserAction } from '../../types/setup-task';
import { useLocation } from 'react-router-dom';
import { TODO_QUERY_PARAM } from '../../constants/todo';
import { useChatbotVisibilityControl } from '@hooks/useChatbotVisibilityControl';

interface Props {
  defaultIsOpen?: boolean;
}

export const TodoWidget = (props: Props) => {

  const {
    defaultIsOpen = false,
  } = props;

  const dispatch = useAppDispatch();

  const { search } = useLocation();
  const queryParams = useMemo(() => new URLSearchParams(search), [search])
  const defaultOpen = Boolean(defaultIsOpen || queryParams.get(TODO_QUERY_PARAM));

  const [isOpen, setIsOpen] = useState(defaultOpen);
  const [actions, SetActions] = useState<ActionTracker>({});
  const [deleteId, setDeleteId] = useState('');
  const currentUser = useAppSelector(getCurrentUser);
  const initiative = useAppSelector(currentInitiative);
  const userId = currentUser?._id;
  const initiativeId = initiative?._id;

  const setupTasks = useGetSetupTaskQuery({
    initiativeId: initiativeId,
  }, { skip: !userId || !isOpen || !initiativeId });

  const [setupActionMutation] = useSetupTaskActionMutation()

  useChatbotVisibilityControl({ isHidden: isOpen });

  if (!currentUser || !InitiativePermissions.hasRoleToManage(currentUser) || !initiativeId) {
    return null;
  }

  const onAction: TodoItemProps['onAction'] = async (item, action) => {

    if (action === SetupTaskUserAction.ConfirmDelete) {
      return setDeleteId(item._id);
    } else if (action === SetupTaskAction.Delete) {
      setDeleteId('')
    }

    SetActions((c) => ({ ...c, [item._id]: action }));

    return setupActionMutation({ taskId: item._id, action, initiativeId }).unwrap()
      .then(() => setupTasks.refetch())
      .catch(e => dispatch(addSiteAlert({
        content: e.message,
        color: SiteAlertColors.Danger
      })))
      .finally(() => {
        SetActions((c) => {
          delete c[item._id]
          return ({ ...c });
        });
      })
  }

  const toggleModal = () => setDeleteId('')

  const btn = <div role='button' onClick={() => setIsOpen(!isOpen)} style={{position: 'relative', top: -2}}>
    <i className='fa-light fa-clipboard-list-check text-BrandBlue mx-2 text-xl'/>
  </div>

  if (!isOpen) {
    return btn;
  }


  return (
    <>
      {btn}
      <TodoContainer toggle={() => setIsOpen(!isOpen)}>
        <QueryWrapper
          query={setupTasks}
          onSuccess={(items) => {
            return (
              <>
                <TodoList items={items} onAction={onAction} actions={actions}/>

                {deleteId ? <Modal isOpen={true} toggle={toggleModal} backdrop='static' className='confirm-modal'>
                  <ModalHeader toggle={toggleModal}>Delete item</ModalHeader>
                  <ModalBody>
                    <p>Are you sure you want to delete this item?</p>
                  </ModalBody>
                  <ModalFooter>
                    <Button color='link-secondary' outline onClick={toggleModal}>
                      Cancel
                    </Button>
                    <Button color={'danger'} onClick={() => onAction({ _id: deleteId }, SetupTaskAction.Delete)}>
                      Delete
                    </Button>
                  </ModalFooter>
                </Modal> : null}
              </>
            )
          }}
        />
      </TodoContainer>
    </>
  );
}
