/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useCallback, useState } from 'react';
import { Button } from 'reactstrap';
import { getActual, getTarget } from '../../selectors/universal-tracker-values';
import { UtrTableView } from './UtrTableView';
import { UniversalTrackerPlain } from '../../types/universalTracker';
import { UtrTableEditWrapper } from './UtrTableEditWrapper';
import { reloadInitiative } from '../../actions/initiative';
import { isUserManager } from '../../selectors/user';
import { DashboardSection } from '../dashboard';
import { useAppDispatch, useAppSelector } from '../../reducers';
import IconButton from '../button/IconButton';
import './index.scss';

interface UniversalTrackersTableProps {
  universalTrackers: UniversalTrackerPlain[];
  header?: string;
  suppressUndefined?: boolean;
  collapsePanel?: boolean;
  rows?: number;
  className?: string;
  usage?: string;
  readOnly?: boolean;
}

export const UniversalTrackersTable = (props: UniversalTrackersTableProps) => {

  const {
    universalTrackers,
    header = 'Tracker',
    suppressUndefined,
    rows = 10,
    className = '',
    usage,
    readOnly = false,
  } = props;

  const dispatch = useAppDispatch();

  const getLastEffectiveDate = React.useCallback((universalTracker: UniversalTrackerPlain) => {
    const effectiveDate = dispatch(getActual(universalTracker, 'effectiveDate'));
    if (effectiveDate) {
      return effectiveDate
    }
    const target: any = dispatch(getTarget(universalTracker));
    return target ? target.effectiveDate : undefined;
  }, [dispatch]);

  const data = React.useMemo(
    () => suppressUndefined ? universalTrackers.filter(utr => getLastEffectiveDate(utr)) : universalTrackers,
    [suppressUndefined, universalTrackers, getLastEffectiveDate]
  );

  const [isEdit, setIsEdit] = useState(false)
  const utrv = useAppSelector((state) => state.universalTrackerValues);
  const isManager = useAppSelector(isUserManager);
  const handleReload = useCallback(() => dispatch(reloadInitiative()), [dispatch]);

  const toggleEdit = (e: React.MouseEvent<any, MouseEvent>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsEdit(!isEdit);
  };

  if (readOnly || !usage || !isManager) {
    if (data.length === 0) {
      return <></>;
    }

    return (
      <DashboardSection
        title={header}
        className='universal-tracker-table'
        padding={2}
      >
        <UtrTableView className={className} data={data} rows={rows} utrv={utrv} />
      </DashboardSection>
    )
  }

  return (
    <DashboardSection
      title={header}
      className='universal-tracker-table'
      padding={2}
      buttons={[
        !isEdit ? <IconButton tooltip={'Manage metrics'} icon={'fal fa-cog'} onClick={toggleEdit} /> : <></>
      ]}>
      {isEdit
        ?
        <>
          <UtrTableEditWrapper currentQuestions={data} handleReload={handleReload} usage={usage} />
          <div className='mt-2 text-right'>
            <Button color='primary' tooltip={'Finish configuring'} onClick={toggleEdit} >
              <i className='fa fa-check mr-2' />Done
            </Button>
          </div>
        </>
        :
        <UtrTableView className={className} data={data} rows={rows} utrv={utrv} />
      }
    </DashboardSection>
  )
}
