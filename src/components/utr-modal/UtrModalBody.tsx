/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { useState } from 'react';
import { <PERSON>ton, Collapse } from 'reactstrap';
import { ChartComponent } from './components/chart';
import UniversalTrackerModalService from '../../model/UniversalTrackerModalService';
import Dashboard, { DashboardRow } from '../dashboard';
import { canAddTarget } from '../../utils/universalTracker';
import { SetDataForm } from './components/SetDataForm';
import { UniversalTrackerPlain, UtrValueType } from '../../types/universalTracker';
import { ShowAs, UniversalTrackerModalServiceUtrv } from '../../reducers/universal-tracker-modal';
import { CollapsibleQuestionInfo } from './CollapsibleQuestionInfo';
import { AdditionalInfo } from './AdditionalInfo';
import { ContentTabs, Tab } from './ContentTabs';
import { CurrentUserData } from '../../reducers/current-user';
import { ColumnFilter } from './ColumnFilter';
import { ViewRadioButtons } from './ViewRadioButtons';
import { useGetValueListByIdQuery } from '../../api/value-list';
import { Loader } from '@g17eco/atoms/loader';
import { DISABLED_TARGET_BASELINE_ADDING_TOOLTIP } from '../../routes/custom-dashboard/utils';
import { QUESTION } from '@constants/terminology';
import { Filters, SurveyFilters, SurveyFiltersProps } from './SurveyFilters';
import { DataTable } from '../../apps/company-tracker/components/utr-modal/DataTable';
import { AddToDashboard } from '../../apps/company-tracker/components/utr-modal/AddToDashboard';
import { UtrModalActions } from '../../apps/company-tracker/components/utr-modal/UtrModalActions';
import { useToggle } from '@hooks/useToggle';

type Props = Pick<SurveyFiltersProps, 'onChangeFilters'> & {
  modalService: UniversalTrackerModalService;
  firstValueListCode?: string;
  alternativeCode?: string;
  activeTabId?: Tab['navId'];
  canAccessCustomDashboards: boolean;
  hideProvenance?: boolean;
  reloadUtrvs: (filters: Filters) => Promise<void>;
  currentUser?: CurrentUserData;
  canManage: boolean;
  disableTargetBaselineAdding?: boolean;
  initialFilters: Filters;
  isOpenFilterByDefault?: boolean;
};

function getErrorMessage(utr: Pick<UniversalTrackerPlain, 'valueType'>) {
  if (utr.valueType === UtrValueType.Table) {
    return `Currently targets/baselines are not supported for multi-row table ${QUESTION.PLURAL}`;
  }
  return 'Currently targets/baselines are not supported for this metric type';
}

export const UtrModalBody = (props: Props) => {
  const {
    modalService,
    firstValueListCode,
    alternativeCode,
    activeTabId,
    canAccessCustomDashboards,
    hideProvenance,
    reloadUtrvs,
    currentUser,
    canManage,
    disableTargetBaselineAdding = false,
    initialFilters,
    onChangeFilters,
    isOpenFilterByDefault,
  } = props;

  const [selectedColumnCode, setColumnCode] = useState<undefined | string>(firstValueListCode);

  const utr = modalService.getUniversalTracker();
  const utrvs = modalService.getUniversalTrackerValues();
  const utrPlain: UniversalTrackerPlain = utr.getRaw();
  const { data: valueList, isFetching } = useGetValueListByIdQuery(utrPlain.valueValidation?.valueList?.listId ?? '', {
    skip: !utrPlain.valueValidation?.valueList?.listId,
  });

  // Add list options to numericValueList question to show in adding target or baseline.
  if (utr.getValueType() === UtrValueType.NumericValueList) {
    const utrValueList = utr.getValueValidation().valueList;
    if (utrValueList && !utrValueList.list) {
      utr.overrideValueValidation({ valueList: { ...utrValueList, list: valueList?.options } });
    }
  }

  const canDrawChart = canAddTarget(utrPlain);

  const [activeTab, setActiveTab] = useState<Tab['navId']>(() => {
    return activeTabId ? activeTabId : canDrawChart ? ShowAs.Chart : ShowAs.Table;
  });
  const [showFilters, toggle] = useToggle(isOpenFilterByDefault ?? utr.getValueType() === UtrValueType.Table);
  const [selectedUtrv, setSelectedUtrv] = useState<UniversalTrackerModalServiceUtrv | undefined>(undefined);

  const handleSelectUtrv = (selectedUtrv: UniversalTrackerModalServiceUtrv, navId: Tab['navId']) => {
    setSelectedUtrv(selectedUtrv);
    setActiveTab(navId);
  };
  const handleChangeTab = (navId: Tab['navId']) => {
    setSelectedUtrv(undefined);
    setActiveTab(navId);
  };

  const initiativeId = modalService.getInitiativeId() ?? '';

  const handleBack = () => setActiveTab(ShowAs.Table);

  const [filters, setFilters] = useState(initialFilters);

  const handleChangeFilters = (newFilters: Filters) => {
    setFilters(newFilters);
    onChangeFilters(newFilters);
  };

  const handleReloadUtrvs = () => reloadUtrvs(filters);

  if (isFetching) {
    return <Loader />;
  }

  const viewTabs: Tab[] = [
    {
      navId: ShowAs.Chart,
      disabled: !canDrawChart,
      name: 'Chart',
      component: <ChartComponent utr={utr} utrvs={utrvs} selectedColumnCode={selectedColumnCode} />,
    },
    {
      navId: ShowAs.Table,
      name: 'Table',
      component: (
        <DashboardRow mb={0}>
          <DataTable
            selectedColumnCode={selectedColumnCode}
            utr={utr}
            utrvs={utrvs}
            hideProvenance={hideProvenance}
            reloadUtrvs={handleReloadUtrvs}
            currentUser={currentUser}
            users={modalService.users}
            documents={modalService.documents}
            handleSelectUtrv={handleSelectUtrv}
          />
        </DashboardRow>
      ),
    },
  ];

  const getAddDataTabs = () => {
    const tabs = [];
    if (canManage) {
      const targetBaselineTab = {
        navId: ShowAs.TargetBaseline,
        name: 'Target Baseline',
        disabled: disableTargetBaselineAdding,
        component: (
          <SetDataForm
            utr={utr}
            selectedUtrv={selectedUtrv}
            utrvs={utrvs}
            initiativeId={initiativeId}
            userId={currentUser?._id}
            handleBack={handleBack}
            reloadUtrvs={handleReloadUtrvs}
          />
        ),
      };
      tabs.push(targetBaselineTab);
    }

    if (canAccessCustomDashboards) {
      tabs.push({
        navId: ShowAs.AddToDashboard,
        name: 'Add to dashboard',
        component: (
          <AddToDashboard
            initiativeId={initiativeId}
            utr={utr}
            selectedColumnCode={selectedColumnCode}
            alternativeCode={alternativeCode}
          />
        ),
      });
    }
    return tabs;
  };

  const addDataTabs: Tab[] = getAddDataTabs();

  const tabs = [...viewTabs, ...addDataTabs];

  const isAddDataMode = [ShowAs.TargetBaseline, ShowAs.AddToDashboard].includes(activeTab);

  const renderBreadcrumbs = () => {
    const breadcrumbs = [
      <Button key='root' color='link' onClick={handleBack}>
        Metric information
      </Button>,
    ];

    breadcrumbs.push(
      <div key='divider' className='mx-1'>
        &#47;
      </div>
    );

    if (activeTab === ShowAs.TargetBaseline) {
      breadcrumbs.push(
        selectedUtrv ? (
          <div key='edit-target-baseline' className='ms-1'>
            Edit target or baseline
          </div>
        ) : (
          <div key='add-target-baseline' className='ms-1'>
            Add target or baseline
          </div>
        )
      );
    }

    if (activeTab === ShowAs.AddToDashboard) {
      breadcrumbs.push(
        <div key='dashboard' className='ms-1'>
          Add to dashboard
        </div>
      );
    }

    return breadcrumbs;
  };

  return (
    <Dashboard className='utr-modal-container'>
      <CollapsibleQuestionInfo utr={utr} alternativeCode={alternativeCode} />

      <DashboardRow>
        <div className='w-100'>
          {isAddDataMode ? (
            <div className='d-flex'>{renderBreadcrumbs()}</div>
          ) : (
            <div>
              <div className='w-100 d-flex justify-content-between align-items-center gap-2'>
                <ViewRadioButtons tabs={viewTabs} activeTab={activeTab} handleChangeTab={setActiveTab} />
                <Button color='transparent' onClick={toggle} className='ms-2'>
                  Filters <i className={showFilters ? 'fal fa-caret-up' : 'fal fa-caret-down'} />
                </Button>
                {canManage ? (
                  <UtrModalActions
                    initiativeId={initiativeId}
                    utrId={utr.getId()}
                    disabledTargetBaselineAddingText={
                      disableTargetBaselineAdding
                        ? DISABLED_TARGET_BASELINE_ADDING_TOOLTIP
                        : canDrawChart
                        ? ''
                        : getErrorMessage(utrPlain)
                    }
                    canAccessCustomDashboards={canAccessCustomDashboards}
                    canDrawChart={canDrawChart}
                    handleChangeTab={handleChangeTab}
                  />
                ) : null}
              </div>

              <Collapse isOpen={showFilters}>
                <div className='mt-1 row gx-2 gy-2'>
                  <ColumnFilter
                    utr={utr}
                    selectedColumnCode={selectedColumnCode}
                    valueList={valueList}
                    handleChangeColumn={setColumnCode}
                  />
                  <SurveyFilters
                    key={`${filters.surveyType}-${filters.period}`}
                    filters={filters}
                    onChangeFilters={handleChangeFilters}
                  />
                </div>
              </Collapse>
            </div>
          )}
        </div>
      </DashboardRow>

      <ContentTabs tabs={tabs} activeTab={activeTab} />

      {isAddDataMode ? null : <AdditionalInfo utrPlain={utrPlain} />}
    </Dashboard>
  );
};
