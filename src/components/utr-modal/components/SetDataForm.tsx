/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { useMemo, useState } from 'react';
import { Button } from 'reactstrap';
import { DashboardRow } from '../../dashboard';
import { BlockingLoader , Loader } from '@g17eco/atoms/loader';
import { QuestionInput } from '../../question/QuestionInput';
import { UtrvStatus, UtrvType } from '../../../constants/status';
import {
  getCurrentStartOfTheDayDate,
  getDate,
  getMonth,
  getMonthNonJSOptions,
  getPeriodOptions,
  getYear,
  getYearDropdownOptions,
  isSame,
} from '../../../utils/date';
import { isDataEmpty } from '../../survey/question/questionUtil';
import { InitialResettableQuestionState, RowStatus, ValueData } from '../../survey/question/questionInterfaces';
import UniversalTrackerValue, { NewUniversalTrackerValuePlain } from '../../../model/UniversalTrackerValue';
import { unload } from '../../../slice/utrvChartDataSlice';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { createUtrvByModalService } from '../../../actions/universalTrackerModal';
import { UniversalTrackerPlain, UtrValueType, TableColumnType, DataPeriods } from '@g17eco/types/universalTracker';
import { ColumnInputProps } from '../../survey/form/input/table/InputInterface';
import { BaseInputProps } from '../../survey/form/input/InputProps';
import { canAddTarget } from '../../../utils/universalTracker';
import UniversalTracker from '../../../model/UniversalTracker';
import { RadioInput } from '@g17eco/molecules/form';
import { UniversalTrackerModalServiceUtrv } from '@reducers/universal-tracker-modal';
import { useUpdateTargetBaselineMutation } from '@api/utrv';
import { useHasValueChanged } from '../../survey/question/useHasChanged';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { BasicAlert } from '@g17eco/molecules/alert';
import { getEffectiveDate } from '@utils/survey';
import { useAppDispatch } from '@reducers/index';

enum ErrorMessage {
  DateConflict = 'Date conflict error: please check the date or period and try again.'
}

export interface DataFormState {
  comments?: undefined;
  year?: undefined;
  saved: boolean;
  type: UtrvType;
  message?: string;
  saving: boolean;
  month?: undefined;
  action?: string;
  files: any[];
  value?: string | number | boolean;
  valueData?: ValueData;
  sampleSize?: number | boolean;
  effectiveDate?: string;
  errored: boolean;
  period?: DataPeriods;
}

interface SetDataFormProps {
  handleBack?: () => void;
  utr: UniversalTracker;
  initiativeId?: string;
  userId?: string;
  reloadUtrvs: () => Promise<void>;
  selectedUtrv?: UniversalTrackerModalServiceUtrv;
  utrvs: UniversalTrackerModalServiceUtrv[];
}

interface UtrvData {
  type: UtrvType;
  month: number | undefined;
  year: number | undefined;
  period: DataPeriods | undefined;
}

const typeOptions = [
  { code: UtrvType.Target, name: 'Target' },
  { code: UtrvType.Baseline, name: 'Baseline' },
]

const getExistingUtrvSettingsMap = (
  utrvs: UniversalTrackerModalServiceUtrv[],
  excludeDate: { [key: string]: string }
) => {
  return utrvs.reduce((acc, { type, effectiveDate, period = DataPeriods.Yearly }) => {
    if (type === UtrvType.Actual || isSame(excludeDate[type], effectiveDate)) {
      return acc;
    }

    const effectiveDates = acc.get(type);

    if (!effectiveDates) {
      acc.set(type, new Set([`${effectiveDate}-${period}`]));
    }
    effectiveDates?.add(`${effectiveDate}-${period}`);

    return acc;
  }, new Map() as Map<string, Set<string>>);
};

const getInitialState = (selectedUtrv?: UniversalTrackerModalServiceUtrv) => {
  if (!selectedUtrv) {
    return { type: UtrvType.Target, month: undefined, year: undefined, period: undefined };
  }
  return {
    type: selectedUtrv.type as UtrvType,
    month: getMonth(selectedUtrv.effectiveDate, true) + 1,
    year: getYear(selectedUtrv.effectiveDate, true),
    period: selectedUtrv.period,
  };
};

const checkIsValidFormData = (utr: UniversalTracker, formData: InitialResettableQuestionState) => {
  const { value, valueData = {}, displayCheckbox, table } = formData;
  return !isDataEmpty({ utr, table, value, valueData, displayCheckbox });
};

export const SetDataForm = (props: SetDataFormProps) => {
  const { utr, selectedUtrv, handleBack, utrvs, initiativeId, userId, reloadUtrvs } = props;

  const dispatch = useAppDispatch();
  const [utrvData, setUtrvData] = useState<UtrvData>(getInitialState(selectedUtrv));
  const [formData, setFormData] = useState<InitialResettableQuestionState>();
  const [isSaving, setSaving] = useState(false);
  const [updateTargetBaseline, { isLoading }] = useUpdateTargetBaselineMutation();
  const { addSiteError } = useSiteAlert();

  const { type, month, year, period } = utrvData;

  const utrvSettingsMap = getExistingUtrvSettingsMap(utrvs, selectedUtrv ? { [type]: selectedUtrv.effectiveDate } : {});
  const hasSelectedDate = month && year;
  const currentEffectiveDate = hasSelectedDate ? getEffectiveDate(month, year) : '';
  const hasConflict = !!utrvSettingsMap.get(type)?.has(`${currentEffectiveDate}-${period}`);

  const isValidSettings = hasSelectedDate && period && !hasConflict;

  const convertedFormData = useMemo(() => {
    if (!formData) {
      return {} as InitialResettableQuestionState;
    }
    formData.table = {
      editRowId: -1,
      rows: [{ id: 0, rowStatus: RowStatus.original, data: formData.valueData.table?.[0] ?? [] }],
    };
    return formData;
  }, [formData]);

  const isValidFormData = checkIsValidFormData(utr, convertedFormData);

  const hasValueChanged = useHasValueChanged(convertedFormData, { utrv: selectedUtrv, utr });

  const checkHasAnythingChangedOnUpdate = () => {
    // if create new one, no need to detect changes
    if (!selectedUtrv) {
      return true;
    }
    if (!isSame(selectedUtrv.effectiveDate, currentEffectiveDate)) {
      return true;
    }
    if (selectedUtrv.type !== utrvData.type) {
      return true;
    }
    if (selectedUtrv.period !== utrvData.period) {
      return true;
    }
    return hasValueChanged;
  };

  const hasAnythingChangedOnUpdate = checkHasAnythingChangedOnUpdate();

  const canSubmit = isValidSettings && isValidFormData && hasAnythingChangedOnUpdate;

  const monthOptions = getMonthNonJSOptions();
  const yearOptions = getYearDropdownOptions(5, type === UtrvType.Target ? 10 : 0);
  const periodOptions = getPeriodOptions();

  const newUtrv = useMemo(() => {
    if (!initiativeId || !userId) {
      return;
    }

    return new UniversalTrackerValue({
      status: UtrvStatus.Created,
      effectiveDate: getCurrentStartOfTheDayDate('day').toISOString(),
      sourceType: 'manual',
      type: UtrvType.Target,
      history: [],
      stakeholders: {
        stakeholder: [userId],
        verifier: [],
      },
      evidenceRequired: false,
      verificationRequired: false,
      lastUpdated: getDate().toISOString(),
      initiativeId,
      universalTrackerId: utr.getId(),
      _new: true
    });
  }, [initiativeId, userId, utr]);

  if (!newUtrv) {
    return <BlockingLoader />;
  }

  const updateUtrv = (key: string, value: UtrvType | DataPeriods | number | undefined) => {
    setUtrvData((prev) => ({ ...prev, [key]: value }));
  };

  const onAdd = (newData: DataFormState) => {
    const universalTrackerValue = newUtrv.getUniversalTrackerValue();
    return dispatch(createUtrvByModalService(newData, universalTrackerValue as NewUniversalTrackerValuePlain))
  };
  const onUpdate = (newData: DataFormState) => {
    if(!selectedUtrv) {
      return;
    }
    return updateTargetBaseline({ ...newData, type: utrvData.type, utrvId: selectedUtrv._id });
  }
  const handleSubmit = async () => {
    if (!formData || isSaving || !canSubmit) {
      return false;
    }

    setSaving(true);

    const newData: DataFormState = {
      ...formData,
      comments: undefined,
      errored: false,
      saved: false,
      type,
      effectiveDate: currentEffectiveDate,
      period,
    }

    const submitFn = selectedUtrv ? onUpdate : onAdd;

    submitFn(newData)
      ?.then(async () => {
        dispatch(unload());
        await reloadUtrvs();
        handleBack?.();
      })
      .catch((e) => {
        addSiteError(e);
      })
      .finally(() => setSaving(false));
  }

  const isDisabled = (props?: Partial<BaseInputProps> | Partial<ColumnInputProps>): boolean => {
    if (!props?.universalTracker) {
      return true;
    }

    const utrPlain: UniversalTrackerPlain = props.universalTracker.getRaw();
    if (utrPlain.valueType === UtrValueType.Table) {
      if ('column' in props) {
        const validColumnTypes = [TableColumnType.Number, TableColumnType.Percentage];
        if (props.column?.type && validColumnTypes.includes(props?.column?.type)) {
          return false;
        }
      }
      return true;
    }

    return !canAddTarget(utrPlain);
  }

  return (
    <>
      <DashboardRow className='px-4' mb={0}>
        {isLoading ? <Loader /> : null}
        <BasicAlert hide={!hasConflict} type={'danger'} className='w-100'>
          {ErrorMessage.DateConflict}
        </BasicAlert>
      </DashboardRow>
      <DashboardRow>
        <div className='p-2 d-flex flex-column flex-sm-row w-100 gap-2'>
          <SelectFactory
            selectType={SelectTypes.SingleSelect}
            placeholder={'Select month'}
            className='w-100'
            options={monthOptions}
            value={monthOptions.find(op => op.value === month)}
            onChange={(op) => updateUtrv('month', op?.value)}
          />
          <SelectFactory<number>
            selectType={SelectTypes.SingleSelect}
            placeholder={'Select year'}
            className='w-100'
            options={yearOptions}
            value={yearOptions.find(op => op.value === year)}
            onChange={(op) => updateUtrv('year', op?.value)}
          />
          <SelectFactory
            selectType={SelectTypes.SingleSelect}
            placeholder={'Select period'}
            className='w-100'
            options={periodOptions}
            value={periodOptions.find(op => op.value === period)}
            onChange={(op) => updateUtrv('period', op?.value)}
          />
        </div>
      </DashboardRow>

      <DashboardRow mb={0}>
        <div className='d-flex w-100 gap-3 px-2'>
          <div className='text-uppercase text-medium'>Value type:</div>
          <RadioInput
            groupCode='value-type'
            value={type}
            options={typeOptions}
            onChange={({ value }) => updateUtrv('type', value)}
            className='d-flex gap-3'
          />
        </div>
      </DashboardRow>
      <hr className='mx-4 mt-0' />
      <DashboardRow>
        <div className='w-100 p-2'>
          <QuestionInput
            utrv={selectedUtrv ?? newUtrv.universalTrackerValue}
            utr={utr}
            handleUpdate={setFormData}
            isDisabled={isDisabled}
            displayCheckbox={formData?.displayCheckbox}
          />
        </div>
      </DashboardRow>

      <DashboardRow>
        <div className='flex-fill text-right'>
          {handleBack ? (
            <Button color='link-secondary' onClick={handleBack} className='mr-3'>
              Cancel
            </Button>
          ) : null}
          <Button disabled={!canSubmit} color='primary' onClick={handleSubmit}>
            Submit
          </Button>
        </div>
      </DashboardRow>
    </>
  );
}
