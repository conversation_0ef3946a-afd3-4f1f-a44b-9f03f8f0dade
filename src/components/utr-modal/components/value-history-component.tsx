/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { PureComponent } from 'react';
import { ConnectedProps, connect } from 'react-redux';
import { Loader } from '@g17eco/atoms/loader';
import { loadValueDetails, unloadValueDetails } from '../../../actions/universalTracker';
import { HistoryTimelineComponent } from '../../value-history/HistoryTimelineComponent';
import UniversalTracker from '../../../model/UniversalTracker';
import { RootState } from '../../../reducers';
import { ExistingEvidenceFile } from '../../survey/question/questionInterfaces';
import { UserMin } from '../../../constants/users';

export interface ValueHistoryProps {
  utr: UniversalTracker;
  utrValueId: string;
  users?: UserMin[];
  documents?: ExistingEvidenceFile[];
  isVisible: boolean;
}

class ValueHistoryComponent extends PureComponent<ValueHistoryProps & PropsFromRedux> {
  componentWillUnmount() {
    const { isVisible, utrValueDetailsState } = this.props;
    if (utrValueDetailsState.loaded && !isVisible) {
      this.props.unloadValueDetails();
    }
  }

  componentDidMount() {
    const { utrValueId, utr } = this.props;

    this.props.loadValueDetails(utr.getId(), utrValueId);
  }

  render() {
    const { utrValueDetailsState, users: utrUsers = [], documents: utrDocuments = [], utr: propUtr } = this.props;

    if (!utrValueDetailsState.loaded) {
      return <Loader />;
    }

    const { history, users: utrvUsers, universalTracker, documents: utrvDetailDocs, surveyType, sourceItems } = utrValueDetailsState.data;

    const users = utrUsers.length > 0 ? utrUsers : utrvUsers;
    const docs = utrvDetailDocs || [];
    const documents = utrDocuments.length > 0 ? utrDocuments : docs;

    // Reload utr with metric unit
    const utr = universalTracker ? universalTracker[0] : undefined;

    const modalUtr = utr ? new UniversalTracker(utr) : propUtr;

    return (
      <HistoryTimelineComponent
        universalTracker={modalUtr}
        history={history}
        users={users}
        documents={documents}
        surveyType={surveyType}
        sourceItems={sourceItems}
      />
    );
  }
}

const mapStateToProps = (state: RootState) => ({
  utrValueDetailsState: state.universalTrackerValueDetails,
});

const mapDispatchToProps = {
  loadValueDetails,
  unloadValueDetails,
};

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;
export default connector(ValueHistoryComponent);
