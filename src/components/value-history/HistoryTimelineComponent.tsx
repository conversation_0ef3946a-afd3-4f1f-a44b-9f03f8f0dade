/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { PureComponent } from 'react';
import { Button } from 'reactstrap';
import UniversalTrackerValue from '../../model/UniversalTrackerValue';
import { formatLongDate } from '../../utils/date';
import { UTR } from '../../constants/labels';
import { EvidenceModal } from '../files/EvidenceModal';
import { ValueHistoryText } from './ValueHistoryText';
import { setCurrentHistoryData } from '../../actions/api'
import { Loader } from '@g17eco/atoms/loader';
import config from '../../config';
import { UtrvAssuranceStatus, ValueHistory } from '../../types/universalTrackerValue';
import { HistoryDocument } from '../../types/document';
import { UserMin } from '../../constants/users';
import UniversalTracker from '../../model/UniversalTracker';
import { BasicAlert } from '@g17eco/molecules/alert';
import { PartialAssuranceIcon } from '@g17eco/molecules/partial-assurance';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import './history-timeline.scss';
import { sortByDateAndId } from '@utils/sort';
import { SurveyType } from '@g17eco/types/survey';
import { SourceItem } from '@g17eco/types/surveyScope';
import { isAggregatedSurvey } from '@utils/survey';
import { isUtrvAssuranceRejected, isUtrvAssuranceRestated } from '../../utils/universalTrackerValue';

interface Props {
  universalTracker: UniversalTracker
  history: ValueHistory[];
  documents: HistoryDocument[];
  users: UserMin[];
  surveyType?: SurveyType;
  isComposite?: boolean;
  isStakeholder?: boolean;
  displayEvidence: true,
  displayNote?: boolean,
  isFragment?: boolean,
  sourceItems?: SourceItem[];
  reload?: () => void;
  utrvId?: string;
  isReadOnly?: boolean;
}

interface State {
  errorMessage?: string;
  loading: boolean
  displaySupportingDocuments: boolean,
  displayLocationInfo: boolean | undefined | number,
  history: ValueHistory[],
  lastHistory?: ValueHistory,
}

export class HistoryTimelineComponent extends PureComponent<Props, State> {

  private userMap = new Map()

  static defaultProps = {
    history: [],
    users: [],
    documents: [],
    displayEvidence: true,
    displayNote: true,
    isStakeholder: false,
    isFragment: false,
    reload: () => { },
  };

  state: State = {
    lastHistory: undefined,
    displaySupportingDocuments: false,
    displayLocationInfo: false,
    history: [],
    loading: false
  };

  componentDidMount() {
    this.props.users.forEach(u => this.userMap.set(u._id, u));
    const sorted = [...this.props.history].sort(sortByDateAndId);

    this.setState({ history: sorted });
  }

  toggleSupportingDocuments = (e: React.MouseEvent, history: ValueHistory) => {
    e.preventDefault();
    this.setState({
      displaySupportingDocuments: !this.state.displaySupportingDocuments,
      lastHistory: history
    });
  };

  toggleLocationInfo = (e: React.MouseEvent, id: number | undefined = undefined) => {
    e.preventDefault();
    this.setState({
      displayLocationInfo: id
    });
  };

  triggerHistoryUpdate = (e: React.MouseEvent, h: ValueHistory) => {
    e.preventDefault();
    if (!this.props.utrvId) {
      return;
    }
    this.setState({ loading: true })

    setCurrentHistoryData(this.props.utrvId, h).then(() => {
      this.setState({ loading: false })
      this.props.reload?.()
    }).catch((e) => this.setState({
      loading: false,
      errorMessage: e.message
    }))
  }

  renderHistoryIcon(h: ValueHistory) {
    if (h.assuranceStatus === UtrvAssuranceStatus.Restated) {
      return <i className='fa fa-rotate' />
    }
    if (h.assuranceStatus === UtrvAssuranceStatus.Completed) {
      return <i className='fa fa-award fs-5' />
    }
    if (h.assuranceStatus === UtrvAssuranceStatus.Partial) {
      return <PartialAssuranceIcon height='17px' />
    }
    return <i className='fa fa-circle' />
  }

  render() {
    const { documents, displayEvidence, displayNote, isStakeholder, isReadOnly = false } = this.props;
    const { history } = this.state;

    if (history.length === 0) {
      return 'No History available';
    }
    if (this.state.loading) {
      return <Loader />
    }

    return (
      <div className='timeline-wrapper'>
        <div className='timeline'>
          {this.state.errorMessage ? <BasicAlert type={'danger'} >{this.state.errorMessage}</BasicAlert > : ''}
          {
            history.map((h, i) => {
              const statusText = UniversalTrackerValue.getStatusTextByHistory(h);

              const isAssuranceRestated = isUtrvAssuranceRestated(h.assuranceStatus);
              const isAssuranceRejected = isUtrvAssuranceRejected(h.assuranceStatus);
              const useAssuranceStatus = isAssuranceRestated || isAssuranceRejected;
              const className = useAssuranceStatus ? `${h.assuranceStatus}` : statusText.toLowerCase();

              const showEvidence = displayEvidence && h.evidence && h.evidence.length > 0;
              const showNote = displayNote && h.note;
              const text = this.getText(statusText, className, h);
              if (text === undefined) {
                return '';
              }
              return (
                <div className='row' key={i}>
                  <div className='col-12 col-md-3'>
                    <span className={`${className} mr-1`}>
                      {this.renderHistoryIcon(h)}
                    </span>
                    {formatLongDate(h.date)}
                    {i !== 0 && this.props.isFragment && h.action === 'updated' && !isAssuranceRestated && isStakeholder && !isReadOnly
                      ?
                      (
                        <SimpleTooltip text={'Click to undo action'}>
                          <i onClick={(e) => this.triggerHistoryUpdate(e, h)} className={'fas fa-undo ml-2 text-ThemeAccentMedium'} />
                        </SimpleTooltip>
                      )
                      : <></>
                    }
                  </div>
                  <div className='col'>
                    {text}
                    {showEvidence && this.renderHistoryDocuments(h)}
                    {this.renderLocationInfo(h, i)}
                    {showNote &&
                      <div className='historyDetails'>
                        <strong>{UTR.NOTE}:</strong>&nbsp;<span className='dont_translate'>{h.note}</span>
                      </div>
                    }
                    {h.valueData?.isImported === true
                      ?
                      <BasicAlert type='info'>
                        <i className='fa fa-exclamation-triangle mr-2' />This data was imported from outside the platform and may not have been validated.
                        </BasicAlert>
                      :
                      <></>
                    }
                  </div>
                </div>
              )
            })
          }
        </div>
        {
          displayEvidence &&
          <EvidenceModal
            history={this.state.lastHistory}
            isOpen={this.state.displaySupportingDocuments}
            documents={documents}
            toggle={this.toggleSupportingDocuments}
          />
        }
      </div >
    );
  }

  renderLocationInfo = (history: ValueHistory, i: number) => {
    if (!history.location || !history.location.latitude || !history.location.longitude) {
      return;
    }

    const { displayLocationInfo } = this.state;
    if (displayLocationInfo === i) {
      const latLong = `${history.location.latitude},${history.location.longitude}`;
      return <div className='mapContainer'>
        <div className='d-flex justify-content-between'>
          <Button className='closeButton' color='danger' onClick={(e) => this.toggleLocationInfo(e, undefined)}  ><i className='fa fas fa-times' /></Button>
          <a href={`https://maps.google.com/?q=${latLong}`} target='_blank' rel='noopener noreferrer' className='align-self-end'><small>View in Google maps</small><i className='fas fa-external-link-alt ml-2'></i></a>
        </div>
        <img alt='Geo location'
          src={`https://maps.googleapis.com/maps/api/staticmap?zoom=16&key=${config.googleMapsKey}&size=600x300&markers=${latLong}`} />
      </div>
    }

    return <div className='link-like' onClick={(e) => this.toggleLocationInfo(e, i)}>
      <i className='fa-light fa-location-dot text-ThemeAccentMedium text-sm mr-1' />View location info
    </div>
  }

  getText(statusText: string, className: string, h: ValueHistory) {
    const { displayEvidence, displayNote, universalTracker } = this.props;

    const userName = this.getName(h.userId);

    if (this.props.isComposite) {
      const text = ValueHistoryText.getCompositeHistoryText(className, statusText, h, universalTracker);

      const showEvidence = displayEvidence && h.evidence && h.evidence.length > 0;
      const showNote = displayNote && h.note;
      if (text === undefined && (showEvidence || showNote)) {
        return 'Supporting information provided';
      }

      return text;
    }

    return ValueHistoryText.getHistoryText(className, statusText, h, userName, universalTracker);
  }

  renderHistoryDocuments(history: ValueHistory) {
    const count = history.evidence?.length ?? 0;
    const text = `View ${count} supporting file${count > 1 ? 's' : ''}`;

    return (
      <div className='historyDetails'>
        <span className={'link-like'} onClick={(e) => this.toggleSupportingDocuments(e, history)}>
          <i className='fa-light fa-file text-ThemeAccentMedium text-sm' />{text}
        </span>
      </div>
    );
  }

  getName(userId: string) {
    const { surveyType, sourceItems } = this.props;
    const hasAggregatedData = sourceItems && sourceItems.length > 1;

    if (hasAggregatedData || isAggregatedSurvey(surveyType)) {
      return 'aggregated combined report'
    }

    const user = this.userMap.get(userId);
    return user ? `${user.firstName} ${user.surname}` : '- -'
  }
}

