import { renderHook } from '@testing-library/react';
import { useRef } from 'react';
import { useExpandableContent } from '../useExpandableContent';
import { vi } from 'vitest';

// Mock DOM element with scrollHeight
const createMockElement = (scrollHeight: number) => ({
  scrollHeight,
  current: {
    scrollHeight,
  } as HTMLElement,
});

describe('useExpandableContent', () => {
  beforeEach(() => {
    // Clear any existing DOM queries
    document.querySelector = vi.fn();
  });

  it('should work with elementRef and detect truncation', () => {
    const mockRef = createMockElement(150);
    
    const { result } = renderHook(() => 
      useExpandableContent({
        elementRef: mockRef as any,
        defaultHeight: 90,
        isHidden: true,
      })
    );

    expect(result.current.isTruncated).toBe(true);
    expect(result.current.height).toBe('90px');
    expect(result.current.className).toBe('is-truncated collapsed');
  });

  it('should work with elementRef when expanded', () => {
    const mockRef = createMockElement(150);
    
    const { result } = renderHook(() => 
      useExpandableContent({
        elementRef: mockRef as any,
        defaultHeight: 90,
        isHidden: false,
      })
    );

    expect(result.current.isTruncated).toBe(true);
    expect(result.current.height).toBe('150px');
    expect(result.current.className).toBe('is-truncated expanded');
  });

  it('should work with provided height (legacy useTextCollapse behavior)', () => {
    const { result } = renderHook(() => 
      useExpandableContent({
        height: 120,
        defaultHeight: 55,
        isHidden: true,
      })
    );

    expect(result.current.isTruncated).toBe(true);
    expect(result.current.height).toBe('55px');
    expect(result.current.className).toBe('is-truncated collapsed');
  });

  it('should not truncate when content is smaller than default height', () => {
    const mockRef = createMockElement(40);
    
    const { result } = renderHook(() => 
      useExpandableContent({
        elementRef: mockRef as any,
        defaultHeight: 90,
        isHidden: true,
      })
    );

    expect(result.current.isTruncated).toBe(false);
    expect(result.current.height).toBe('100%');
    expect(result.current.className).toBe('');
  });

  it('should fallback to selector for backward compatibility', () => {
    const mockElement = { scrollHeight: 120 };
    (document.querySelector as any).mockReturnValue(mockElement);
    
    const { result } = renderHook(() => 
      useExpandableContent({
        selector: '.test-selector',
        defaultHeight: 90,
        isHidden: true,
      })
    );

    expect(document.querySelector).toHaveBeenCalledWith('.test-selector');
    expect(result.current.isTruncated).toBe(true);
    expect(result.current.height).toBe('90px');
    expect(result.current.className).toBe('is-truncated collapsed');
  });
});
