import { renderHook } from '@testing-library/react';
import { useTextCollapse } from '../useTextCollapse';

// Mock DOM element with scrollHeight
const createMockElement = (scrollHeight: number) => ({
  current: {
    scrollHeight,
  } as HTMLElement,
});

describe('useTextCollapse', () => {
  it('should work with elementRef and detect truncation', () => {
    const mockRef = createMockElement(150);
    
    const { result } = renderHook(() => 
      useTextCollapse({
        elementRef: mockRef as any,
        defaultHeight: 90,
        isHidden: true,
      })
    );

    expect(result.current.isTruncated).toBe(true);
    expect(result.current.height).toBe('90px');
    expect(result.current.className).toBe('is-truncated collapsed');
  });

  it('should work with elementRef when expanded', () => {
    const mockRef = createMockElement(150);
    
    const { result } = renderHook(() => 
      useTextCollapse({
        elementRef: mockRef as any,
        defaultHeight: 90,
        isHidden: false,
      })
    );

    expect(result.current.isTruncated).toBe(true);
    expect(result.current.height).toBe('150px');
    expect(result.current.className).toBe('is-truncated expanded');
  });

  it('should not truncate when content is smaller than default height', () => {
    const mockRef = createMockElement(40);
    
    const { result } = renderHook(() => 
      useTextCollapse({
        elementRef: mockRef as any,
        defaultHeight: 90,
        isHidden: true,
      })
    );

    expect(result.current.isTruncated).toBe(false);
    expect(result.current.height).toBe('100%');
    expect(result.current.className).toBe('');
  });

  it('should handle missing ref gracefully', () => {
    const mockRef = { current: null };
    
    const { result } = renderHook(() => 
      useTextCollapse({
        elementRef: mockRef as any,
        defaultHeight: 90,
        isHidden: true,
      })
    );

    expect(result.current.isTruncated).toBe(false);
    expect(result.current.height).toBe('100%');
    expect(result.current.className).toBe('');
  });
});
