import { RefObject, useLayoutEffect, useState } from 'react';

interface UseExpandableContentProps {
  elementRef?: RefObject<HTMLElement>;
  selector?: string;
  height?: number;
  defaultHeight?: number;
  isHidden: boolean;
}

const DEFAULT_HEIGHT = 90;

export const useExpandableContent = ({
  elementRef,
  selector,
  height: providedHeight,
  defaultHeight = DEFAULT_HEIGHT,
  isHidden
}: UseExpandableContentProps) => {
  const [measuredHeight, setMeasuredHeight] = useState(0);

  useLayoutEffect(() => {
    if (elementRef?.current) {
      setMeasuredHeight(elementRef.current.scrollHeight || 0);
    } else if (selector) {
      // Fallback to selector for backward compatibility
      const element = document.querySelector(selector);
      setMeasuredHeight(element?.scrollHeight || defaultHeight);
    }
  }, [elementRef, selector, defaultHeight]);

  // Use provided height, measured height from ref, or fallback to default
  const contentHeight = providedHeight || measuredHeight || defaultHeight;
  const isTruncated = contentHeight > defaultHeight;

  const getHeight = () => {
    if (!isTruncated) {
      return '100%';
    }
    return isTruncated && isHidden ? `${defaultHeight}px` : `${contentHeight}px`;
  };

  const getClassName = () => {
    if (!isTruncated) {
      return '';
    }
    return `is-truncated ${isHidden ? 'collapsed' : 'expanded'}`;
  };

  return {
    isTruncated,
    height: getHeight(),
    className: getClassName(),
  };
};
