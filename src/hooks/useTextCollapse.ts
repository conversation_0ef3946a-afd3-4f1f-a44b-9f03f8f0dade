import { RefObject, useLayoutEffect, useState } from 'react';

interface UseTextCollapseProps {
  elementRef: RefObject<HTMLElement>;
  defaultHeight?: number;
  isHidden: boolean;
}

const DEFAULT_HEIGHT = 90;

export const useTextCollapse = ({ 
  elementRef, 
  defaultHeight = DEFAULT_HEIGHT, 
  isHidden 
}: UseTextCollapseProps) => {
  const [measuredHeight, setMeasuredHeight] = useState(0);

  useLayoutEffect(() => {
    if (elementRef?.current) {
      setMeasuredHeight(elementRef.current.scrollHeight || 0);
    }
  }, [elementRef]);

  const contentHeight = measuredHeight || defaultHeight;
  const isTruncated = contentHeight > defaultHeight;

  const getHeight = () => {
    if (!isTruncated) {
      return '100%';
    }
    return isTruncated && isHidden ? `${defaultHeight}px` : `${contentHeight}px`;
  };

  const getClassName = () => {
    if (!isTruncated) {
      return '';
    }
    return `is-truncated ${isHidden ? 'collapsed' : 'expanded'}`;
  };

  return {
    isTruncated,
    height: getHeight(),
    className: getClassName(),
  };
};
