import { useTextCollapse } from '@hooks/useTextCollapse';
import React, { useRef, useState } from 'react';
import { Button } from 'reactstrap';
import './styles.scss';

interface Props {
  children: React.ReactNode;
  defaultHeight?: number;
}

export const ExpandableParagraph = (props: Props) => {
  const { children, defaultHeight = 55 } = props;

  const [isHidden, setHidden] = useState(true);
  const paragraphRef = useRef<HTMLDivElement | null>(null);

  const { height, className, isTruncated } = useTextCollapse({
    elementRef: paragraphRef,
    defaultHeight,
    isHidden,
  });

  const HiddenBtn = () => {
    const text = isHidden ? 'Read more' : 'Read less';
    const icon = isHidden ? 'fa-angle-down' : 'fa-angle-up';
    return (
      <Button color='link-secondary' onClick={() => setHidden((prev) => !prev)}>
        <div className='text-sm py-1'>
          <i className={`fa ${icon} mr-1 text-sm`} />
          {text}
        </div>
      </Button>
    );
  };

  return (
    <div className='expandable-paragraph'>
      <div ref={paragraphRef} className={className} style={{ height }}>
        {children}
      </div>
      {isTruncated ? <HiddenBtn /> : null}
    </div>
  );
};
