/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React from 'react';
import { Route, useParams, useRouteMatch } from 'react-router-dom';
import { AppRouteInterface, RouteInterface } from '../types/routes';
import { OrganizationSwitcher } from '../components/initiative/OrganizationSwitcher';
import { useAppDispatch, useAppSelector } from '../reducers';
import { SettingStorage } from '../services/SettingStorage';
import { loadInitiativeTree } from '../actions';
import { AppLayout } from '../layout/AppLayout';
import Header from '../components/header';
import { AppHeader } from '../components/header/AppHeader';
import classNames from 'classnames';
import { SubscriptionBanner } from '../components/header/SubscriptionBanner';
import { SubscriptionService } from '../services/SubscriptionService';
import { Tree } from '../types/tree';
import { SubscriptionBlocked } from './company-tracker/SubscriptionBlocked';
import { loadInitiativeById } from '../actions/initiative';
import Dashboard, { DashboardSection } from '../components/dashboard';
import SDGapLogo30pc from '../images/30pc_SDGap_logo.svg';
import { getCurrentUser } from '../selectors/user';
import { Loader } from '@g17eco/atoms/loader';
import { AppRootMatcher } from './appRootMatcher';
import { useIsOrganizationId } from '../hooks/isOrgInitiativeId';
import { NoOrganization } from '@features/organization/NoOrganizationView';
import { getAppRoute, shouldRedirectToAppPath } from './util';
import { RootInitiativeData } from '../types/initiative';
import { useRouteAppConfig } from '../hooks/useRouteAppConfig';
import { isDemo } from '@utils/initiative';

interface Props {
  appRoute: AppRouteInterface;
  routeMatcher: AppRootMatcher;
  pathMatch: string;
  initiativeTree: Tree;
  changeReportingLevelRoute: RouteInterface;
  redirectHandler: (initiativeId: string, rootAppPath?: string) => void;
}

export const RootSwitcherContext = React.createContext<Partial<Props>>({});

const DemoBanner = () => {
  return (
    <div className={'background-ColourPink text-white p-2 text-center w-100 text-uppercase fw-bold'}>
      You are currently in a demo account
    </div>
  );
}

export const OrganizationSwitcherRoute: React.FC<React.PropsWithChildren<Props>> = (props) => {
  const {
    appRoute: defaultRoute,
    routeMatcher,
    pathMatch,
    initiativeTree,
    redirectHandler,
    changeReportingLevelRoute: route,
  } = props;

  const dispatch = useAppDispatch();

  const { rootAppPath } = useParams<{ rootAppPath: string }>();

  const user = useAppSelector(getCurrentUser);
  const initiativeState = useAppSelector((state) => state.initiative);
  const rootInitiativesState = useAppSelector((state) => state.rootInitiatives);
  const rootInitiatives = rootInitiativesState.data;

  const globalData = useAppSelector((state) => state.globalData);
  const organizations = routeMatcher.filterInitiatives<RootInitiativeData>(rootInitiatives);

  // Only available should select it automatically
  const selectedId =
    organizations.length === 1 ? organizations[0]?._id : SettingStorage.getItem(routeMatcher.storageKey);
  const organization = organizations.find((o) => o._id === selectedId);
  const appConfig = useRouteAppConfig({ organization });

  const match = useRouteMatch<{ initiativeId: string }>({ path: pathMatch });
  const urlId = match?.params?.initiativeId;
  const { isValid, isLoaded } = useIsOrganizationId({ organization, initiativeId: urlId });

  React.useEffect(() => {
    if (organization) {
      dispatch(loadInitiativeTree({ organization }));
    }
  }, [dispatch, organization]);

  React.useEffect(() => {
    if (isValid && urlId) {
      dispatch(loadInitiativeById(urlId));
    }
  }, [dispatch, urlId, isValid]);

  const firstInitiativeId = organization?.firstInitiativeId;
  const root = initiativeState.loaded ? initiativeState.data.root : undefined;
  const shouldRedirect =
    isLoaded &&
    !isValid && // Url id is not valid
    // it's not the same as first id, we would redirect anyway
    firstInitiativeId &&
    urlId !== firstInitiativeId &&
    // Root id is not the one we have selected?
    root?._id !== firstInitiativeId;

  if (shouldRedirect || shouldRedirectToAppPath(rootAppPath, appConfig)) {
    if (appConfig && firstInitiativeId) {
      redirectHandler(firstInitiativeId, appConfig.rootAppPath);
    }
    return <Loader />;
  }

  if (!organization) {
    return (
      <RootSwitcherContext.Provider value={props}>
        <AppLayout
          Header={
            <Route path={pathMatch}>
              <Header routeMatcher={routeMatcher} rootInitiatives={rootInitiatives}>
                <AppHeader
                  initiativeTree={initiativeTree}
                  appRoute={defaultRoute}
                  route={route}
                  appConfig={appConfig}
                />
              </Header>
            </Route>
          }
        >
          {organizations.length === 0 ? (
            <NoOrganization rootInitiativesState={rootInitiativesState} appName={defaultRoute.appName} />
          ) : (
            <Dashboard>
              <DashboardSection className={'text-center'}>
                <div className={'text-left '} style={{ maxWidth: '260px', margin: '0 auto' }}>
                  <h1 className={'h2 text-ThemeAccentExtradark mb-2'}>Hi {user?.firstName ?? ''}</h1>
                  <span className={'d-block mb-4'}>Which organisation do you want to view?</span>

                  <OrganizationSwitcher
                    selectStyles={{
                      container: (p) => ({ ...p, marginBottom: '1rem' }),
                      menu: (p) => ({ ...p, marginTop: '1px' }),
                    }}
                    viewMode={'select'}
                    routeMatcher={routeMatcher}
                    rootInitiatives={rootInitiatives}
                  />

                  <div className='image-container mb-2'>
                    <img src={SDGapLogo30pc} alt={'Logo'} style={{ maxWidth: '300px' }} />
                  </div>
                </div>
              </DashboardSection>
            </Dashboard>
          )}
        </AppLayout>
      </RootSwitcherContext.Provider>
    );
  }

  if (!globalData.loaded || globalData.data.organization?._id !== organization?._id) {
    return <Loader />;
  }

  const appRoute = getAppRoute(defaultRoute, appConfig);
  const subs = organization.calculatedSubscriptions;
  const subscription = appRoute.productCode
    ? SubscriptionService.getBestValidSubscription(appRoute.productCode, subs)
    : undefined;
  const isBlocked =
    appRoute.productCode &&
    !SubscriptionService.shouldAllowAccess(appConfig ?? { validProductCodes: [appRoute.productCode] }, subs);

  return (
    <RootSwitcherContext.Provider value={props}>
      <AppLayout
        Header={
          <Route path={pathMatch}>
            {isDemo(organization) ? <DemoBanner /> : null}
            <Header
              brandingLogo={appRoute.headerIcon}
              displayRootSwitcher
              className={classNames({ 'has-banner': Boolean(subscription) })}
              routeMatcher={routeMatcher}
              rootInitiatives={rootInitiatives}
            >
              <SubscriptionBanner appConfig={appConfig} organization={organization} subscription={subscription} />
              <AppHeader initiativeTree={initiativeTree} appRoute={appRoute} route={route} appConfig={appConfig} />
            </Header>
          </Route>
        }
      >
        {appRoute.productCode && isBlocked ? (
          <SubscriptionBlocked
            organization={organization}
            subscriptions={subs}
            appName={appRoute.appName}
            productCode={appRoute.productCode}
            initiativeId={organization._id}
          />
        ) : (
          props.children
        )}
      </AppLayout>
    </RootSwitcherContext.Provider>
  );
};
