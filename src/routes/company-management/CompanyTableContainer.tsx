/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import { useHistory } from 'react-router-dom';
import { ROUTES } from '../../constants/routes';
import SettingsSidebar, { SearchBox, SettingsSection } from '../../components/settings-sidebar';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import CompanyTable from './CompanyTable';
import { generateUrl } from '../util';
import Dashboard, { DashboardSection } from '../../components/dashboard';
import { InitiativeCompany } from '../../types/initiative';
import './styles.scss';

interface CompanyTableContainerProps {
  portfolioId: string;
  companies: InitiativeCompany[];
  isLoadingCompanies?: boolean;
  handleReload: () => void;
}

export const CompanyTableContainer = ({ portfolioId, companies, isLoadingCompanies = false, handleReload }: CompanyTableContainerProps) => {
  const history = useHistory();

  const [searchText, setSearchText] = React.useState('');
  const [filterType, setFilterType] = React.useState('');

  const filterOptions = [
    {
      value: 'active',
      label: 'Active',
    },
    {
      value: 'inactive',
      label: 'Inactive',
    },
    {
      value: 'invited',
      label: 'No users onboarded',
    },
  ];

  const toggleCompanyInvitationForm = () => {
    const clickRoute = ROUTES.PORTFOLIO_TRACKER_MANAGE_COMPANIES;
    const invitationUrl = generateUrl(clickRoute, { portfolioId: portfolioId, page: 'invite' });
    history.push(invitationUrl);
  };

  return (
    <Dashboard className='companyManagement' hasSidebar={true}>
      <SettingsSidebar>
        <SettingsSection title={'Search companies'}>
          <SearchBox
            handleOnChange={(e) => setSearchText(String((e.target as HTMLInputElement).value))}
            value={searchText}
          />
        </SettingsSection>
        <SettingsSection title={'Filters'}>
          <>
            <SelectFactory
              selectType={SelectTypes.SingleSelect}
              placeholder='Select status'
              isClearable={true}
              options={filterOptions}
              value={filterType ? filterOptions.find((o) => o.value === filterType) : null}
              onChange={(o) => setFilterType(o?.value ?? '')}
            />
          </>
        </SettingsSection>
      </SettingsSidebar>

      <DashboardSection
        className='companyManagement-container'
        title='Manage Companies'
        icon='fal fa-cog'
        buttons={[
          {
            icon: 'fa-plus',
            tooltip: 'Invite a new company',
            onClick: toggleCompanyInvitationForm,
          },
        ]}
      >
        <>
          <p>Here you can view and manage the companies that you have added as a holding to your portfolios.</p>
          <CompanyTable
            portfolioId={portfolioId}
            companies={companies}
            isLoadingCompanies={isLoadingCompanies}
            handleReload={handleReload}
            searchText={searchText}
            filterType={filterType}
          />
        </>
      </DashboardSection>
    </Dashboard>
  );
};
