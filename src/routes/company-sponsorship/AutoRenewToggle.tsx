/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */


import { SponsorshipConfig } from '@g17eco/types/sponsorship';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { Toggle } from '@g17eco/molecules/toggle';
import { addSiteError } from '@g17eco/slices/siteAlertsSlice';
import { QueryWrapper } from '@components/query/QueryWrapper';
import { useAppDispatch } from '@reducers/index';
import { useAutoRenewSubscriptionsMutation, useGetSponsorshipConfigQuery } from '@api/portfolio-tracker';

interface Props {
  portfolioId: string;
  selectedCode: string;
}

export const AutoRenewToggle = (props: Props) => {
  const { portfolioId, selectedCode } = props;

  const dispatch = useAppDispatch();
  const sponsorshipConfigQuery = useGetSponsorshipConfigQuery({ portfolioId, code: selectedCode });
  const [autoRenewSubscriptions, { isLoading: isUpdatingAutoRenew }] = useAutoRenewSubscriptionsMutation();

  const render = (sponsorshipConfig?: SponsorshipConfig) => {
    return (
      <div>
        <SimpleTooltip
          text={!sponsorshipConfig ? 'This feature is not available for a legacy sponsorship' : undefined}>
          <Toggle
            className={{
              form: 'd-flex align-items-center mb-2',
              label: 'text-md ml-2 text-ThemeTextDark fw-normal',
            }}
            checked={Boolean(sponsorshipConfig?.autoRenew)}
            disabled={!sponsorshipConfig || isUpdatingAutoRenew}
            onChange={(e) => {
              autoRenewSubscriptions({ portfolioId, code: selectedCode, autoRenew: e.currentTarget.checked })
                .unwrap()
                .catch((e) => dispatch(addSiteError(e)))
            }}
            label='Renew subscriptions automatically'
          />
        </SimpleTooltip>
      </div>
    )
  }

  return <QueryWrapper query={sponsorshipConfigQuery} onSuccess={render} onError={() => render()} />;
}
