/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useCallback, useEffect, useMemo, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { CompanyTable } from './CompanyTable';
import { Loader } from '@g17eco/atoms/loader';
import Dashboard, { DashboardRow, DashboardSection, DashboardSectionTitle } from '../../components/dashboard';
import { AdminBreadcrumbs } from '../admin-dashboard/AdminBreadcrumbs';
import { useAppSelector } from '../../reducers';
import { isStaff } from '@selectors/user';
import { convertToTableRows, isSponsorshipActive } from './utils';
import { Button } from 'reactstrap';
import { generateUrl } from '../util';
import { ROUTES } from '@constants/routes';
import { BasicAlert } from '@g17eco/molecules/alert';
import { ExtraFeature, FeatureStability } from '@g17eco/molecules/feature-stability';
import { Option, SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { Toggle } from '@g17eco/molecules/toggle';
import { useToggle } from '@hooks/useToggle';
import { FormatOptionLabelMeta } from 'react-select';
import { useGetSponsoredInitiativesQuery, useGetSponsorshipCodesQuery } from '@api/portfolio-tracker';
import { skipToken } from '@reduxjs/toolkit/query';
import { AutoRenewToggle } from '@routes/company-sponsorship/AutoRenewToggle';

export const CompanySponsorship = () => {
  const history = useHistory();
  const { portfolioId } = useParams<{ portfolioId: string }>();
  const isUserStaff = useAppSelector(isStaff);

  const [showExpired, toggleShowExpired] = useToggle(false);
  const [selectedCode, setSelectedCode] = useState<string | undefined>();

  const { data: sponsorshipCodes = [], isFetching: isFetchingCodes } = useGetSponsorshipCodesQuery(portfolioId);

  const referralCode = selectedCode ?? sponsorshipCodes[0];

  const { data: sponsoredCompanies = [], isFetching: isFetchingCompanies, refetch } = useGetSponsoredInitiativesQuery(referralCode ? { portfolioId, code: referralCode } : skipToken);

  const companiesTableData = useMemo(() => {
    return convertToTableRows(sponsoredCompanies).filter((company) => showExpired || isSponsorshipActive(company));
  }, [sponsoredCompanies, showExpired]);

  const handleReload = useCallback(async () => {
    await refetch();
  }, [refetch]);

  useEffect(() => {
    if (!selectedCode || !sponsorshipCodes.includes(selectedCode)) {
      setSelectedCode(sponsorshipCodes[0]);
    }
  }, [sponsorshipCodes, selectedCode, setSelectedCode]);

  if (isFetchingCodes) {
    return <Loader />;
  }

  if (!isUserStaff) {
    return (
      <Dashboard className='company-sponsorship'>
        <DashboardSection title='Coming Soon'>
          <p>Here you can view and manage the companies that have been sponsored using your discount code.</p>
        </DashboardSection>
      </Dashboard>
    );
  }

  const noCodes = sponsorshipCodes.length === 0;

  const sponsorshipOptions: Option<string>[] = sponsorshipCodes.map((code) => ({
    value: code,
    label: code,
  }));

  const formatOptionLabel = (option: Option<string> | null, meta: FormatOptionLabelMeta<Option<string> | null>) => {
    if (!option) {
      return <></>;
    }
    if (meta.context === 'value') {
      return <span className='fs-5 fw-semibold text-ThemeHeadingMedium'>Referral code: {selectedCode}</span>;
    }
    return option.label;
  };

  return (
    <Dashboard className='company-sponsorship'>
      <DashboardRow>
        <AdminBreadcrumbs
          breadcrumbs={[{ label: 'Manage sponsorship' }]}
          initiativeId={portfolioId}
          isPortfolioTracker={true}
        />
      </DashboardRow>
      <DashboardSectionTitle
        title={
          <span>
            Manage sponsorship <FeatureStability feature={ExtraFeature.CompanySponsorship} />
          </span>
        }
        buttons={[
          <Button
            key='sponsor-new-companies'
            color='secondary'
            outline
            onClick={() => history.push(generateUrl(ROUTES.PORTFOLIO_TRACKER_MANAGE_COMPANIES, { portfolioId }))}
          >
            Sponsor new companies
          </Button>,
        ]}
      />
      <DashboardSection>
        {noCodes || !selectedCode ? (
          <BasicAlert type='info'>
            You have no sponsorship referral codes. Please contact our support if you think this is an error.
          </BasicAlert>
        ) : (
          <>
            <div className='d-flex justify-content-between mb-2'>
              <SelectFactory
                selectType={SelectTypes.SingleSelect}
                options={sponsorshipOptions}
                onChange={(op) => (op ? setSelectedCode(op.value) : undefined)}
                isTransparent
                formatOptionLabel={formatOptionLabel}
                isSearchable={false}
                noOptionsMessage={() => 'No referral codes'}
                placeholder='Please select a code'
                value={sponsorshipOptions.find((op) => op.value === selectedCode)}
              />
              <div>
                <AutoRenewToggle portfolioId={portfolioId} selectedCode={selectedCode} />
                <Toggle
                  className={{
                    form: 'd-flex align-items-center mb-2',
                    label: 'text-md ml-2 text-ThemeTextDark fw-normal',
                  }}
                  checked={showExpired}
                  onChange={toggleShowExpired}
                  label='Show expired sponsorships'
                />
              </div>
            </div>
            <CompanyTable
              referralCode={selectedCode}
              portfolioId={portfolioId}
              companiesTableData={companiesTableData}
              isFetching={isFetchingCompanies}
              handleReload={handleReload}
            />
          </>
        )}
      </DashboardSection>
    </Dashboard>
  );
};
