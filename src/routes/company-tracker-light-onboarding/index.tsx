/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React from 'react';
import Header from '../../components/header';
import { Switch, Route, useParams } from 'react-router-dom';
import { AppLayout } from '../../layout/AppLayout';
import { AppRouteInterface, RouteInterface } from '../../types/routes';
import { AppHeader } from '@components/header/AppHeader';
import { NotFound } from '../not-found/NotFound';
import { getDefaultConfig } from '../../config/app-config';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { useGetAppConfigQuery } from '@api/g17ecoApi';
import { getAppRoute } from '../util';
import Dashboard, { DashboardRow } from '../../components/dashboard';
import { SGXESGenomeOnboarding } from '@components/onboarding-esgenome';
import { Onboarding } from '@components/onboarding';
import { Activation } from '@components/onboarding/Activation';
import { BasicAlert } from '@g17eco/molecules/alert';
import { RouteContainer } from '@g17eco/molecules/route-container';
import { ctBrandedRoutes, getOnboardingRoutes } from '@utils/apps';

// Will get overwritten by appConfig if available
const settings = getDefaultConfig().branding.ctlApp;
const onboardingAppName = settings.name;

const allRoutes = getOnboardingRoutes();
const routeAppCode = `:onboardingPath(${allRoutes.join('|')})`;
const nonSGXRouteAppCode = `:onboardingPath(${ctBrandedRoutes.join('|')})`;

export const onboardingRouteEntry: RouteInterface = {
  id: 'company_tracker_light_entry',
  label: onboardingAppName,
  path: `/${routeAppCode}/onboarding`,
  exact: false,
  component: CompanyTrackerLightRoute
}

export const companyTrackerOnboardingRoute: RouteInterface = {
  id: 'company_tracker',
  label: `${onboardingAppName} - Signup`,
  path: `/${routeAppCode}/onboarding/:activeStep?`,
  exact: false,
  icon: 'fa-cloud-sun',
}

export const esgenomeOnboardingRoute: RouteInterface = {
  ...companyTrackerOnboardingRoute,
  path: '/:onboardingPath(sgx-esgenome)/onboarding',
}

const companyTrackerActivationRoute: RouteInterface = {
  id: 'company_tracker_activation',
  label: `${onboardingAppName} - Activation`,
  path: `/${nonSGXRouteAppCode}/onboarding/:activation(activate|incomplete)/:initiativeId/:surveyId/:token`,
  exact: true,
}

const esgenomeActivationRoute: RouteInterface = {
  ...companyTrackerActivationRoute,
  path: '/:onboardingPath(sgx-esgenome)/onboarding/:activation(activate|incomplete)/:initiativeId/:surveyId/:token',
}

export const matOnboardingRoute: RouteInterface = {
  id: 'materiality_tracker_tool',
  label: 'Materiality Tracker - Signup',
  path: '/:onboardingPath(materiality-tracker)/onboarding/:activeStep?',
  exact: false,
};

const matActivationRoute: RouteInterface = {
  id: 'materiality_tracker_tool_activation',
  label: 'Materiality Tracker - Activation',
  path: '/:onboardingPath(materiality-tracker)/onboarding/:activation(activate|incomplete)/:initiativeId/:surveyId/:token',
  exact: true,
};

const onboardingAppRoute: AppRouteInterface = {
  appName: onboardingRouteEntry.label,
  appIcon: settings.logo,
  routes: [companyTrackerOnboardingRoute],
  hasSidebar: false
}

function CompanyTrackerLightRoute() {
  const { onboardingPath } = useParams<{ onboardingPath: string }>();
  const { isError, error, isSuccess, data } = useGetAppConfigQuery(onboardingPath)

  if (isError) {
    return (
      <RouteContainer>
        <Dashboard>
          <DashboardRow>
            <BasicAlert type={'danger'}>{error.message}</BasicAlert>
          </DashboardRow>
        </Dashboard>
      </RouteContainer>
    )
  }

  if (!isSuccess) {
    return (
      <RouteContainer>
        <BlockingLoader />
      </RouteContainer>
    )
  }

  const appRoute = getAppRoute(onboardingAppRoute, data.appConfig)
  const onboardingRoute = { ...companyTrackerOnboardingRoute, label: appRoute.appName }

  const header = <Header brandingLogo={appRoute.headerIcon}>
    <AppHeader appRoute={appRoute} route={onboardingRoute} />
  </Header>;

  if (!data.appConfig) {
    return <BlockingLoader />;
  }

  // Manually wrap with container, need to be outside due to non-activated flow
  return (
    <RouteContainer>
      <AppLayout Header={header}>
        <Switch>
          <Route {...esgenomeActivationRoute} >
            <SGXESGenomeOnboarding appConfig={data.appConfig} />
          </Route>
          <Route {...esgenomeOnboardingRoute}>
            <SGXESGenomeOnboarding appConfig={data.appConfig} />
          </Route>
          <Route {...matActivationRoute} >
            <Activation appConfig={data.appConfig} />
          </Route>
          <Route {...matOnboardingRoute}>
            <Onboarding appConfig={data.appConfig} />
          </Route>
          <Route {...companyTrackerActivationRoute} >
            <Activation appConfig={data.appConfig} />
          </Route>
          <Route {...companyTrackerOnboardingRoute}>
            <Onboarding appConfig={data.appConfig} />
          </Route>
          <Route>
            <NotFound />
          </Route>
        </Switch>
      </AppLayout>
    </RouteContainer>
  )
}
