/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { GridLayoutDashboard } from '@routes/custom-dashboard/GridLayoutDashboard';
import { DashboardSurveyType, GridDashboardItem, InsightDashboard } from '@g17eco/types/insight-custom-dashboard';
import { DeleteFromOverviewBtn } from '@routes/summary/DeleteFromOverviewBtn';
import { useSelector } from 'react-redux';
import { selectMostRecentSurvey } from '@g17eco/slices/initiativeSurveyListSlice';
import { useGetSurveyByIdQuery } from '@api/surveys';
import { Loader } from '@g17eco/atoms/loader';
import { CompanyProfile } from '@components/company-profile';
import { DashboardSectionTitle } from '@components/dashboard';
import { UniversalTrackersTable } from '@components/universal-trackers-table';
import MediaSlider from '@apps/company-tracker/components/documents/media-slider';
import { RatingsCarousel, StandardsCarousel } from '@components/ratings-standards';
import { useAppDispatch, useAppSelector } from '@reducers/index';
import { useCallback, useEffect, useState } from 'react';
import { loadScorecardsByInitiativeId, loadScorecardsBySurveyId } from '@actions/scorecard';
import { getSummaryUtrTable } from '@selectors/index';
import { SUMMARY_DASHBOARD_UTR_TABLE } from '@constants/usage-slots';
import { loadUniversalTrackerValues } from '@actions/universalTracker';
import { ReportSwitcherContainer } from '@components/initiative/ReportSwitcherContainer';
import { InitiativeData } from '@g17eco/types/initiative';
import { useAppSettings } from '@hooks/app/useAppSettings';
import { SdgContributionGraph } from '@components/impact-performance/SdgContributionGraph';
import { MaterialityModal } from '@components/target-actual/MaterialityModal';
import IconButton from '@components/button/IconButton';
import { canAccessCurrentLevel, isUserManagerByInitiativeId } from '@selectors/user';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { reloadInitiative } from '@actions/initiative';
import { skipToken } from '@reduxjs/toolkit/query';
import { DataPeriods } from '@g17eco/types/universalTracker';
import { SdgChangeFn } from '@components/impact-performance/SdgCharProps';

interface Props {
  initiative: InitiativeData | undefined;
  dashboard: InsightDashboard;
  initiativeId: string;

  // URL surveyId
  surveyId?: string;

  // Highlight data filter
  filters?: {
    period?: DataPeriods;
    surveyType?: DashboardSurveyType;
  }
}

export const OverviewDashboard = ({ initiative, surveyId: urlSurveyId = '', dashboard, initiativeId, filters }: Props) => {
  const dispatch = useAppDispatch();

  const mostRecentSurvey = useSelector(selectMostRecentSurvey);
  const { data: latestSurvey, isFetching } = useGetSurveyByIdQuery(mostRecentSurvey?._id ?? skipToken);

  const utrTable = useAppSelector(getSummaryUtrTable);
  const { canEditInsightOverview } = useAppSettings();
  const readOnly = !canEditInsightOverview;
  const [sdgCode, setSDGCode] = useState<string | undefined>('');

  // SDG Chart + materiality + utr table props
  const canManage = useAppSelector((state) => isUserManagerByInitiativeId(state, initiativeId));
  const canAccessAITools = useAppSelector(FeaturePermissions.canAccessSDGInsightAI);
  const [materialityModal, setMaterialityModal] = useState(false);

  const canAccess = useAppSelector((state) => canAccessCurrentLevel(state));

  const handleSDGChange = useCallback(
    (sdgCodes: Parameters<SdgChangeFn>[0]) => {
      setSDGCode(String(Array.isArray(sdgCodes) ? sdgCodes[0] : sdgCodes));
    },
    [setSDGCode]
  );

  // @TODO need to make a decision here
  // Idea here, is that we ignore route parameter and always use latest?
  // for everything except SDG chart scorecard?
  // Quite confusing, but it's how it is now. Maybe to avoid page reload, to align?
  // Otherwise, we should update url on survey change to make surveyId consistent
  const latestSurveyId = mostRecentSurvey?._id;
  const [sdgChartSurveyId, setSdgChartSurveyId] = useState<string>(latestSurveyId ?? urlSurveyId);

  const scorecardState = useAppSelector((state) => (sdgChartSurveyId ? state.scorecardSurvey : state.scorecard));
  useEffect(() => {
    if (sdgChartSurveyId) {
      dispatch(loadScorecardsBySurveyId(initiativeId, sdgChartSurveyId));
    } else {
      dispatch(loadScorecardsByInitiativeId(initiativeId));
    }
  }, [dispatch, initiativeId, sdgChartSurveyId]);

  const handleReload = () => {
    dispatch(reloadInitiative());
    if (sdgChartSurveyId) {
      dispatch(loadScorecardsBySurveyId(initiativeId, sdgChartSurveyId, false, undefined, true));
    } else {
      dispatch(loadScorecardsByInitiativeId(initiativeId, false, undefined, true));
    }
  };

  useEffect(() => {
    // This is no longer SDG Chart, so it uses latest surveyId that is not changing...
    // @TODO: Should this even use survey or load initiative data?
    dispatch(loadUniversalTrackerValues(true, latestSurveyId));
  }, [dispatch, latestSurveyId]);

  const toggleMaterialityModal = () => setMaterialityModal(!materialityModal);
  const materialityToggle = (
    <IconButton className='fas sdg-materiality-editbtn' onClick={() => toggleMaterialityModal()} icon='fa-pen' />
  );

  return (
    <>
      {isFetching ? <Loader /> : null}
      <CompanyProfile initiative={initiative} readOnly={readOnly} />
      <DashboardSectionTitle title='Highlights' headingStyle={2} className='text-ThemeHeadingLight' />
      <GridLayoutDashboard
        isEditing={false}
        gridItems={dashboard.items}
        utrsData={dashboard.utrsData}
        readOnly
        initiativeId={initiativeId}
        actionBtn={({ item }: { item: GridDashboardItem }) => (
          <DeleteFromOverviewBtn initiativeId={initiativeId} item={item} />
        )}
        survey={latestSurvey}
        dataFilters={filters}
      />

      <SdgContributionGraph
        canAccess={canAccess}
        showAITools={canAccessAITools}
        scorecardState={scorecardState}
        toggleMaterialityBtn={canManage ? materialityToggle : <></>}
        readOnly={readOnly}
        surveyId={sdgChartSurveyId}
        reportSwitcher={
          <div className='d-flex justify-content-end'>
            <ReportSwitcherContainer
              defaultToCurrent={false}
              initiativeId={initiativeId}
              selectedSurveyId={sdgChartSurveyId}
              onChange={(value: string) => setSdgChartSurveyId(value)}
            />
          </div>
        }
        initiativeId={initiativeId}
        handleChangeSDG={handleSDGChange}
        sdgCode={sdgCode}
      />
      {utrTable ? (
        <>
          <DashboardSectionTitle title='Targets and Trends' headingStyle={2} className='text-ThemeHeadingLight' />
          <UniversalTrackersTable
            header={'Targets and trends'}
            readOnly={!canManage || readOnly}
            usage={SUMMARY_DASHBOARD_UTR_TABLE}
            universalTrackers={utrTable}
          />
        </>
      ) : null}

      <MaterialityModal
        isOpen={materialityModal}
        toggle={toggleMaterialityModal}
        initiative={initiative}
        handleReload={handleReload}
      />

      {!canEditInsightOverview ? null : (
        <>
          <DashboardSectionTitle
            title='Media, Ratings and Certifications'
            headingStyle={2}
            className='text-ThemeHeadingLight'
          />
          <MediaSlider readOnly={readOnly} initiativeId={initiativeId} />

          <RatingsCarousel
            surveyId={latestSurveyId}
            readOnly={readOnly}
            initiativeId={initiativeId}
            options={{ showInfoButton: true }}
          />
          <StandardsCarousel
            surveyId={latestSurveyId}
            readOnly={readOnly}
            initiativeId={initiativeId}
            options={{ showDownloadButton: true, showInfoButton: true }}
          />
        </>
      )}
    </>
  );
};
