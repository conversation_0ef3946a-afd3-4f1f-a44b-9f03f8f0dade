/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import { rootAppPath, getRootAppPath } from './utils';
import ReportsList from '@apps/company-tracker/components/reports-list';
import { RoutesInterface } from '@g17eco/types/routes';
import { Switch, Route } from 'react-router-dom';
import { CompanyTrackerSurveyRoute } from './CompanyTrackerSurveyRoute';
import { AssuranceModal } from '@components/assurance/assurance-modal';
import { reloadSurveyListSummary } from '@actions/survey';
import { SurveyAddAssurance } from '@g17eco/types/survey';
import { RootSwitcherContext } from '../OrganizationSwitcherRoute';
import { useAppDispatch, useAppSelector } from '@reducers/index';
import { SettingStorage } from '@services/SettingStorage';
import { SurveyConfiguration } from '@components/survey-configuration/SurveyConfiguration';
import Dashboard from '@components/dashboard';
import { SURVEY } from '@constants/terminology';
import { CreateCombinedReportRoute } from '@apps/company-tracker/routes/CreateCombinedReportRoute';

export const reportRoutes: RoutesInterface = {
  COMPANY_TRACKER_LIST: {
    id: 'company_tracker_list',
    label: `All ${SURVEY.PLURAL}`,
    path: `/${rootAppPath}/reports/:initiativeId?`,
    icon: 'fa-tasks',
    component: ReportingRoute,
    appPermissionId: 'app_company_tracker',
    auth: true,
    exact: true,
    parentId: 'company_tracker',
    hideOnMissingPermission: true,
    getRootAppPath: getRootAppPath
  },
  COMPANY_TRACKER_ASSURANCE: {
    id: 'company_tracker_survey_assurance',
    label: 'Assurance',
    path: `/${rootAppPath}/reports/:initiativeId/:surveyId/assurance/:assuranceId?`,
    component: ReportingRoute,
    appPermissionId: 'app_company_tracker',
    auth: true,
    hideOnMissingPermission: true,
    requiresInitiativeId: true,
    parentId: 'company_tracker',
    getRootAppPath: getRootAppPath
  },
  COMPANY_TRACKER_SURVEY: {
    id: 'company_tracker_survey',
    label: SURVEY.CAPITALIZED_ADJECTIVE,
    path: `/${rootAppPath}/reports/:initiativeId/:surveyId/:page?`,
    defaultParams: {
      page: 'overview'
    },
    component: ReportingRoute,
    appPermissionId: 'app_company_tracker',
    auth: true,
    hideOnMissingPermission: true,
    requiresInitiativeId: true,
    parentId: 'company_tracker',
    getRootAppPath: getRootAppPath
  },
}

interface AssuranceModalState {
  isOpen: boolean;
  surveyId: string;
  initiativeId: string;
}

export function ReportingRoute() {

  const [assuranceModal, setAssuranceModal] = useState<AssuranceModalState>({
    isOpen: false,
    surveyId: '',
    initiativeId: '',
  });
  const { data: rootInitiatives } = useAppSelector((state) => state.rootInitiatives);
  const { routeMatcher } = React.useContext(RootSwitcherContext)

  // @TODO should clean up once root org is improved
  let selectedId: string | undefined;
  if (routeMatcher) {
    const organizations = routeMatcher.filterInitiatives(rootInitiatives)
    selectedId = organizations.length === 1 ? organizations[0]?._id : SettingStorage.getItem(routeMatcher.storageKey);
  }

  const dispatch = useAppDispatch();

  const handleAddAssurance = (survey: SurveyAddAssurance) => {
    setAssuranceModal((am) => ({
      ...am,
      isOpen: true,
      surveyId: survey._id,
      initiativeId: survey.initiativeId,
    }));
  }

  const toggleAssurance = (submitted?: boolean) => {
    setAssuranceModal((am) => ({ ...am, isOpen: !am.isOpen }));
    if (submitted === true) {
      dispatch(reloadSurveyListSummary());
    }
  };

  // Match survey or fallback to list view
  return <>
    <Switch>
      <Route path={`/${rootAppPath}/reports/:initiativeId/create-combined`}>
        <CreateCombinedReportRoute />
      </Route>
      <Route path={`/${rootAppPath}/reports/:initiativeId/:surveyId/update-combined`}>
        <CreateCombinedReportRoute />
      </Route>
      <Route path={`/${rootAppPath}/reports/:initiativeId/create`}>
        <Dashboard>
          <SurveyConfiguration />
        </Dashboard>
      </Route>

      <Route path={reportRoutes.COMPANY_TRACKER_SURVEY.path} >
        <CompanyTrackerSurveyRoute handleAddAssurance={handleAddAssurance} />
      </Route>

      <Route>
        <ReportsList organizationId={selectedId} handleAddAssurance={handleAddAssurance} />
      </Route>
    </Switch>
    {assuranceModal.isOpen && <AssuranceModal toggle={toggleAssurance} {...assuranceModal} />}
  </>
}
