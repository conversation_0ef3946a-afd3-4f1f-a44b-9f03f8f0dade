/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useParams } from 'react-router-dom';
import { useGetSummaryInsightDashboardQuery, useUpdateSummaryInsightDashboardMutation } from '@api/insight-dashboards';
import Dashboard, { DashboardRow } from '@components/dashboard';
import { CompanyInsightsSidebar } from '../summary/insights/partials/sidebar/CompanyInsightsSidebar';
import { Loader } from '@g17eco/atoms/loader';
import { OverviewDashboard } from './OverviewDashboard';
import { Summary } from '../summary/Summary';
import {
  DashboardSurveyType,
  InsightDashboard,
  InsightDashboardActions,
  InsightDashboardFilters,
  InsightDashboardTitle,
  InsightDashboardType,
  SummaryPages,
  UtrvFilter,
} from '@g17eco/types/insight-custom-dashboard';
import { RootState, useAppSelector } from '../../reducers';
import { canManageCurrentLevel } from '../../selectors/user';
import { DashboardSettings } from '../custom-dashboard/dashboard-settings';
import { SharingButton } from '../custom-dashboard/shared-dashboard/SharingButton';
import { isESGDashboard } from '../custom-dashboard/shared-dashboard/utils';
import { useGetAvailablePeriodsQuery } from '@api/initiatives';
import { useState } from 'react';
import { DataPeriods } from '@g17eco/types/universalTracker';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { getRootOrg } from '../../selectors/initiative';
import { getMainDownloadCode, MainDownloadCode } from '../../config/app-config';
import { Button, ButtonGroup } from 'reactstrap';
import { layoutOptions } from '@constants/layout';
import { SurveyType } from '@g17eco/types/survey';
import { SurveyTypeDropdown } from '@components/survey-type-dropdown/SurveyTypeDropdown';
import { SurveyPeriodDropdown } from '@components/survey-period-dropdown';
import {
  useInsightDashboardFilters,
  usePresetInsightDashboardFilters,
} from '@features/custom-dashboard/hooks/useInsightDashboardFilters';
import { CustomDashboardTitle } from '@routes/custom-dashboard/CustomDashboardTitle';
import { useCTCustomDashboards } from '@hooks/useCTCustomDashboards';
import { useSettingsSidebar } from '@routes/custom-dashboard/dashboard-settings/useSettingsSidebar';
import { getValidPage } from '@routes/summary/insights/utils/helpers';
import { dashbboardMetricStatusOptions } from '@routes/custom-dashboard/dashboard-settings/utils';

export const StaticDashboardRoute = () => {
  const rootInitiative = useAppSelector(getRootOrg);
  const mainDownloadCode = getMainDownloadCode(rootInitiative?.appConfigCode, rootInitiative?.permissionGroup);
  // No summaryPage means we are rendering SUMMARY without explicit page, fallback to overview
  const {
    initiativeId = '',
    surveyId,
    summaryPage = 'overview',
  } = useParams<{ initiativeId?: string; summaryPage?: string; surveyId?: string }>();
  const canManage = useAppSelector(canManageCurrentLevel);
  const initiative = useAppSelector((state: RootState) => state.initiative.data);
  const [downloadCode, setDownloadCode] = useState<MainDownloadCode>(mainDownloadCode);

  const isESGDashboardPage = isESGDashboard(summaryPage);

  const { filters, setFilters } = useInsightDashboardFilters();
  const { period, surveyType } = filters;

  const { data: availablePeriods = [] } = useGetAvailablePeriodsQuery(
    { initiativeId, surveyType },
    {
      skip: !initiativeId,
    }
  );

  const { data: dashboard, isFetching } = useGetSummaryInsightDashboardQuery(
    { dashboardId: summaryPage, initiativeId, queryParams: { period, mainDownloadCode: downloadCode, surveyType } },
    {
      skip: !initiativeId || !SummaryPages.includes(summaryPage as InsightDashboardType),
    }
  );

  const [updateSummaryDashboard] = useUpdateSummaryInsightDashboardMutation();

  usePresetInsightDashboardFilters({ filters, setFilters, dashboard });

  const settingsSidebarProps = useSettingsSidebar({ initiativeId });
  const { handleAddNew } = settingsSidebarProps;
  const {
    currentPage,
    options,
    isFetchingDashboards,
    handleClickOption,
    handleNavigateCustom
  } = useCTCustomDashboards({ initiativeId, summaryPage: getValidPage(summaryPage), surveyId, handleAddNew });
  const commonSidebarProps = { initiativeId, availablePeriods, ...settingsSidebarProps, currentPage, options, handleClickOption, handleNavigateCustom };

  if (!dashboard || !initiativeId || isFetchingDashboards) {
    return (
      <Dashboard className='profile-dashboard insights-dashboard' hasSidebar>
        <CompanyInsightsSidebar {...commonSidebarProps} />
        <LoadingPlaceholder height={600} />
      </Dashboard>
    );
  }

  const handleSave = async (changes: Partial<InsightDashboard>) => {
    await updateSummaryDashboard({ ...changes, initiativeId, dashboardId: dashboard.type });

    // Sync with surveyPeriod, surveyType
    if (changes.filters) {
      setFilters({
        period: changes.filters?.period ?? DataPeriods.Yearly,
        surveyType: changes.filters.surveyType ?? SurveyType.Default,
      });
    }
  };

  const layoutButtons = (
    <ButtonGroup>
      {layoutOptions.map((option) => {
        const isActive = downloadCode === option.code;
        return (
          <Button
            key={option.code}
            outline={!isActive}
            active={isActive}
            color={'primary'}
            onClick={() => setDownloadCode(option.code)}
          >
            {option.name}
          </Button>
        );
      })}
    </ButtonGroup>
  );

  const isOverview = dashboard.type === InsightDashboardType.Overview;

  return (
    <>
      {isFetching ? <Loader /> : null}
      <Dashboard className='profile-dashboard insights-dashboard' hasSidebar>
        <CompanyInsightsSidebar {...commonSidebarProps} />
        {mainDownloadCode === MainDownloadCode.SGX_Metrics && isESGDashboardPage ? (
          <DashboardRow className='d-flex w-100 justify-content-end align-items-start'>{layoutButtons}</DashboardRow>
        ) : null}
        <DashboardRow className='w-100'>
          <div className='flex-grow-1 d-flex align-items-center'>
            <CustomDashboardTitle
              title={<h3 className='text-ThemeHeadingLight'>{dashboard.title}</h3>}
              currentPage={currentPage}
              options={options}
              handleClickOption={handleClickOption}
            />
          </div>
          {canManage ? (
            <div className='d-flex align-items-center dashboard-filters-wrapper'>
              <SurveyTypeDropdown<DashboardSurveyType>
                surveyType={surveyType}
                setSurveyType={(newSurveyType) => setFilters({ surveyType: newSurveyType })}
                styleProps={{ dropdown: 'ml-3', isTransparent: true, isFlexibleSize: true }}
              />
              <SurveyPeriodDropdown
                styleProps={{ dropdown: 'ml-2', isTransparent: true, isFlexibleSize: true }}
                period={period}
                availablePeriods={availablePeriods}
                setPeriod={(newPeriod) => setFilters({ period: newPeriod })}
              />
              {isFetching ? null : (
                <DashboardSettings
                  key={dashboard.type}
                  handleSave={handleSave}
                  dashboard={dashboard}
                  hideOptions={[
                    InsightDashboardTitle,
                    InsightDashboardFilters.BaselinesTargets,
                    InsightDashboardFilters.ShareWithSubsidiaries,
                    InsightDashboardFilters.TimeFrame,
                    ...(isOverview
                      ? [InsightDashboardFilters.InitiativeInfo, InsightDashboardFilters.SdgContribution]
                      : []),
                    InsightDashboardActions,
                    InsightDashboardFilters.InitiativeIds,
                  ]}
                  availablePeriods={availablePeriods}
                  metricStatusOptions={dashbboardMetricStatusOptions}
                />
              )}
              {isESGDashboardPage ? <SharingButton dashboard={dashboard} /> : null}
            </div>
          ) : null}
        </DashboardRow>
        {isOverview ? (
          <OverviewDashboard
            surveyId={surveyId}
            initiativeId={initiativeId}
            initiative={initiative}
            dashboard={dashboard}
            filters={filters}
          />
        ) : (
          <Summary canManage={canManage} initiativeId={initiativeId} initiative={initiative} dashboard={dashboard} filters={filters} />
        )}
      </Dashboard>
    </>
  );
};
