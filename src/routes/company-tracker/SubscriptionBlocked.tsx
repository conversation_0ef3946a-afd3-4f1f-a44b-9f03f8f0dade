/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React, { useEffect, useState } from 'react';
import Dashboard, { DashboardSection } from '../../components/dashboard';
import { RootInitiativeData, Subscription } from '../../types/initiative';
import TrialEndImage from '../../images/ctl/ctl_calendar.svg';
import SubscriptionEndImage from '../../images/ctl/ctl_creditcard.svg';
import { Button } from 'reactstrap';
import G17Client from '../../services/G17Client';
import { ProductCodes, SubscriptionService } from '../../services/SubscriptionService';
import { currentTimestamp } from '../../utils/date';
import queryString from 'query-string';
import { useLocation } from 'react-router-dom';
import { reloadInitiative } from '../../actions/initiative';
import RequestDemoModal from '../../components/request-demo-modal';
import { addSiteAlert, SiteAlertColors } from '../../slice/siteAlertsSlice';
import { useAppDispatch, useAppSelector } from '../../reducers';
import { isOrgManager } from '../../selectors/user';
import { ReferralContainerForm } from '../../components/referrals/ReferralContainerForm';
import { loggerMessage } from '../../logger';

interface SubscriptionProps {
  organization: RootInitiativeData;
  initiativeId: string;
  appName: string
  productCode: ProductCodes;
  subscriptions?: Subscription[];
}

interface ContentProps {
  isManager: boolean;
  subscription?: Subscription
  productCode: ProductCodes;
  appName: string
  onFeedbackClick: () => void;
}

const SubscriptionNonAdmin = (props: { title: string}) => {
  const { title } = props;
  return (
    <>
      <h1 className={'h2 text-ThemeAccentExtradark'}>{title}</h1>

      <div className='image-container mb-2'>
        <img src={SubscriptionEndImage} alt={'Subscription required'} style={headerImageStyle} />
      </div>

      <p className={'text-ThemeTextDark mb-3'} style={imageStyle}>
        This may be because your subscription has lapsed.
      </p>

      <p className={'text-ThemeTextDark mb-3'} style={imageStyle}>
        If you require access, please contact someone in your organisation who has admin
        permissions and ask them to make payment.
      </p>
    </>
  )
}

const TrialEndedContent = (props: ContentProps) => {

  const title = 'Your trial period has come to an end';
  if (!props.isManager) {
    return <SubscriptionNonAdmin title={title} {...props} />
  }

  return <>
    <h1 className={'h2 text-ThemeAccentExtradark'}>{title}</h1>

    <div className='image-container mb-2'>
      <img src={TrialEndImage} alt={'Trial End'}
           style={{ maxWidth: '300px' }} />
    </div>

    <p className={'text-ThemeTextDark'}
       style={{
         maxWidth: '300px',
         margin: '0 auto',
         verticalAlign: 'baseline'
       }}>
      Thank-you for trying {props.appName}.
      Please select one of the options below or send us some
      <Button color='link' size={'sm'} onClick={props.onFeedbackClick}
              style={{ verticalAlign: 'baseline', padding: '0 0 0 .2rem' }}>
        feedback
      </Button>.
    </p>
  </>;
};

const imageStyle = {
  maxWidth: '400px',
  margin: '0 auto',
  verticalAlign: 'baseline',
};

const headerImageStyle = { maxWidth: '300px' };

const SubscriptionMissing = ({ appName }: Pick<ContentProps, 'appName'>) => {
  return <>
    <h1 className={'h2 text-ThemeAccentExtradark'}>Subscription required for {appName}</h1>

    <div className='image-container mb-2'>
      <img src={SubscriptionEndImage} alt={'Subscription required'} style={headerImageStyle} />
    </div>

    <p className={'text-ThemeTextDark mb-3'} style={imageStyle}>
      This may be because you haven’t paid this month,
      or your credit card has expired/payment failed.
    </p>

    <p className={'text-ThemeTextDark mb-3'} style={imageStyle}>
      Please go to the payment page to try again or contact us below.
    </p>
  </>;
};

const SubscriptionNeeded = (props: ContentProps) => {
  const { subscription, onFeedbackClick } = props;

  const title = 'Your subscription has ended';
  if (!props.isManager) {
    return <SubscriptionNonAdmin title={title} {...props} />
  }

  if (!subscription) {
    return <SubscriptionMissing appName={props.appName} />
  }

  return <>
    <h1 className={'h2 text-ThemeAccentExtradark'}>{title}</h1>

    <div className='image-container mb-2'>
      <img src={SubscriptionEndImage} alt={'Subscription required'} style={headerImageStyle} />
    </div>

    <p className={'text-ThemeTextDark mb-3'} style={imageStyle}>
      This may be because you have cancelled your subscription,
      or your credit card has expired/payment failed.
    </p>

    <p className={'text-ThemeTextDark mb-3'} style={imageStyle}>
      If you require access, please go to the payment page
      or add referral code
      to correct the issue or contact us below.
    </p>
    <p className={'text-ThemeTextDark'} style={imageStyle}>
      If you have cancelled, it would be much appreciated if
      you could let us know why {props.appName} didn’t work for you.

    </p>
    <div>
    <Button color='link' onClick={onFeedbackClick}>
      Provide feedback
    </Button>
    </div>
  </>;
};

const trialEndedWithinOneWeek = (subscription: undefined | Subscription) => {

  if (!subscription || subscription.status !== 'trialing') {
    return false;
  }

  const sevenDays = 7 * 24 * 3600;
  return (subscription.trialEnd ?? 0) + sevenDays >= currentTimestamp()
};

export function SubscriptionBlocked(props: SubscriptionProps) {
  const { initiativeId, subscriptions, productCode, appName, organization } = props;

  const location = useLocation();
  const dispatch = useAppDispatch();
  const { checkout, session_id } = queryString.parse(location.search);

  const isManager = useAppSelector(isOrgManager)
  const [view, setView] = useState<'default' | 'referral'>('default')
  const [isOpen, setIsOpen] = useState(false);
  const toggleRequestDemoModal = () => setIsOpen(open => !open);

  // Reload on success
  useEffect(() => {
    if (checkout === 'success' && session_id) {
      dispatch(reloadInitiative());
      const timeout = setInterval(() => dispatch(reloadInitiative()), 5000);
      return () => clearInterval(timeout);
    }
  }, [checkout, dispatch, session_id])

  useEffect(() => {
    loggerMessage(`Customer "${organization.name}" received SubscriptionBlocked for ${appName}`, {
      level: 'info',
      appName,
      organization,
    });
  }, [appName, organization]);

  const manageSubscription = () => {
    const recoverableSubscription = SubscriptionService.getBestValidSubscription(productCode, subscriptions);
    const apiCall = recoverableSubscription ?
      G17Client.createCustomerPortal({ initiativeId, returnUrl: location.pathname }) :
      G17Client.createCheckoutSession({
        initiativeId,
        productCodes: [productCode],
        returnUrl: location.pathname,
      });

    apiCall.then(s => window.open(s.url, '_blank', ''))
      .catch((e: Error) => {
        dispatch(addSiteAlert({
          content: e.message,
          color: SiteAlertColors.Danger,
        }))
      })
  };

  const subscription = SubscriptionService.getBestFromAllSubscription(productCode, subscriptions);

  const contentProps: ContentProps = {
    isManager,
    productCode,
    appName,
    subscription,
    onFeedbackClick: toggleRequestDemoModal,
  }


  const renderBody = () => {

    if (view === 'referral') {
      return <div className={'text-left d-lg-flex justify-content-center'}>
        <div style={{ maxWidth: '360px' }}>
          <ReferralContainerForm
            backBtn={
              <Button color='link-secondary' onClick={() => setView('default')}>
                Back
              </Button>
            }
            onSubmit={() => setView('default')}
            org={organization}
            productCode={productCode} />
        </div>
      </div>
    }

    return <>
      {trialEndedWithinOneWeek(subscription) ?
        <TrialEndedContent {...contentProps} /> :
        <SubscriptionNeeded {...contentProps} />}
      {contentProps.isManager ?
        <>
          <Button color='link-secondary' className='m-3' onClick={() => setView('referral')}>
            Add referral code
          </Button>
          <Button color='primary' className='m-3' onClick={manageSubscription}>
            Go to payment page
          </Button>
        </>
        : null}
    </>
  }

  return (
    <Dashboard hasSidebar={false}>
      <DashboardSection>
        <div className={'text-center'}>
          {renderBody()}
          <div className='mt-3'>
            <Button onClick={toggleRequestDemoModal} color='link-secondary' className='text-xs mr-2'>
              Contact Us
            </Button>
          </div>
        </div>
      </DashboardSection>
      <RequestDemoModal
        isOpen={isOpen}
        title={'Contact us'}
        toggle={toggleRequestDemoModal}
      />
    </Dashboard>
  );
}
