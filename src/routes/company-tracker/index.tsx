/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useCallback, useEffect } from 'react';
import { matchPath } from 'react-router';
import { getRootAppPath, isValidCompanyTrackerRoot, rootAppPath } from './utils';
import { Route, Switch, useHistory, useRouteMatch } from 'react-router-dom';
import { reportRoutes } from './ReportingRoute';
import { AppRouteInterface, RouteInterface, RoutesInterface } from '../../types/routes';
import { CompanyTrackerInitiativeRoute, companyTrackerInitiativeRoutes } from './CompanyTrackerInitiativeRoute';
import { RouteErrorBoundary } from '@features/error';
import UTrModal from '../../components/utr-modal';
import Dashboard, { DashboardSection } from '../../components/dashboard';
import { getInitiativeTree } from '../../selectors/initiativeTree';
import { OrganizationSwitcherRoute } from '../OrganizationSwitcherRoute';
import { companyTrackerRouteMatcher } from '../appRootMatcher';
import RedirectToCompanyTracker from './RedirectToCompanyTracker';
import { getDefaultConfig } from '../../config/app-config';
import { AgreementChecker } from '@features/agreement';
import { useAppDispatch, useAppSelector } from '../../reducers';
import { loadSurveyList, selectMostRecentSurvey } from '../../slice/initiativeSurveyListSlice';
import './styles.scss';
import { SURVEY } from '@constants/terminology';

// Do not require initiative
export const companytrackerRouteEntry: RouteInterface = {
  id: 'company_tracker_root',
  label: SURVEY.CAPITALIZED_SINGULAR,
  tooltip:  SURVEY.CAPITALIZED_SINGULAR,
  path: `/${rootAppPath}`,
  icon: 'fa-tasks',
  component: CompanyTracker,
  appPermissionId: 'app_company_tracker',
  exact: false,
  auth: true,
  getRootAppPath: getRootAppPath
};

const redirector = {
  COMPANY_TRACKER_SURVEY_REDIRECTOR: {
    id: 'company_tracker_survey_redirector',
    label: `${SURVEY.CAPITALIZED_SINGULAR} Redirect`,
    path: `/${rootAppPath}/redirect/:initiativeId/:destination(survey)`,
    defaultParams: {
      destination: 'survey'
    },
    component: RedirectToCompanyTracker,
    appPermissionId: 'app_company_tracker',
    auth: true,
    hideOnMissingPermission: true,
    requiresInitiativeId: true,
    parentId: 'company_tracker',
    getRootAppPath: getRootAppPath
  }
}

export const companyTrackerRoutes: RoutesInterface = {
  COMPANY_TRACKER: companytrackerRouteEntry,
  ...redirector,
  ...reportRoutes,
  ...companyTrackerInitiativeRoutes,
}

const branding = getDefaultConfig().branding.ctlApp;
const companyTrackerAppRoute: AppRouteInterface = {
  appName: branding.name,
  appIcon: branding.logo,
  routes: [
    reportRoutes.COMPANY_TRACKER_SURVEY,
    companyTrackerRoutes.COMPANY_TRACKER_LIST,
    companyTrackerRoutes.SUMMARY,
    companyTrackerRoutes.DOWNLOADS,
    companyTrackerRoutes.ADMIN_SETTINGS,
  ],
  hasReportingLevels: true,
}

const pathMatch = `${companytrackerRouteEntry.path}/:anyPage?/:initiativeId?`;

// Extract all routes that allow initiative change
const routesWithSwitch = Object.values(companyTrackerRoutes).filter(r => r.allowInitiativeChange);

/**
 * Company Tracker App entry point
 */
export function CompanyTracker() {

  const history = useHistory();
  const dispatch = useAppDispatch();
  const match = useRouteMatch<{ initiativeId: string }>({ path: pathMatch });
  const initiativeId = match?.params?.initiativeId;

  const initiativeTree = useAppSelector(getInitiativeTree);
  const currentSurvey = useAppSelector(selectMostRecentSurvey);
  useEffect(() => {
    if (initiativeId) {
      dispatch(loadSurveyList(initiativeId));
    }
  }, [initiativeId, dispatch]);

  const redirectRoute = routesWithSwitch.find(route => matchPath(history.location.pathname, route))
    ?? companyTrackerRoutes.SUMMARY;

  const redirectHandler = useCallback((initiativeId: string, rootAppPath?: string) => {

    if (initiativeId) {
      if (rootAppPath && !isValidCompanyTrackerRoot(rootAppPath)) {
        // @TODO fix the routing for MAT
        // Must be wrong root app path, try to redirect, rather than fail (MAT access etc.)
        history.push(`/${rootAppPath}/${initiativeId}`)
        return;
      }

      history.push(companyTrackerRouteMatcher.generateUrl(initiativeId, rootAppPath))
    }
  }, [history]);

  const defaultParams: AppRouteInterface['defaultParams'] = {
    initiativeId,
    surveyId: currentSurvey?._id,
  }

  return (
    <RouteErrorBoundary>
      <OrganizationSwitcherRoute
        appRoute={{
          ...companyTrackerAppRoute,
          defaultParams
        }}
        routeMatcher={companyTrackerRouteMatcher}
        changeReportingLevelRoute={redirectRoute}
        redirectHandler={redirectHandler}
        initiativeTree={initiativeTree}
        pathMatch={pathMatch}>
        <AgreementChecker />
        <Switch>
          <Route path={redirector.COMPANY_TRACKER_SURVEY_REDIRECTOR.path}>
            <redirector.COMPANY_TRACKER_SURVEY_REDIRECTOR.component />
          </Route>

          {/*"/company-tracker/reports/:initiativeId?",*/}
          <Route {...reportRoutes.COMPANY_TRACKER_LIST} />
          <Route {...reportRoutes.COMPANY_TRACKER_SURVEY} />
          {/* Normal Initiative*/}
          <Route path={pathMatch} component={CompanyTrackerInitiativeRoute} />
          <Route>
            <Dashboard>
              <DashboardSection icon={'fa-tasks'} title='Company Tracker'>
                <p>You currently don't have any Companies associated with this account.</p>
                <p>Please contact our customer success team to set up your account.</p>
              </DashboardSection>
            </Dashboard>
          </Route>
        </Switch>
        <UTrModal />
      </OrganizationSwitcherRoute>
    </RouteErrorBoundary>
  )
}

export default CompanyTracker;
