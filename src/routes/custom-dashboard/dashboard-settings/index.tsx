import { Button } from 'reactstrap';
import { InsightDashboard, InsightDashboardFilters, InsightDashboardSettingKeys, MetricStatusOption } from '@g17eco/types/insight-custom-dashboard';
import { useToggle } from '@hooks/useToggle';
import { DataPeriods } from '@g17eco/types/universalTracker';
import { DashboardSettingsSidebar } from './DashboardSettingsSidebar';
import type { DashboardSettingsProps } from '@features/custom-dashboard';

interface Props {
  dashboard: Pick<InsightDashboard, 'title' | 'filters' | 'type' | 'items' | 'initiativeId'>;
  handleSave: (dashboard: Partial<InsightDashboard>, keepEditing?: boolean) => void;
  handleDelete?: () => void;
  hideOptions?: InsightDashboardSettingKeys[];
  availablePeriods: DataPeriods[];
  metricStatusOptions?: MetricStatusOption[];
  disabled?: boolean;
  components?: {
    [key in InsightDashboardFilters]?: (props: DashboardSettingsProps) => React.ReactNode;
  };
}

export const DashboardSettings = ({
  dashboard,
  hideOptions = [],
  availablePeriods,
  handleSave,
  handleDelete = () => {},
  metricStatusOptions,
  disabled = false,
  components,
}: Props) => {
  const [openSettingsSidebar, toggleSettingsSidebar, setOpenSettingsSidebar] = useToggle(false);
  const dashboardKey = '_id' in dashboard ? dashboard._id : dashboard.type;

  return (
    <>
      <Button
        tooltip='Select the data being pulled'
        color='secondary'
        className='px-2 ml-2'
        onClick={() => setOpenSettingsSidebar(true)}
        disabled={disabled}
      >
        <i className='fa-light fa-gear fs-6 mr-1' /> Settings
      </Button>
      <DashboardSettingsSidebar
        key={`${dashboard.initiativeId}-${dashboardKey}`}
        isOpenSidebar={openSettingsSidebar}
        toggleSidebar={toggleSettingsSidebar}
        dashboard={dashboard}
        handleSave={handleSave}
        handleDelete={handleDelete}
        hideOptions={hideOptions}
        availablePeriods={availablePeriods}
        metricStatusOptions={metricStatusOptions}
        components={components}
      />
    </>
  );
};
