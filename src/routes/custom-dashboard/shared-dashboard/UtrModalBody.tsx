/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { useState } from 'react';
import { ViewRadioButtons } from '../../../components/utr-modal/ViewRadioButtons';
import { ShowAs, UniversalTrackerModalServiceUtrv } from '../../../reducers/universal-tracker-modal';
import { canAddTarget } from '../../../utils/universalTracker';
import { ContentTabs, Tab } from '../../../components/utr-modal/ContentTabs';
import { ChartComponent } from '../../../components/utr-modal/components/chart';
import Dashboard, { DashboardRow } from '../../../components/dashboard';
import { CollapsibleQuestionInfo } from '../../../components/utr-modal/CollapsibleQuestionInfo';
import { AdditionalInfo } from '../../../components/utr-modal/AdditionalInfo';
import UniversalTracker, { UniversalTrackerBase } from '../../../model/UniversalTracker';
import { DataTable } from '../../../apps/company-tracker/components/utr-modal/DataTable';
import { UtrValueType } from '@g17eco/types/universalTracker';
import { ValueListPlain } from '@g17eco/types/valueList';
import { TOTAL_OPTION } from '@components/utr-modal/ColumnFilter';

interface UtrModalContainerProps {
  utr: UniversalTracker;
  utrvs: UniversalTrackerModalServiceUtrv[];
  firstValueListCode?: string;
  valueList?: ValueListPlain;
}

export const UtrModalBody = (props: UtrModalContainerProps) => {
  const { utr, utrvs, firstValueListCode: selectedColumnCode, valueList } = props;

  const getColumnName = () => {
    if (!selectedColumnCode) {
      return;
    }
    if (utr.getValueType() === UtrValueType.NumericValueList && valueList) {
      return selectedColumnCode === TOTAL_OPTION.value
        ? TOTAL_OPTION.label
        : valueList.options.find((op) => op.code === selectedColumnCode)?.name;
    }
    return utr.getValueValidation().table?.columns.find((col) => col.code === selectedColumnCode)?.name;
  };

  const columnName = getColumnName();

  const utrPlain: UniversalTrackerBase = utr.getRaw();
  const canDrawChart = canAddTarget(utrPlain);

  const [activeTab, setActiveTab] = useState<Tab['navId']>(ShowAs.Chart);

  const viewTabs: Tab[] = [
    {
      navId: ShowAs.Chart,
      disabled: !canDrawChart,
      name: 'Chart',
      component: <ChartComponent utr={utr} utrvs={utrvs} selectedColumnCode={selectedColumnCode} />,
    },
    {
      navId: ShowAs.Table,
      name: 'Table',
      component: (
        <DashboardRow>
          <DataTable selectedColumnCode={selectedColumnCode} utr={utr} utrvs={utrvs} hideProvenance />
        </DashboardRow>
      ),
    },
  ];

  return (
    <Dashboard className='utr-modal-container'>
      <CollapsibleQuestionInfo utr={utr} />

      <DashboardRow mb={0}>
        <div className='w-100'>
          <div className='w-100 d-flex flex-wrap justify-content-between align-items-center'>
            <ViewRadioButtons tabs={viewTabs} activeTab={activeTab} handleChangeTab={setActiveTab} />
          </div>
          {columnName ? (
            <div className='mt-3'>
              <strong>Metric:</strong> {columnName}
            </div>
          ) : null}
        </div>
      </DashboardRow>

      <ContentTabs tabs={viewTabs} activeTab={activeTab} />

      <AdditionalInfo utrPlain={utrPlain} />
    </Dashboard>
  );
};
