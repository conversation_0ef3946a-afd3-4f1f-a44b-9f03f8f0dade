/*!
 * Copyright (c) 2019. World Wide Generation Ltd
 */

@import 'src/css/variables';

.mindmap-wrapper {
  flex: 1;
  overflow: hidden;
  position: relative;
  font-size: 10px;

  background-color: var(--theme-BgMedium);

  h1 {
    color: var(--theme-TextDark);
    position: relative;
    z-index: 1;
  }

  .mindmap {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    margin: 0 calc(-1 * $grid-gutter-width-base/2);

    > svg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    .node {
      foreignObject {
        fill: var(--theme-TextWhite);
      }
      .btn-link {
        color: var(--theme-TextMedium);
        border-radius: 2px;
        &:hover {
          background-color: var(--theme-BorderDefault);
        }
        &:focus {
          color: var(--theme-TextDark);
          border: 1px solid var(--theme-TextDark);
        }
      }
      circle {
        fill: var(--theme-TextWhite);

        text {
          color: #fff;
        }
      }

      &.initiative {
        circle {
          fill: var(--theme-AccentMedium);
        }
      }

      &.fund {
        circle {
          fill: var(--theme-SuccessMedium);
        }
      }

      &.project {
        circle {
          fill: var(--theme-DangerMedium);
        }
      }
    }

    .link {
      stroke: var(--theme-NeutralsDark);
      opacity: 25%;
      stroke-width: 2px;
      fill: none;
    }
  }

  .mindmap-disabled-text {
    fill: var(--theme-TextPlaceholder);
  }

  .mindmap-circle-text {
    cursor: pointer;
    font-size: 1.5em;
  }

  .mindmap-text {
    border: var(--theme-NeutralsDark);
    cursor: pointer;

    h6 {
      color: var(--theme-TextMedium);
    }

    text-decoration: none;

    &:hover {
      text-decoration: none;
    }
  }

  .mindmap-text-wrap {
    background: var(--theme-TextWhite);
    border-left: 8px solid rgb(0, 85, 255);
    padding-left: 10px;
  }

  .mindmap-section {
    fill: var(--theme-TextPlaceholder);
    font-size: 12px;
  }

  .mindmap-expand {
    fill: var(--theme-ColourBlack);
    font-size: 25px;
    pointer-events: none;
  }

  .mindmap-menu {
    position: absolute;
    top: 0;
    right: 0;
    padding: 15px 20px;
    z-index: 1;
    background-color: rgba(var(--theme-TextMedium));
    color: var(--theme-TextMedium);
    line-height: 30px;
    font-size: 13px;

    .col {
      &:first-of-type {
        .fas,
        .fa-light {
          margin-right: 5px;
          margin-left: 0;
        }
      }
    }

    button {
      background: none;
      color: var(--theme-TextMedium);
      border: none;
      transition: calc($transitionTime/2) color;
      cursor: pointer;

      &:disabled {
        cursor: not-allowed;
        opacity: 0.2;
      }

      &:hover,
      &:active,
      &:focus {
        color: var(--theme-HeadingLight);
      }
    }

    .fas,
    .fa-light {
      color: var(--theme-IconSecondary);
      vertical-align: top;
      font-size: 24px;
      line-height: 30px;
      margin-left: 10px;
    }

    .expand-actions {
      border-left: 1px solid var(--theme-BorderDefault);
    }
  }

  .backButton {
    left: 0;
    padding: 0.94rem 1.25rem;
    position: absolute;
    z-index: 1;
    font-size: 12px;

    button {
      line-height: 1.87rem;
      padding: 0;
      cursor: pointer;

      &:hover,
      &:active,
      &:focus {
        color: var(--theme-HeadingLight);
      }
    }
  }

  @keyframes contactAnimation {
    to {
      opacity: 1;
    }
  }

  @keyframes nodeAnimation {
    to {
      opacity: 1;
    }
  }

  @keyframes linkAnimation {
    to {
      opacity: 0.2;
    }
  }

  .contact-us__wrapper {
    width: fit-content;
    margin: 20px auto;
    font-weight: 500;
    color: var(--theme-ColourBlack);

    .btn {
      z-index: 1;
      animation: contactAnimation 1s 4s forwards;
      opacity: 0;
    }
  }

  .subscription-info-wrapper {
    width: fit-content;
    position: absolute;
    bottom: 20px;
    right: 40px;
  }

  .subscription-warning-wrapper {
    margin: 20px auto;
    width: fit-content;
  }

  .details {
    color: var(--theme-ColourBlack);
    width: 100%;
    position: fixed;
    bottom: 3vh;
    animation: contactAnimation 1s 4s forwards;
    opacity: 0;
  }

  //vertical center
  .initiative-modal {
    max-width: 300px; //bootstrap modal-sm is 300
    --bs-modal-width: 300px; //bootstrap modal-sm is 300
    width: 300px; //bootstrap modal-sm is 300
    -webkit-transform: translate(0, -50%);
    -o-transform: translate(0, -50%);
    transform: translate(0, -50%);
    top: 50%;
    margin: 0 auto;

    input {
      width: 100%;
    }

    .modal-body {
      padding: 16px; //adjusted for smaller size
    }
  }

  .expand-button {
    background: var(--theme-NeutralsExtralight);
    border: 1px solid var(--theme-NeutralsLight);
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    .fal {
      color: var(--theme-IconSecondary);
    }
  }

  .expand-line {
    background-color: var(--theme-NeutralsDark);
    opacity: 25%;
    transform: translate(0, 1.25rem);
  }

  .dropdown_menu {
    background-color: white;
    border: 1px solid var(--theme-BorderDefault);
    border-radius: 4px;
  }

  ul {
    li {
      list-style-type: none;
      &:hover {
        background-color: var(--theme-BorderDefault);
        cursor: pointer;
      }
      &.disabled {
        color: var(--theme-TextPlaceholder);
        cursor: not-allowed;
        i {
          color: var(--theme-TextPlaceholder) !important;
        }
      }
    }
  }
}
