/*!
 * Copyright (c) 2019. World Wide Generation Ltd
 */

@import "../../css/variables";

.mindmap-wrapper {
  flex: 1;
  overflow: hidden;
  position: relative;
  background-color: var(--theme-ColourBlack);
  font-size: 10px;

  h1 {
    color: var(--theme-TextWhite);
    position: relative;
    z-index: 1;
    background-color: rgba(var(--theme-ColourBlack), 0.5);
  }

  .mindmap {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    margin: 0 calc(-1 * $grid-gutter-width-base/2);

    > svg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    .node {
      circle {
        fill: var(--theme-TextWhite);

        text {
          color: #fff;
        }
      }

      &.initiative {
        circle {
          fill: var(--theme-AccentMedium);
        }
      }

      &.fund {
        circle {
          fill: var(--theme-SuccessMedium);
        }
      }

      &.project {
        circle {
          fill: var(--theme-DangerMedium);
        }
      }
    }

    .link {
      stroke: var(--theme-NeutralsMedium);
      opacity: 25%;
      stroke-width: 2px;
      fill: none;
    }
  }

  .mindmap-disabled-text {
    fill: var(--theme-TextPlaceholder);
  }

  .mindmap-circle-text {
    cursor: pointer;
    font-size: 1.5em;
  }

  .mindmap-text {
    cursor: pointer;
    fill: var(--theme-TextWhite);
    text-decoration: underline;

    &:hover {
      text-decoration: none;
    }
  }

  .mindmap-section {
    fill: var(--theme-TextPlaceholder);
    font-size: 12px;
  }

  .mindmap-expand {
    fill: var(--theme-ColourBlack);
    font-size: 25px;
    pointer-events: none;
  }

  .mindmap-menu {
    position: absolute;
    top: 0;
    right: 0;
    padding: 15px 20px;
    z-index: 1;
    background-color: rgba(var(--theme-ColourBlack), 0.5);
    color: var(--theme-TextWhite);
    line-height: 30px;
    font-size: 12px;

    .col {
      border-left: 1px solid var(--theme-TextWhite);

      &:first-of-type {
        border: none;

        .fas {
          margin-right: 5px;
          margin-left: 0;
        }
      }
    }

    button {
      background: none;
      border: none;
      color: var(--theme-TextWhite);
      transition: calc($transitionTime/2) color;
      line-height: 30px;
      padding: 0;
      cursor: pointer;

      &:disabled {
        cursor: not-allowed;
        opacity: 0.2;
      }

      &:hover,
      &:active,
      &:focus {
        color: var(--theme-HeadingLight);
      }
    }

    .fas {
      vertical-align: top;
      font-size: 20px;
      line-height: 30px;
      margin-left: 10px;
    }
  }

  .backButton {
    left: 0;
    padding: 0.94rem 1.25rem;
    position: absolute;
    z-index: 1;
    font-size: 12px;
    button {
      color: var(--theme-TextWhite);
      line-height: 1.87rem;
      padding: 0;
      cursor: pointer;
      &:hover,
      &:active,
      &:focus {
        color: var(--theme-HeadingLight);
      }
    }
  }
  @keyframes contactAnimation {
    to {
      opacity: 1;
    }
  }

  @keyframes nodeAnimation {
    to {
      fill-opacity: 1;
    }
  }

  @keyframes linkAnimation {
    to {
      opacity: 0.2;
    }
  }

  .contact-us__wrapper {
    width: fit-content;
    margin: 20px auto;
    font-weight: 500;
    color: var(--theme-TextWhite);
    .btn {
      z-index: 1;
      animation: contactAnimation 1s 4s forwards;
      opacity: 0;
    }
  }

  .details {
    color: var(--theme-TextWhite);
    width: 100%;
    position: fixed;
    bottom: 3vh;
    animation: contactAnimation 1s 4s forwards;
    opacity: 0;
  }

  //vertical center
  .modal .modal-dialog.initiative-modal {
    max-width: 250px; //bootstrap modal-sm is 300
    --bs-modal-width: 250px; //bootstrap modal-sm is 300
    width: 250px; //bootstrap modal-sm is 300
    -webkit-transform: translate(0, -50%);
    -o-transform: translate(0, -50%);
    transform: translate(0, -50%);
    top: 50%;
    margin: 0 auto;
    .modal-content .modal-header {
      padding: 0.2rem 1rem;
    }
    input {
      width: 100%;
    }
    .modal-body {
      padding: 16px; //adjusted for smaller size
    }
  }
}
