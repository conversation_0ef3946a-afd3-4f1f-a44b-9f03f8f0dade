/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { Component } from 'react';
import { Link, withRouter, RouteComponentProps } from 'react-router-dom';
import { connect, ConnectedProps } from 'react-redux';
import { hidePermissionDenied } from '../../actions/auth';
import { RootState } from '../../reducers';
import CONFIG from '../../config';
import Dashboard, { DashboardSection } from '../../components/dashboard';

interface NotAuthorisedProps extends PropsFromRedux {
  message?: JSX.Element;
  hidePermissionDenied: () => any;
  showPermissionDenied: boolean;
}

class NotAuthorised extends Component<NotAuthorisedProps & RouteComponentProps> {

  render() {
    const { message } = this.props;

    return (
      <div className='d-flex'>
        <Dashboard className='flex-fill' >
          <DashboardSection
            title={'Not Authorised'}
            headingStyle={4}
          >
            <p>{message ?? 'You do not have access to the selected resource.'}</p>
            <p>If you think this is an error, please
              &nbsp;<a href={`mailto:${CONFIG.emailSupport}&subject=Permission%20Request`}>contact our support</a>.</p>
            <Link to='/' onClick={this.props.hidePermissionDenied}>Go to homepage</Link>
          </DashboardSection>
        </Dashboard>
      </div>
    )
  }
}

const mapStateToProps = (state: RootState) => ({
  showPermissionDenied: state.authentication.showPermissionDenied,
});

const mapDispatchToProps = {
  hidePermissionDenied
}

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

export default withRouter(connector(NotAuthorised));
