/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import Dashboard, { DashboardSection } from '../../components/dashboard';
import { Button } from 'reactstrap';
import { useHistory } from 'react-router-dom';
import Eyes from '../../images/eyes.gif';

export const NotFound = () => {
  const history = useHistory();

  document.title='Oh no! We can\'t find that page. It\'s a 404 error. - G17Eco';

  return (
    <Dashboard>
      <DashboardSection>
        <div className='row'>
          <div className='col-12 col-sm-3 text-sm-center' style={{ margin: 'auto 0' }}>
            <img src={Eyes} alt={'404'} width={180} />
          </div>
          <div className='mt-4 mt-sm-0 ps-5 col'>
            <div style={{ fontWeight: '700', fontSize: '32px', lineHeight: '38px', letterSpacing: '-2%' }}>
              <div className='text-ThemeAccentExtradark'>Oh no!</div>
              <div>
                <span className='text-ThemeAccentExtradark'>We can't find that page. </span>
                <span className='text-ThemeTextPlaceholder'>It's a 404 error.</span>
              </div>
            </div>
            <div className='mt-3 text-ThemeTextMedium text-lg'>
              We looked but can’t find it. Please double check the web address is correct. Please get in touch with us
              using the chat bubble (in the bottom right of the page) if you think there is an error. Thanks!
            </div>
            <div className='text-center text-sm-left'>
              <Button className='mt-4' onClick={() => history.push('/')}>
                Go to Homepage
              </Button>
            </div>
          </div>
        </div>
      </DashboardSection>
    </Dashboard>
  );
};
