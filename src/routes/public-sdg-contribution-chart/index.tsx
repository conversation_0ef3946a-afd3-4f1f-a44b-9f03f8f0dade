/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React from 'react';
import { useSelector } from 'react-redux';
import { useRouteMatch } from 'react-router-dom';
import { SdgChart } from '../../components/impact-performance/SdgChart';
import { Loader } from '@g17eco/atoms/loader';
import { RootState, useAppDispatch } from '../../reducers';
import { loadPublicScorecardsByInitiativeId } from '../../slice/publicScorecard';
import './style.scss';
import Dashboard, { DashboardSection } from '../../components/dashboard';

const PublicSDGContributionChart = () => {
  const match = useRouteMatch<{
    token?: string;
    initiativeId?: string;
  }>();
  const { initiativeId, token } = match.params;
  const scorecardState = useSelector((state: RootState) => state.publicScorecard);
  const { data: scorecard, errorMessage } = scorecardState;
  const dispatch = useAppDispatch();

  React.useEffect(() => {
    if (initiativeId && token) {
      dispatch(loadPublicScorecardsByInitiativeId(initiativeId, token));
    }
  }, [dispatch, initiativeId, token]);

  if (!initiativeId || !token) {
    return <></>;
  }

  if (errorMessage) {
    return (
      <Dashboard>
        <DashboardSection
          title='Not available'
          subtitle='It is currently not possible to access this resource.'
        ></DashboardSection>
      </Dashboard>
    );
  }

  if (!scorecard?.scorecard) {
    return <Loader />;
  }

  return (
    <div className='sdg-contribution-chart'>
      <SdgChart scorecard={scorecard} initiativeId={initiativeId} showDetail={false} showPoweredBy={true} />
    </div>
  );
};

export default PublicSDGContributionChart;
