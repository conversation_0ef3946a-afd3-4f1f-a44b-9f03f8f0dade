/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import React from 'react';
import Dashboard, { DashboardSection } from '../../components/dashboard';
import { useAppSelector } from '../../reducers';
import { isOrgManager } from '../../selectors/user';
import NotAuthorised from '../not-authorised';
import { Loader } from '@g17eco/atoms/loader';
import './ReferralsRoute.scss';
import { ReferralContainerForm } from '../../components/referrals/ReferralContainerForm';
import { useParams } from 'react-router-dom';
import { BasicAlert } from '@g17eco/molecules/alert';
import { useAppConfig } from '../../hooks/app/useAppSettings';

export const ReferralsRoute = () => {

  const { initiativeId } = useParams<{ initiativeId: string }>();
  const isManager = useAppSelector(isOrgManager)
  const { loaded, data } = useAppSelector(state => state.rootInitiatives)
  const appConfig = useAppConfig();

  if (!loaded) {
    return <Loader />
  }

  if (!isManager || !appConfig) {
    return <NotAuthorised />;
  }

  const org = data.find(o => o._id === initiativeId);

  return (
    <Dashboard className={'referrals-route'}>
      <DashboardSection>
        <div className='d-lg-flex justify-content-center'>
          {org ?
            <ReferralContainerForm org={org} productCode={appConfig.productCode} /> :
            <BasicAlert type={'warning'}>
              No referral codes can be applied at this moment
            </BasicAlert>}
        </div>
      </DashboardSection>
    </Dashboard>
  );
}
