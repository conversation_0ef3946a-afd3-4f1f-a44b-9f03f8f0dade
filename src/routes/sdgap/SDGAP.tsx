/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { Component } from 'react';
import { connect, ConnectedProps } from 'react-redux';
import { Loader } from '@g17eco/atoms/loader';
import ReportingFrameworkChart from '../../components/charts/reporting-framework-chart';
import SettingsSidebar, { SettingsSection } from '../../components/settings-sidebar';
import SDGIconComponent from '../../components/sdg-icon/sdg-icon';
import { loadReportingFrameworks } from '../../actions/reportingFrameworks';
import { loadUNSDGMap } from '../../actions/common';
import { selectByFramework } from '../../selectors/sdgap';
import { SunburstDataItem, Framework } from '../../reducers/reporting-frameworks';
import { RouteComponentProps, withRouter } from 'react-router-dom';
import { Button } from 'reactstrap';
import { naturalSort } from '../../utils';
import ClampLines from 'react-clamp-lines';
import Dashboard, { DashboardRow, DashboardSection } from '../../components/dashboard';
import { RootState } from '../../reducers';
import SdgTabs from './SdgTabs';
import { generateUrl } from '../util';
import { RouteInterface } from '../../types/routes';
import './style.scss';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';

interface TaxonomyState {
  data?: SunburstDataItem;
}

interface TaxonomyProps extends PropsFromRedux {
  match: {
    params: {
      [key: string]: string
    }
  };
  loadUNSDGMap: () => undefined | Promise<any>;
  frameworkMap: (code: string) => SunburstDataItem | undefined;
  route: RouteInterface;
}

class SDGAP extends Component<TaxonomyProps & RouteComponentProps, TaxonomyState> {
  state: TaxonomyState = {
    data: undefined
  };

  componentDidMount() {
    this.props.loadUNSDGMap();
    const { match, frameworkMap } = this.props;
    this.setState({ data: frameworkMap(match.params.type) });
  }

  componentDidUpdate(prevProps: TaxonomyProps) {
    const { match, frameworkMap, reportingFrameworksState } = this.props;
    const hasLoadingStateChanged = reportingFrameworksState.loaded && !prevProps.reportingFrameworksState.loaded;
    const hasTypeChanged = match.params.type !== prevProps.match.params.type;
    // const hasCodeChanged = match.params.code !== prevProps.match.params.code;

    if (hasTypeChanged || hasLoadingStateChanged) {
      this.setState({ data: frameworkMap(match.params.type) });
    }
  }

  render() {
    const { reportingFrameworksState, sdgInfoState, match } = this.props;
    const { type = 'sdg', code } = match.params;
    const { data } = this.state;
    const showSDGTabs = type === 'sdg' && !code;

    if (!reportingFrameworksState.loaded || !data || !sdgInfoState.loaded) {
      return (<Loader />);
    }

    return <Dashboard hasSidebar={true} className='sdgap-container d-flex'>
      <div className='sidebar-container'>
        {this.renderSidebar()}
      </div>

      {this.renderInfoBox()}
      <DashboardRow>
        <div className='position-relative sunburst-container mt-3 dont_translate' >
          {this.renderBackButton()}
          {this.renderChart()}
        </div>
      </DashboardRow>
      {this.renderFrameworkIssuesCovered()}
      {showSDGTabs ? <SdgTabs /> : <></>}
    </Dashboard >
  }

  goBack = () => {
    this.props.history.goBack();
  }

  renderBackButton = () => {
    const { type = 'sdg', code } = this.props.match.params;
    if (!code && type === 'sdg') {
      return;
    }
    return (
      <div className='position-absolute'>
        <Button onClick={this.goBack}>
          <i className='fa fa-chevron-left mr-2' />Back
        </Button>
      </div>
    );
  }

  renderChart = () => {
    const { match } = this.props;
    const { data } = this.state;

    if (!data?.children) {
      return (
        <div className='whiteBoxContainer p-4 d-flex align-items-center justify-content-center' style={{ height: '200px' }}>
          <span>
            There is not enough information about this framework at this time. Please check back later.
            </span>
        </div>
      );
    }

    return <ReportingFrameworkChart
      data={data}
      code={match.params.code}
      handleSetSelected={this.handleSetSelected}
    />
  }

  getAllFrameworks = () => {
    const { reportingFrameworksState } = this.props;
    const frameworks = reportingFrameworksState.data?.frameworks;
    const allFrameworks = frameworks ? [...frameworks] : [];
    allFrameworks.push({ code: 'sdg', name: 'Sustainable Development Goals (SDGs)' });
    allFrameworks.sort((a, b) => a.name > b.name ? 1 : a.name < b.name ? -1 : 0);
    return allFrameworks;
  }

  renderInfoBox = () => {
    const { type = 'sdg' } = this.props.match.params;

    return (
      <DashboardSection
        title='Sustainable Development Global Accounting Principles'>
        <ClampLines id='sdgap' text='The SDGAP is a digital taxonomy, with a unique visualization of mapping globally recognised
          standards, frameworks and policies to each other and to the backbone of the Sustainable
          Development Goals (SDGs) Targets. The mappings reflect the consensus arrived by the respective
          institutions but digitised into one database, so any institutions can easily map their metrics
          into the taxonomy. The SDGAP aims to guide members as to which metrics and data overlap and will
      best contribute to the impact on the SDGs.' moreText='more' lessText='less' lines={2} />
        {type !== 'sdg' ? (
          <div className='text-left mt-3'>
            <Button className='p-0' color='link' onClick={() => this.props.history.push(generateUrl(this.props.route))} >
              <i className='fa fa-chevron-left mr-2' />Back to Sustainable Development Goals
          </Button>
          </div>
        ) : <></>}
      </DashboardSection>
    );
  }

  renderSidebar = () => {
    return (
      <SettingsSidebar>
        {this.renderDropdowns()}
        {this.renderSDGInfoSections()}
        {this.renderFrameworksCoveredSection()}
      </SettingsSidebar>
    )
  }

  handleSetSelected = (selectedData: { data: { code: string, type: string } }) => {
    const { type = 'sdg', code = '' } = this.props.match.params;
    if (selectedData.data.type !== type || selectedData.data.code !== code) {
      this.handleRedirect(selectedData.data.type, selectedData.data.code);
    }
  }

  handleRedirect: (type: string, code?: string) => void = (type, code) => {
    this.props.history.push(generateUrl(this.props.route, { type, code }));
  }

  renderDropdowns = () => {
    const { type = 'sdg' } = this.props.match.params;

    const allFrameworks = this.getAllFrameworks();
    const filterOptions = allFrameworks.map(f => ({ type: f.type, value: f.code, label: f.name }));

    const frameworkOptions = filterOptions.filter(o => o.type === 'framework');
    const standardOptions = filterOptions.filter(o => !o.type || o.type === 'standard');
    const legislationOptions = filterOptions.filter(o => o.type === 'legislation');

    return (
      <SettingsSection
        title={'View By:'}
        icon='fa-bullseye'
      >
        <div className='border-bottom pb-2' />
        <SelectFactory
          selectType={SelectTypes.LabelSelect}
          labelProps={{ className: 'mt-2 text-ThemeAccentExtradark', title: 'Standard' }}
          placeholder='Select Standard'
          isClearable={true}
          options={standardOptions}
          value={type ? standardOptions.find(o => o.value === type) ?? null : null}
          onChange={(o) => this.handleRedirect(o?.value ?? '')}
        />
        <SelectFactory
          selectType={SelectTypes.LabelSelect}
          labelProps={{ className: 'mt-2 text-ThemeAccentExtradark', title: 'Framework' }}
          placeholder='Select Framework'
          isClearable={true}
          options={frameworkOptions}
          value={type ? frameworkOptions.find(o => o.value === type) ?? null : null}
          onChange={(o) => this.handleRedirect(o?.value ?? '')}
        />

        <SelectFactory
          selectType={SelectTypes.LabelSelect}
          labelProps={{ className: 'mt-2 text-ThemeAccentExtradark', title: 'Legislation' }}
          placeholder='Select Legislation'
          isClearable={true}
          options={legislationOptions}
          value={type ? legislationOptions.find(o => o.value === type) ?? null : null}
          onChange={(o) => this.handleRedirect(o?.value ?? '')}
        />
      </SettingsSection>
    );
  }

  getCurrentFrameworks = (code: string) => {
    const { reportingFrameworksState } = this.props;
    const { data } = this.state;

    const frameworksInScope = new Set();
    const addRecursiveFrameworks: (d: SunburstDataItem) => void = (d) => {
      frameworksInScope.add(d.type);
      if (!d.children) {
        return;
      }
      d.children.forEach(c => {
        if (c.type !== 'sdg') {
          frameworksInScope.add(c.type);
        }
        addRecursiveFrameworks(c);
      });
    }
    const findLeaf: (d: SunburstDataItem) => void = (d) => {
      if (d.code === code) {
        addRecursiveFrameworks(d);
        return;
      }
      if (d.children) {
        d.children.find(findLeaf);
      }
    }
    if (data) {
      findLeaf(data);
    }

    return reportingFrameworksState.data?.frameworks?.filter(c => frameworksInScope.has(c.code));
  }

  renderFrameworksCoveredSection = () => {
    const { code, type } = this.props.match.params;

    const frameworks = code ? this.getCurrentFrameworks(code) : this.getAllFrameworks();

    return (
      <SettingsSection title={'Coverage'} icon='fa-list'>
        {frameworks?.map(f => (
          <div key={`framework_${f.code}`} className={`border-bottom py-1 ${f.code === type ? 'background-ThemeAccentMedium' : ''}`}>
            <Button color='link' onClick={() => this.handleRedirect(f.code)}
              className={`d-block w-100 text-left ${f.code === type ? 'text-ThemeTextWhite' : 'text-ThemeHeadingLight'}`}>
              {f.code === type && <i className='float-end fa fa-chevron-right' />}
              {f.name}
            </Button>
          </div>
        ))}
      </SettingsSection>
    );
  }

  renderSDGInfoSections = () => {
    const { sdgInfoState } = this.props;
    const { code } = this.props.match.params;

    if (!code) {
      return <></>;
    }

    const sdgData = sdgInfoState.data;
    const [sdgGoal, sdgTarget] = code.split('.');

    const sdgGoalInfo = sdgData?.goals.find(i => i.code === sdgGoal);
    if (!sdgGoalInfo) {
      return <></>;
    }

    const sdgTargetInfo = sdgGoalInfo && sdgTarget && sdgGoalInfo.targets.find(i => i.code === `${sdgGoal}.${sdgTarget}`);

    return (
      <SettingsSection
        title={<>
          <SDGIconComponent code={sdgGoalInfo.code} width={40} height={40} className='mr-3' />SDG {sdgGoalInfo.code}
        </>
        }
        subtitle={sdgGoalInfo.title}
      >
        <div className='mt-3'>{sdgGoalInfo.description}</div>
        {sdgTargetInfo ? (
          <>
            <div className='mt-3 d-flex align-items-center'>
              <div>
                <SDGIconComponent code={sdgTargetInfo.code} width={40} height={40} className='mr-3' />
              </div>
              <div className='flex-fill'>
                <h2 className='my-0'>SDG {sdgTargetInfo.code}</h2>
              </div>
            </div>
            <div className='mt-3'>{sdgTargetInfo.title}</div>
          </>
        ) : <></>}
      </SettingsSection>
    );
  }

  renderFrameworkIssuesCovered = () => {
    const { reportingFrameworksState } = this.props;
    const { type, code } = this.props.match.params;

    const frameworks = code ? this.getCurrentFrameworks(code) : reportingFrameworksState.data?.frameworks?.filter(f => f.code === type);

    if (!frameworks || frameworks.length === 0) {
      return <></>;
    }

    const renderFrameworkIssuesCoveredByFramework = (frameworkData: Framework) => {
      const issuesCovered = code ? frameworkData.items?.filter(i => {
        if (i.sdgCodes.includes(code)) {
          return true;
        }
        return i.sdgCodes.some(sdgCode => sdgCode.indexOf(`${code}.`) === 0);
      }) : [];

      const hasIssues = issuesCovered && issuesCovered.length >= 0;

      return (
        <div key={`issue_group_${frameworkData.name}`} className='mt-3 mb-2'>
          <h2><i className='fa fa-bullseye mr-2' />{frameworkData.name}</h2>
          <div className='ml-3 pl-2'>
            <div className='mt-3 text-ThemeAccentExtradark'>{frameworkData.description}</div>
            {hasIssues && (
              <div className='mt-3'>
                {issuesCovered?.map((row, i) =>
                  <div className='border-bottom py-3' key={`issue_${i}_${row.itemCode}`}>
                    <h3>{this.getItemCode(row.itemCode, row.displayCode)}</h3>
                    <div>{row.itemName}</div>
                    <div className='text-muted'>SDG Codes: {row.sdgCodes.sort(naturalSort).join(', ')}</div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      );
    }

    return (
      <DashboardSection>
        {code ? (
          <div className='d-flex align-items-center pb-3 mb-2 border-bottom border-ThemeNeutralsLight'>
            <div>
              <SDGIconComponent code={code} width={40} height={40} className='mr-3' />
            </div>
            <div className='flex-fill'>
              <h1 className='my-0'>Issues Covered on SDG {code}</h1>
            </div>
          </div>
        ) : <></>}
        <>{frameworks.map(f => renderFrameworkIssuesCoveredByFramework(f))}</>
      </DashboardSection>
    );
  }

  getItemCode(itemCode: string, displayCode: string) {
    return displayCode ? displayCode : itemCode.split('/').pop()
  }

}

const mapStateToProps = (state: RootState) => ({
  reportingFrameworksState: state.reportingFrameworks,
  frameworkMap: (code: string) => selectByFramework(state)(code),
  sdgInfoState: state.UNSDGMap
});

const mapDispatchToProps = {
  loadReportingFrameworks,
  loadUNSDGMap
};

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>;

export default withRouter(connector(SDGAP));
