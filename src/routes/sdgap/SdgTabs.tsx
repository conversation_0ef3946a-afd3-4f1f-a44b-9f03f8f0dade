/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import classnames from 'classnames';
import React, { useState } from 'react'
import { useSelector } from 'react-redux';
import { Button, Col, Nav, NavItem, Row, TabContent, Table, TabPane } from 'reactstrap';
import { DashboardSection } from '../../components/dashboard';
import SDGIcon from '../../components/sdg-icon/sdg-icon';

interface UnSdgProps {
  code: string;
  description: string;
  targets: TargetsProps[];
  title: string;
}

interface TargetsProps {
  code: string;
  description: string;
  title: string;
}

const SdgTabs = () => {
  const unsdg = useSelector((state: any) => state.UNSDGMap.data.goals);
  const [activeTab, setActiveTab] = useState('1');

  const toggle = (tab: string) => {
    if (activeTab !== tab) {
      setActiveTab(tab);
    }
  }

  return (
    <div className='sdgTabs-container mt-3'>
      <DashboardSection
        title='The Sustainable Development Goals (SDGs)'
      >
        <Nav tabs>
          {unsdg.map((o: UnSdgProps) => {
            return <NavItem key={o.code}>
              <Button
                color='link'
                className={classnames({ active: activeTab === o.code }, 'mb-2 mr-3 dont_translate')}
                onClick={() => toggle(o.code)} >
                {`SDG ${o.code}`}
              </Button>
            </NavItem>
          })}
        </Nav>
        {unsdg.map((o: UnSdgProps) => (
          <TabContent className='mt-4' activeTab={activeTab} key={o.code}>
            <TabPane tabId={o.code}>
              <div className='d-flex flex-column flex-md-row'>
                <Row>
                  <Col className='mr-3 mb-3'>
                    <SDGIcon code={o.code} width={200} height={200} />
                  </Col>
                </Row>
                <Row>
                  <Col className='mb-3'>
                    <p className='title mb-2'>{o.title}</p>
                    <p className='description mb-1'>{o.description}</p>
                    <a href='https://www.globalgoals.org/' rel='noopener noreferrer' target='_blank'>More info <i className='fas fa-external-link-alt'></i></a>
                  </Col>
                </Row>
              </div>
            </TabPane>
            <TabPane tabId={o.code} >
              <Table>
                <thead className={`sdg-bg-${o.code}-5 header`}>
                  <tr>
                    <th scope='col' className='text-white targets'>Targets</th>
                    <th scope='col'></th>
                  </tr>
                </thead>
                <tbody>
                  {o.targets.map((target: TargetsProps, i) => {
                    return <tr key={`${o.code}-${i}`}>
                      <th scope='row' className='dont_translate'>{target.code}</th>
                      <td>{target.description}</td>
                    </tr>
                  })}
                </tbody>
              </Table>
            </TabPane>
          </TabContent>
        ))}
      </DashboardSection>

    </div>
  )
}


export default SdgTabs;
