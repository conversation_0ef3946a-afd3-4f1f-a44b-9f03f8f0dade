/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React from 'react';
import { useAppDispatch, useAppSelector } from '../../reducers';
import { useParams } from 'react-router-dom';
import { loadValueDetails } from '../../actions/universalTracker';
import Dashboard, { DashboardSection, DashboardColumn } from '../../components/dashboard';
import { Loader } from '@g17eco/atoms/loader';
import { HistoryTimelineComponent } from '../../components/value-history/HistoryTimelineComponent';
import UniversalTracker from '../../model/UniversalTracker';
import './style.scss';

const UniversalTrackerProvenance = () => {

  const dispatch = useAppDispatch();
  const params: { utrId: string, utrvId: string } = useParams();
  const { utrId, utrvId } = params;

  const universalTrackerValueDetails = useAppSelector((state) => state.universalTrackerValueDetails);

  React.useEffect(() => {
    dispatch(loadValueDetails(utrId, utrvId));
  }, [dispatch, utrId, utrvId]);


  if (universalTrackerValueDetails.errored) {
    return (
      <Dashboard className='universal-tracker-provenance'>
        <DashboardSection
          icon='fa-clipboard'
          title={'Universal Tracker Provenance'}
          subtitle={'This is a complete and immutable audit for the updates to this data point.'}
        >
          <div className='border-bottom mb-3' />
          <>You do not have permission to view the provenance for this data point. If you think this is an error, please contact our support team.</>
        </DashboardSection>
      </Dashboard>
    );
  }

  if (!universalTrackerValueDetails.loaded) {
    return <Loader />;
  }

  const { history, users, universalTracker: [universalTracker], documents, surveyType, sourceItems } = universalTrackerValueDetails.data;

  return (
    <Dashboard className='universal-tracker-provenance'>
      <DashboardSection
        icon='fa-clipboard'
        title={'Universal Tracker Provenance'}
        subtitle={'This is an immutable audit of the updates for this data entry.'}
      >
        <div className='border-bottom mb-3' />

        <DashboardColumn
          flexBasisPc={'100%'}
          icon='fa-universal-access'
          title={universalTracker.name}
          subtitle={universalTracker.valueLabel}
        >

          <HistoryTimelineComponent
            universalTracker={new UniversalTracker(universalTracker)}
            history={history}
            users={users}
            documents={documents}
            surveyType={surveyType}
            sourceItems={sourceItems}
          />
        </DashboardColumn>
      </DashboardSection>
    </Dashboard>
  );
}

export default UniversalTrackerProvenance;
