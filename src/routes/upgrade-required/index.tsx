/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Button } from 'reactstrap';
import Dashboard, { DashboardSection } from '../../components/dashboard';
import { UserInterestModal } from '../../components/user-interest-modal';

export const UpgradeRequired = () => {

  const [isOpen, setIsOpen] = useState(false);

    return (
      <div className='d-flex'>
        <Dashboard className='flex-fill' >
          <DashboardSection title={'Upgrade Required'} headingStyle={4}>
            <p>{'You need to upgrade to use this feature.'}</p>
            <p>If you think this is an error, please
              <Button color='link' className='align-baseline' onClick={(e) => {
                e.preventDefault();
                setIsOpen(!isOpen)
              }}>contact our support</Button>.</p>
            <Link to='/'>Go to homepage</Link>
          </DashboardSection>
        </Dashboard>
        {isOpen ? <UserInterestModal title={'Upgrade Required'} isOpen={isOpen} toggle={() => setIsOpen(!isOpen)}/> : null}
      </div>
    )
}
