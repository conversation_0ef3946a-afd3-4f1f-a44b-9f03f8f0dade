/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React, { useMemo, useState } from 'react';
import { useHistory } from 'react-router-dom';
import UserTable, { UserTableUser } from './UserTable';
import UserInvitationForm from './UserInvitationForm';
import { Loader } from '@g17eco/atoms/loader';
import NotAuthorised from '../../routes/not-authorised';
import { generateUrl } from '../util';
import Dashboard, { DashboardRow, DashboardSection, DashboardSectionTitle } from '../../components/dashboard';
import UserInvitationFormContainer from './UserInvitationFormContainer';
import { UserPermissions } from '../../constants/users';
import { filterValidRoles, UserFilterReportingLevel, UserRoles } from '../../constants/user';
import './styles.scss';
import { getFullName } from '../../utils/user';
import { OnboardingStatus } from '../../types/onboarding';
import { RouteInterfaceMin } from '../../types/routes';
import { UserLimitUsage } from '../../apps/company-tracker/components/user-limit/UserLimitTypes';
import { Button } from 'reactstrap';
import { AdminBreadcrumbsProps } from '@g17eco/molecules/breadcrumbs';
import { BasicAlert } from '@g17eco/molecules/alert';
import { Option } from '@g17eco/molecules/select/SelectFactory';
import { InitiativeUser, useGetInitiativeUsersQuery } from '@api/manage-users';
import { BulkOnboardingContainer } from '../../apps/company-tracker/components/bulk-onboarding-import';
import { useAppSelector } from '@reducers/index';
import { isStaff } from '@selectors/user';

const isInherited = (userPermissions: UserPermissions[], initiativeId: string): boolean => {
  return !userPermissions.find((p) => p.initiativeId === initiativeId);
};

const currentLevelPermission = (
  userPermissions: UserPermissions[],
  parentInitiativeIds: string[],
  initiativeId: string
): UserRoles[] => {
  const permissions = userPermissions.find((p) => p.initiativeId === initiativeId);
  if (permissions) {
    return permissions.permissions.filter(filterValidRoles);
  }

  const parentPermissionId = parentInitiativeIds.find((id) => userPermissions.find((p) => p.initiativeId === id));
  const parentPermission = parentPermissionId
    ? userPermissions.find((p) => p.initiativeId === parentPermissionId)
    : undefined;
  if (parentPermission) {
    return parentPermission.permissions.filter(filterValidRoles);
  }
  return [];
};

const getConsolidateUsers = (data: InitiativeUser | undefined, initiativeId: string): UserTableUser[] => {
  const userTableUsers: UserTableUser[] = [];

  if (!data) {
    return userTableUsers;
  }

  data.users.forEach(user => {
    userTableUsers.push({
      _id: user._id,
      isStaff: user.isStaff ?? false,
      isActivated: true,
      name: getFullName(user, user._id),
      email: user.email,
      roles: currentLevelPermission(user.permissions, data.parentInitiativeIds, initiativeId),
      lastUpdated: user.lastLogin,
      lastLogin: user.lastLogin,
      isInherited: isInherited(user.permissions, initiativeId),
      isRequestToJoin: false,
      user,
      invitedDate: user.invitedDate,
    });
  });

  data.invitations.forEach(invitation => {
    const isRequestToJoin = invitation.status === OnboardingStatus.NotStarted;
    const user = invitation.user;
    const emailPrefix = user.email.split('@')[0];
    userTableUsers.push({
      _id: invitation._id,
      isStaff: false,
      isActivated: false,
      name: isRequestToJoin ? getFullName(user, emailPrefix) : emailPrefix,
      email: user.email,
      roles: currentLevelPermission(user.permissions, data.parentInitiativeIds, initiativeId),
      lastUpdated: invitation.created,
      lastLogin: '',
      isInherited: isInherited(user.permissions, initiativeId),
      isRequestToJoin,
      invitation,
    });
  });

  return userTableUsers;
};

interface UserManagementProps {
  initiativeId: string;
  page?: string;
  userLimit: number;
  UserLimitComponent?: React.ElementType<UserLimitUsage>;
  baseRoute: RouteInterfaceMin;
  buttons?: React.JSX.Element[];
  canInviteMultipleUsers?: boolean;
  BreadcrumbsComponent: (props: AdminBreadcrumbsProps) => JSX.Element;
}

interface UserLimitUpgradeParams extends Pick<UserManagementProps, 'UserLimitComponent' | 'userLimit'> {
  limitReached: boolean;
  currentUsage: number;
}

const UserLimitUpgrade = ({ limitReached, currentUsage, UserLimitComponent, userLimit }: UserLimitUpgradeParams) => {
  if (!limitReached) {
    return null;
  }

  if (UserLimitComponent) {
    return <UserLimitComponent currentUsage={currentUsage} userLimit={userLimit} />;
  }

  return (
    <BasicAlert type='warning'>
      You have reached your user limit of {userLimit}. Please contact us to increase your limit.
    </BasicAlert>
  );
};

export const UserManagement = (props: UserManagementProps) => {
  const {
    userLimit,
    initiativeId,
    page,
    baseRoute,
    UserLimitComponent,
    canInviteMultipleUsers = false,
    BreadcrumbsComponent,
  } = props;

  const history = useHistory();

  const isStaffUser = useAppSelector(isStaff);

  const [filterReportingLevel, setFilterReportingLevel] = useState<UserFilterReportingLevel>('inherited');
  const { data, isFetching, refetch, isError } = useGetInitiativeUsersQuery({
    initiativeId,
    includeSubsidiaries: filterReportingLevel === 'inherited_and_subsidiaries'
  });

  const handleReportingLevelChange = (
    filter: Option<UserFilterReportingLevel> | Option<UserFilterReportingLevel>[]
  ) => {
    if (Array.isArray(filter)) {
      return;
    }
    setFilterReportingLevel(filter?.value ?? undefined);
  };

  const listUsers = useMemo(() => {
    return getConsolidateUsers(data, initiativeId).reduce(
      (accumulator, user) => {
        if (user.roles.includes(UserRoles.Owner)) {
          accumulator.hasOwner = true;
        }

        if (user.isStaff) {
          accumulator.staff.push(user);
          return accumulator;
        }
        accumulator.users.push(user);
        return accumulator;
      },
      { users: [], staff: [], hasOwner: false } as { hasOwner: boolean; users: UserTableUser[]; staff: UserTableUser[] }
    );
  }, [data, initiativeId]);

  if (!initiativeId || isFetching) {
    return <Loader />;
  }

  if (isError) {
    return <NotAuthorised />
  }

  const handleReload = async () => {
    refetch();
  }

  const nonStaffUserCount = listUsers.users.length;
  const limitReached = userLimit > 0 && nonStaffUserCount >= userLimit;

  const toggleUserInvitationForm = () => {
    const invitationUrl = generateUrl(baseRoute, { initiativeId, portfolioId: initiativeId, page: 'invite' });
    history.push(invitationUrl);
  };

  const onClickInviteMultipleUsers = () => {
    const url = generateUrl(baseRoute, { initiativeId, page: 'import-export' });
    return history.push(url);
  };

  const baseBreadcrumb = {
    label: 'Manage Users',
    url: generateUrl(baseRoute, { initiativeId, portfolioId: initiativeId }),
  };

  if (page === 'invite') {
    return (
      <Dashboard className='userManagement'>
        <DashboardRow>
          <BreadcrumbsComponent initiativeId={initiativeId} breadcrumbs={[baseBreadcrumb, { label: 'Invite user' }]} />
        </DashboardRow>
        <DashboardSectionTitle title={'Invite user(s)'} buttons={props.buttons} />
        <DashboardSection className='userManagement-container'>
          <UserInvitationFormContainer>
            <UserInvitationForm baseRoute={baseRoute} initiativeId={initiativeId} handleReload={handleReload} />
          </UserInvitationFormContainer>
        </DashboardSection>
      </Dashboard>
    );
  }

  if (page === 'import-export' && canInviteMultipleUsers) {
    const breadcrumbs = (
      <BreadcrumbsComponent
        initiativeId={initiativeId}
        breadcrumbs={[baseBreadcrumb, { label: 'Invite multiple users' }]}
      />
    );
    return (
      <BulkOnboardingContainer
        initiativeId={initiativeId}
        userLimit={userLimit}
        BreadcrumbsComponent={breadcrumbs}
      />
    );
  }

  const renderManageUserButtons = () => {
    if (!canInviteMultipleUsers) {
      return props.buttons;
    }

    const internalButtons = [
      <Button key='invite-btn' onClick={onClickInviteMultipleUsers} disabled={limitReached} color='transparent'>
        <i className='fal fa-users-medical mr-2' />
        Mass invite users
      </Button>,
    ];

    if (isStaffUser) {
      internalButtons.push(
        <Button
          key='manage-workgroups-btn'
          onClick={() => history.push(generateUrl(baseRoute, { initiativeId, page: 'workgroups' }))}
          outline={false}
          className='ms-2'
        >
          <i className='fal fa-users mr-2' />
          Manage workgroups
        </Button>
      );
    }

    return [...(props.buttons ?? []), ...internalButtons];
  };

  return (
    <Dashboard className='userManagement'>
      <DashboardRow>
        <BreadcrumbsComponent initiativeId={initiativeId} breadcrumbs={[baseBreadcrumb]} />
      </DashboardRow>
      <DashboardSectionTitle title={'Manage users'} buttons={renderManageUserButtons()} />
      <DashboardSection className='userManagement-container'>
        <UserLimitUpgrade
          currentUsage={nonStaffUserCount}
          userLimit={userLimit}
          limitReached={limitReached}
          UserLimitComponent={UserLimitComponent}
        />
        <div data-testid='manage-users-table'>
          <UserTable
            users={listUsers.users}
            hasOwner={listUsers.hasOwner}
            handleReload={handleReload}
            toggleUserInvitationForm={toggleUserInvitationForm}
            isAddUserDisabled={limitReached}
            handleReportingLevelChange={handleReportingLevelChange}
            filterReportingLevel={filterReportingLevel}
            isDownloadable
          />
        </div>
        <div className='mt-3 text-right' data-testid={'user-limit-text'}>
          {nonStaffUserCount}/{userLimit || 'Unlimited'} users
        </div>
      </DashboardSection>
      <DashboardSectionTitle title={'Support users (G17Eco staff)'} />
      <DashboardSection className='userManagement-container'>
        <div data-testid='support-users-table'>
          <UserTable
            users={listUsers.staff}
            hasOwner={listUsers.hasOwner}
            handleReload={handleReload}
            toggleUserInvitationForm={toggleUserInvitationForm}
            btnText='Add support'
            handleReportingLevelChange={handleReportingLevelChange}
            filterReportingLevel={filterReportingLevel}
          />
        </div>
      </DashboardSection>
    </Dashboard>
  );
};
