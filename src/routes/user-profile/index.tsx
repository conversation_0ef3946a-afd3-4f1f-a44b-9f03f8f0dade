/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { Component, FormEvent } from 'react';
import { connect, ConnectedProps } from 'react-redux';
import { Button, Col, Form, FormGroup, Input, Label } from 'reactstrap';
import ProfilePicture from '../../components/profile/ProfilePicture';
import { Avatar } from '@g17eco/atoms/avatar';
import { reloadCurrentUser, saveUser } from '@actions/user';
import { Loader } from '@g17eco/atoms/loader';
import Dashboard, { DashboardSection } from '../../components/dashboard';
import { RootState } from '../../reducers';
import { CurrentUserData } from '@reducers/current-user';
import { RouteComponentProps, withRouter } from 'react-router-dom';
import { BasicAlert } from '@g17eco/molecules/alert';
import { UserProfileForm } from '@g17eco/types/user';
import './styles.scss';

const ReactPasswordStrength = React.lazy(() => import('../../components/react-password-strength'));

interface State {
  form: UserProfileForm;
  isPasswordValid?: boolean;
  email: string;
  profile: string;
  files: File[];
  editMode: boolean;
  showPasswordEdit: boolean;
  loaded: boolean;
  submitted: boolean;
  saving: boolean;
  error: string;
}

class UserProfile extends Component<PropsFromRedux & RouteComponentProps> {

  state: State = {
    form: {
      title: '',
      firstName: '',
      surname: '',
      jobTitle: '',
      telephoneNumber: '',
      oldPassword: undefined,
      password: undefined,
      rPassword: undefined,
    },
    email: '',
    profile: '',
    files: [],
    editMode: true,
    showPasswordEdit: false,
    loaded: false,
    submitted: false,
    saving: false,
    error: ''
  }

  componentDidMount = () => {
    this.hydrate();
  }

  getRquiredFields = (): (keyof UserProfileForm)[] => {
    return ['firstName', 'surname'];
  }

  hydrate = () => {
    const { loaded, form } = this.state;
    const { userState } = this.props;
    const origData = userState.data;
    if (!loaded && userState.loaded) {
      const keys = Object.keys(form) as (keyof CurrentUserData)[];
      const userData: Record<string, unknown> = {}
      keys.forEach(key => userData[key] = origData[key]);
      this.setState({ form: { ...form, ...userData }, profile: origData.profile, email: origData.email, loaded: true });
    }
  }

  save = (e: FormEvent) => {
    const { form, files } = this.state;
    e.preventDefault();
    this.setState({ saving: true, error: false });

    saveUser(form, files)
      .then(() => this.setState({ saving: false, submitted: true, error: '' }, () => this.props.reloadCurrentUser()))
      .catch(e => this.setState({ saving: false, error: e.message }));
  }

  toggleEditMode = () => {
    const { form } = this.state;
    form.password = undefined;
    form.rPassword = undefined;
    this.setState({ editMode: !this.state.editMode, showPasswordEdit: false, form: form });
  }

  togglePasswordEdit = () => {
    const { form, showPasswordEdit, isPasswordValid } = this.state;

    if (showPasswordEdit) {
      // About to disable checkbox
      form.password = undefined;
      form.rPassword = undefined;
      form.oldPassword = undefined;
    }
    this.setState({
      isPasswordValid: showPasswordEdit ? false : isPasswordValid,
      showPasswordEdit: !showPasswordEdit,
      form: form
    });
  }

  handleAddFiles = (files: File[]) => {
    this.setState({ files });
  }

  handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const target = e.target;
    this.setState({
      form: {
        ...this.state.form,
        [target.name]: target.value
      }
    })
  };

  handlePasswordChange = (state: { password: string, isValid: boolean }) => {
    this.setState({
      isPasswordValid: state.isValid,
      form: {
        ...this.state.form,
        password: state.password,
      }
    })
  };

  renderFieldRow = (fieldName: keyof UserProfileForm, fieldTitle: string) => {
    const { editMode, form } = this.state;
    const fieldValue = form[fieldName];
    if (!editMode) {
      return this.renderReadOnlyRow(fieldTitle, fieldValue);
    }

    const isValid = fieldValue && fieldValue.length > 0;
    const required = this.getRquiredFields().includes(fieldName);
    const isInvalid = required && !isValid;
    return (
      <FormGroup row>
        <Label sm={3} for={fieldName}>{fieldTitle}{required ? '*' : ''}</Label>
        <Col>
          <Input valid={!!isValid} invalid={isInvalid} type='text' required={required} defaultValue={fieldValue} name={fieldName} onChange={this.handleChange} />
        </Col>
      </FormGroup>
    );
  }

  renderSpacer = () => {
    return <hr className='mt-4' />;
  }

  renderPasswordRow = () => {
    const { editMode, showPasswordEdit, isPasswordValid, form } = this.state;

    if (!editMode) {
      return this.renderReadOnlyRow('Password', '*********');
    }

    const { loaded, data: user } = this.props.userState;
    if (loaded && user.authenticationProvider?.type === 'FEDERATION') {
      return (
        <FormGroup row>
          <Label sm={3}>Password</Label>
          <Label sm={9}>Password not managed by G17Eco. Please contact your SSO administrator for help.</Label>
        </FormGroup>
      )
    }

    if (!showPasswordEdit) {
      return this.renderReadOnlyRow('Password', <span
        className={'d-flex align-items-center'}>
        <span className='dont_translate'>*********</span>
        <Button color='link' className='ml-3' onClick={this.togglePasswordEdit}>
          Edit
        </Button>
      </span>
      );
    }

    return (
      <>
        <FormGroup row>
          <Label sm={3} for='oldPassword'>Current Password*</Label>
          <Col>
            <Input type='password' required={true} name='oldPassword' onChange={this.handleChange} />
          </Col>
        </FormGroup>

        <FormGroup row>
          <Label sm={3} for='password'>Password*</Label>
          <Col sm={9}>
            <React.Suspense fallback={<Loader />}>
              <ReactPasswordStrength
                changeCallback={this.handlePasswordChange}
                inputProps={{
                  required: true,
                  name: 'password',
                  autoComplete: 'off'
                }}
              />
            </React.Suspense>
          </Col>
        </FormGroup>

        <FormGroup row>
          <Label sm={3} for='rPassword'>Repeat Password*</Label>
          <Col>
            <React.Suspense fallback={<Loader />}>
              <ReactPasswordStrength
                changeCallback={(option: { password: string }) => {
                  this.setState({
                    form: {
                      ...this.state.form,
                      rPassword: option.password
                    }
                  })
                }}
                inputProps={{
                  required: true,
                  name: 'rPassword',
                  autoComplete: 'off',
                }}
                invalid={Boolean(form.rPassword && !isPasswordValid)}
              />
            </React.Suspense>
            <div className='text-right'>
              <Button color='link-secondary' className='p-0' onClick={this.togglePasswordEdit}>Cancel</Button>
            </div>
          </Col>
        </FormGroup>

      </>
    );
  }

  renderReadOnlyRow = (title: string, value: React.JSX.Element | string | undefined) => {
    return (
      <FormGroup row>
        <Label sm={3}>{title}</Label>
        <Label sm={9} className='text-truncate'>{value}</Label>
      </FormGroup>
    );
  }

  renderButtons = () => {
    const { form, error, editMode, saving, showPasswordEdit, isPasswordValid = false } = this.state;

    if (!editMode) {
      return (
        <Button
          type='button'
          onClick={this.toggleEditMode}>
          Edit
        </Button>
      );
    }

    const requiredFields = this.getRquiredFields();
    const missingData = requiredFields.some(field => !form[field]);

    const isPasswordsMatch = showPasswordEdit && form.password === form.rPassword;
    const isValidPassword = !showPasswordEdit ? true : isPasswordValid && isPasswordsMatch;
    const hasUpdatePasswordData = !showPasswordEdit ? true : form.oldPassword
    const formDisabled = !isValidPassword || !hasUpdatePasswordData || missingData || saving;

    return (
      <React.Fragment>
        <BasicAlert type={'danger'} hide={!isPasswordValid || isPasswordsMatch}>
          Please make sure both passwords match
        </BasicAlert>

        {error && <div className='alert alert-danger mt-xs'>{error}</div>}

        <Button
          disabled={formDisabled}
          type='submit'
          color='primary'
          data-testid='user-profile-submit-btn'
        >
          {saving ? 'Saving...' : 'Save'}
        </Button>
      </React.Fragment>
    );
  }

  renderProfilePicture = () => {
    const { editMode, profile } = this.state;
    if (editMode) {
      return <ProfilePicture width='150px' handleFilesAdded={this.handleAddFiles} defaultPicture={profile} />;
    }

    return <div className='avatarContainer'>
      <Avatar width='150px'>
        {profile ? <img alt='avatar' src={profile} /> : <></>}
      </Avatar>
    </div>;
  }

  render() {
    const { email } = this.state;
    const { userState, history } = this.props;

    if (userState.errored) {
      history.push('/');
      return '';
    }

    if (!userState.loaded) {
      return <Loader />;
    }

    return (
      <Dashboard>
        <DashboardSection
          title='Profile'
          icon='fa-user-edit'
          buttons={[
            <Button key='edit' color='secondary' outline onClick={() => history.push('/user-preferences')}>
              <i className='fa-light fa-gear fs-6 mr-1' /> My Settings
            </Button>
          ]}
          className='userProfileContainer'>
          <Form onSubmit={this.save}>

            {this.renderProfilePicture()}

            <div className='mt-3' />

            {this.renderReadOnlyRow('E-mail*',
              <span className='align-middle dont_translate'>
                <a href={`mailto:${email}`}>{email}</a>
              </span>
            )}

            {this.renderFieldRow('title', 'Title')}
            {this.renderFieldRow('firstName', 'First Name')}
            {this.renderFieldRow('surname', 'Last Name')}

            {this.renderSpacer()}

            {this.renderFieldRow('jobTitle', 'Job Title')}
            {this.renderFieldRow('telephoneNumber', 'Telephone')}

            {this.renderSpacer()}
            {this.renderPasswordRow()}
            {this.renderSpacer()}

            <div className='row mt-1 g-0'>
              <div className='col text-right pr-1'>
                {this.renderButtons()}
              </div>
            </div>
          </Form>
        </DashboardSection>
      </Dashboard>
    )
  }
}

const mapStateToProps = (state: RootState) => ({
  userState: state.currentUser,
});

const mapDispatchToProps = {
  reloadCurrentUser
};

const connector = connect(mapStateToProps, mapDispatchToProps);
type PropsFromRedux = ConnectedProps<typeof connector>


export default withRouter(connector(UserProfile));
