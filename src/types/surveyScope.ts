/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import { ViewValues } from '../components/survey-overview-sidebar/viewOptions';
import { CardGridButtonProps } from '../components/survey-scope/CardGrid';
import { UserMin, SurveyUserRoles } from '../constants/users';
import UniversalTracker from '../model/UniversalTracker';
import { MetricGroup } from './metricGroup';
import { StakeholderGroup } from '../model/stakeholderGroup';
import { InitiativePlain } from './initiative';
import { SortableItem } from '../utils/sort';
import { DelegationScopeOptions } from '../components/survey-scope/virtualScope';
import { SurveyActionData, SurveyModelMinimalUtrv } from '../model/surveyData';
import { Group } from '@g17eco/core';
import { DataPeriods, ValueValidation } from './universalTracker';
import { UtrvAssuranceStatus, ValueHistory } from './universalTrackerValue';
import { AppConfig } from './app';
import { LatestUtrvNotes } from './utrvNote';

interface ValueDataTableColumn {
  code: string;
  value?: string | number | string[];
  unit?: string;
  numberScale?: string;
}

interface ValueDataInput {
    data?: any;
    value?: number;
    unit?: string;
    numberScale?: string;
    table?: ValueDataTableColumn[][];
}

export interface ValueData {
  data?: any;
  isImported?: boolean;
  table?: ValueDataTableColumn[][];
  explain?: string;
  notApplicableType?: string;
  input?: ValueDataInput;
}

interface SecondaryComposite {
  surveyId?: string;
  blueprint?: string;
  configCode: string;
}

export interface CompositeData extends SecondaryComposite {
  secondary?: SecondaryComposite[];
}

export interface LedgerUniversalTrackerValue {
  created: string;
  lastUpdatedDate: string;
  documentId: string;
  utrvId: string;
  historyIds: string[];
}

export type DisaggregationSummary = Record<string, any>;

export interface SourceItem {
  utrvId: string;
  /** points to the last action=updated history, where data was taken from **/
  historyId: string;

  /** points to the last history when the source item was used **/
  latestHistoryId: string | undefined;
  /** for convenience, we pull the status of the utrv at the time of use **/
  latestStatus: string | undefined;
}

export interface UniversalTrackerValuePlain<T = string> {
  _id: string;
  universalTrackerId: T;
  initiativeId: T;
  sourceType: string;
  sourceCode?: string;
  effectiveDate: string;
  type: string;
  status: string;
  valueData?: ValueData;
  valueType?: string;
  history?: ValueHistory[];
  stakeholders: {
    stakeholder: string[];
    verifier: string[];
  };
  unit?: string;
  sampleSize?: number;
  value?: number;
  numberScale?: string;
  compositeData?: CompositeData;
  sourceItems?: SourceItem[];
  ledgerUniversalTrackerValue?: LedgerUniversalTrackerValue;
  disaggregationSummary?: DisaggregationSummary;
  period?: DataPeriods;
  assuranceStatus?: UtrvAssuranceStatus;
  aggregationCount?: number;
  disaggregation?: DisaggregationUtrv[];
  evidenceRequired?: boolean;
  noteRequired?: boolean;
  verificationRequired?: boolean;
  isPrivate?: boolean;
  lastUpdated: string;
  note?: string;
  notes?: LatestUtrvNotes;
  created?: string;
}

export type DisaggregationUtrv =
  | Pick<
      UniversalTrackerValuePlain,
      'effectiveDate' | 'type' | 'universalTrackerId' | 'initiativeId' | 'value' | 'valueData' | 'compositeData' | '_id'
    >
  | UniversalTrackerValuePlain;

export interface Breadcrumb {
  cardGroup: ViewValues,
  cardCategory: string
  title: string,
}

export interface BaseScopeQuestion {
  utrv?: SurveyModelMinimalUtrv,
  name: string,
  universalTracker: UniversalTracker,
  frameworkCode: string,
  valueType: string,
  valueValidation: ValueValidation,
}

export interface ScopeQuestionOptionalValue extends BaseScopeQuestion, SortableItem {
  shortPrefix: string;
}

export interface ScopeQuestion extends ScopeQuestionOptionalValue {
  utrv: SurveyModelMinimalUtrv,
}

export interface UNSDGMap {
  goals: UNSDGMap_Goal[]
}

export interface UNSDGMap_Goal {
  code: string,
  title: string,
  description: string,
  targets: UNSDGMap_Target[]
}

export interface UNSDGMap_Target {
  code: string,
  title: string,
  description: string,
}

export type handleDrilldownInterface = (
  cardGroup: ViewValues,
  cardCategory: string,
  title: string,
  groupBy?: ViewValues,
) => void;

export interface CardProps {
  appSettings: AppConfig['settings']
  questionList: ScopeQuestionOptionalValue[];
  UNSDGMap: UNSDGMap;
  materiality?: InitiativePlain['materiality'];
  surveyData: Pick<SurveyActionData, '_id' | 'scope' | 'scopeConfig' | 'initiativeId'>;
  blueprint: any;
  view: ViewValues;
  cardGroup: ViewValues;
  scopeType: ViewValues | DelegationScopeOptions;
  addBtn: (scopeTag: string, name?: string, group?: Group) => CardGridButtonProps;
  breadcrumbs: Breadcrumb[],
  metricGroups: MetricGroup[],
  handleDrilldown: handleDrilldownInterface;
  handleToggleDelegation?: (e: React.MouseEvent<HTMLButtonElement>, userRole: SurveyUserRoles) => void;
  users: UserMin[],
  surveyStakeholders?: StakeholderGroup,
  initiativeTree?: InitiativePlain[],
  isUserDelegated?: (userId: string, userRole?: string) => boolean,
  handleCreateCustomMetric?: () => void;
  selectedView?: ViewValues;
  setSelectedView?: (view: ViewValues) => void;
}
