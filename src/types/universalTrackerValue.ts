/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { ExistingEvidenceFile, ValueData } from '../components/survey/question/questionInterfaces';
import { ConnectionUtr, UniversalTrackerPlain, UtrValueType } from './universalTracker';
import { UniversalTrackerValuePlain } from './surveyScope';
import { UserMin } from '../constants/users';
import { UniversalTrackerBase } from '../model/UniversalTracker';
import { UtrvNote } from './utrvNote';
import type { PartialAssuranceField } from '@g17eco/types/assurance';
import { SurveyType } from './survey';

export interface IpLocation {
  ip: string;
  latitude?: number;
  longitude?: number;
}

export enum UtrvAssuranceStatus {
  /** Question is added to assurance portfolio, but not yet completed **/
  Created = 'created',
  /** Question is completed and should no longer be updated **/
  Completed = 'completed',
  /** Question has been marked to be explicitly open for modifications **/
  CompletedOpen = 'completed_open',
  Rejected = 'rejected',
  /** Question has been updated after completion **/
  Restated = 'restated',
  /** Question has been marked to be partial assurance and can assure later **/
  Partial = 'partial',
}

type ValueType = number | string | undefined;
export type ValueDataObject = Record<string, ValueType>
export type ValueDataData = string | string[] | ValueDataObject

export interface ValueHistory extends Partial<UtrvNote>{
  _id: string;
  action: string;
  userId: string;
  value: number | undefined;
  /** @deprecated */
  sampleSize?: number;
  date: string;
  evidence?: string[];
  unit?: string;
  numberScale?: string;
  assuranceStatus?: UtrvAssuranceStatus;
  assuranceFields?: PartialAssuranceField[];
  valueType?: UtrValueType;
  valueData?: ValueData<ValueDataData>;
  location?: IpLocation;
}


export interface UtrvHistory {
  latestHistory: {
    documents: ExistingEvidenceFile[],
    stakeholderHistory?: ValueHistory,
    verifierHistory?: ValueHistory
  }
}

export interface UtrvHistoryInfoInterface extends UniversalTrackerValuePlain {
  documents: ExistingEvidenceFile[];
  users: UserMin[];
  universalTracker: UniversalTrackerPlain[];
  history: ValueHistory[];
  surveyType?: SurveyType;
}

export type KeyUtrvs = 'value' |
  'valueData' |
  'effectiveDate' |
  'assuranceStatus' |
  'universalTrackerId' |
  'period' |
  'type';

// Aggregated Utrv
export type UtrValueFields = Pick<UniversalTrackerValuePlain, KeyUtrvs>
export type UtrvDataState = UniversalTrackerValuePlain | UtrValueFields;

export interface UtrvComment {
  _id: string;
  userId: string;
  user: {
    _id: string;
    name: string;
  };
  created: string;
  text: string;
}

export interface UtrvCommentsResponse {
  _id: string;
  utrvId: string;
  created: string;
  items: UtrvComment[];
  users: Pick<UserMin, '_id' | 'firstName' | 'surname'>[];
}

export interface ConnectionUtrv extends UtrValueFields, Pick<UniversalTrackerValuePlain, '_id' | 'compositeData' | 'note' | 'initiativeId' | 'status'> {
  universalTracker: ConnectionUtr;
}

export interface ConversionData {
  unit?: string;
  defaultUnit?: string;
  numberScale?: string;
  defaultNumberScale?: string;
  value?: string | number;
  utr: UniversalTrackerPlain | UniversalTrackerBase;
  isCurrency: boolean;
  suffix?: string;
  prefix?: string;
}

export interface UniversalTrackerValueExtended extends UniversalTrackerValuePlain {
  universalTracker: UniversalTrackerPlain
}
