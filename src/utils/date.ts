/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import dayjs, { Dayjs, ManipulateType, OpUnitType, QUnitType } from 'dayjs';
import calendar from 'dayjs/plugin/calendar';
import utc from 'dayjs/plugin/utc';
import duration from 'dayjs/plugin/duration';
import relativeTime from 'dayjs/plugin/relativeTime';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { DataPeriods } from '../types/universalTracker';
import { getPeriodName } from './universalTracker';
import { TimeFrameType } from '@g17eco/types/insight-custom-dashboard';
import { Option } from '../molecules/select/SelectFactory';
import { TimeRangeSelectorProps } from '../molecules/time-range-selector';

dayjs.extend(utc)
dayjs.extend(duration)
dayjs.extend(calendar)
dayjs.extend(relativeTime)
dayjs.extend(customParseFormat);
dayjs.extend(advancedFormat)

interface SetDateProps {
  year?: number | string;
  month?: number | string;
  effectiveDate?: DateString;
  isUtc?: boolean;
}

export type DateString = string | Date;
type CommonDate = DateString | Dayjs;

type EndOfUnit = 'hour' | 'day' | 'month' | 'year' | 'date'
interface SetTimeProps {
  hour: number;
  minute: number;
  second: number;
}

export enum TimePeriod {
  Hourly = 'hourly',
  Daily = 'daily',
  Weekly = 'weekly',
  Monthly = 'monthly',
  Yearly = 'yearly'
}

export const DATE = {
  DEFAULT_SPACES: 'DD MMM YYYY',
  DEFAULT_DASHES: 'DD-MMM-YYYY',
  MONTH_YEAR: 'MMMM YYYY',
  MONTH_YEAR_SHORT: 'MMM YYYY',
  MONTH_YEAR_SHORT_2: 'MMM YY',
  MONTH_YEAR_DATE_SHORT: 'DD MMM YY',
  MONTH: 'MMM',
  LONG: 'Do [of] MMMM, YYYY',
  SORTABLE: 'YYYY-MM-DD HH:mm:ss',
  HUMANIZE: 'humanize',
  DEFAULT_SPACES_WITH_TIME: 'DD MMM YYYY, HH:mm:ss',
  AT_TIME: 'DD MMM YYYY [at] HH:mm',
  UK: 'DD/MM/YYYY',
  YEAR_MONTH: 'YYYY MMMM',
  YEAR_MONTH_SHORT: 'YYYY MMM',
  DATE_PICKER: 'YYYY-MM-DD',
  MONTH_ONLY: 'MMMM',
  MONTH_NUMBER: 'M',
  YEAR_ONLY: 'YYYY',
  FILE_NAME: 'YYYY-MM-DD_HH-mm-ss',
  TOOLTIP: 'MMM D, YYYY',
  ISO: 'YYYY-MM-DDTHH:mm:ss.sssZ',
  YEAR_MONTH_DATE: 'YYYY, MMM DD',
  TIME_ONLY: 'HH:mm:ss',
};

export const getCurrentYear = () => {
  return dayjs().year();
}

const getNextYears = (numberOfNextYears: number, currentMonth: number) => {
  if (numberOfNextYears === 0) {
    return currentMonth >= 11 ? 1 : 0;
  }
  return numberOfNextYears;
}

export const getYearDropdownOptions = (numberOfYears: number = 5, numberOfNextYears: number = 0) => {
  const currentMonth = dayjs().month();
  const currentYear = dayjs().year();
  const endYear = currentYear + getNextYears(numberOfNextYears, currentMonth);
  const startYear = currentYear - numberOfYears;
  const options: Option<number>[] = [];
  for (let year = endYear; year > startYear; year--) {
    options.push({ value: year, label: year.toString() });
  }

  return options;
}

export const getYearOptionsByRange = (endDate: DateString, startDate: DateString, utc?: boolean) => {
  const endYear = getYear(endDate, utc);
  const startYear = getYear(startDate, utc);
  const options: Option<number>[] = [];
  for (let year = endYear; year >= startYear; year--) {
    options.push({ value: year, label: year.toString() });
  }
  return options;
};

const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];
export const MONTHS_VALUE = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

const validDateFormats = [DATE.UK, DATE.DATE_PICKER];

export const parseUserStringToDate = (date: string) => {
  return dayjs(date, validDateFormats, true);
};

/** @deprecated will be replaced with formatDateNonUTC & formatDateUTC */
export function formatDate(date: string | Date | number, format = DATE.DEFAULT_SPACES, utc = false) {
  if (!date) {
    return '';
  }
  const day = utc ? dayjs.utc(date) : dayjs(date);
  switch (format) {
    case DATE.HUMANIZE:
      return day.fromNow();
    default:
      return day.format(format);
  }
}

export const formatDateNonUTC = (date: string | Date | number, format = DATE.DEFAULT_SPACES) => formatDate(date, format, false);

export const formatDateUTC = (date: string | Date | number, format = DATE.DEFAULT_SPACES) => formatDate(date, format, true);

export const formatHumaniseDate = (date: string | Date | number) => formatDateUTC(date, DATE.HUMANIZE);

export function formatLongDate(date?: string | Date) {
  return date ? dayjs(date).format('DD/MM/YYYY HH:mm:ss') : '';
}

export const getMonthOfYear = (monthNum: number) => dayjs().utc().month(monthNum).toDate();

export const getMonthName = (monthNum: number) =>
  dayjs
    .utc()
    .set('month', monthNum - 1)
    .format(DATE.MONTH_ONLY);

type DateActions = 'add' | 'sub' | '';

export const DEFAULT_API_PAST_MONTHS = 3;

export function formatDatePickerString(
  value: number = 0,
  type: DateActions = 'add',
  format: string = DATE.DATE_PICKER,
  unit: ManipulateType = 'month',
  utc = false
) {
  const date = utc ? dayjs.utc() : dayjs();
  switch (type) {
    case 'add':
      return date.add(value, unit).format(format);
    case 'sub':
      return date.subtract(value, unit).format(format);
    default:
      return date.format(format);
  }
}

export const getPeriodOptions = (asPeriodicity = true) => [
  {
    value: DataPeriods.Monthly,
    label: getPeriodName(DataPeriods.Monthly, asPeriodicity),
  },
  {
    value: DataPeriods.Quarterly,
    label: getPeriodName(DataPeriods.Quarterly, asPeriodicity),
  },
  {
    value: DataPeriods.Yearly,
    label: getPeriodName(DataPeriods.Yearly, asPeriodicity),
  },
];

export const getMonthOptions = (short = false) =>
  months.map((m, i) => ({
    value: i,
    label: short ? m.substring(0, 3) : m,
  }));

export const getMonthNonJSOptions = () => months.map((m, i) => ({
  value: i + 1,
  label: m
}))

export const getDuration = (diff: number, unit?: duration.DurationUnitType) => {
  return dayjs.duration(diff, unit ?? 'milliseconds')
}

export const getDiff = (
  date1: string | number | Date,
  date2: string | number | Date = new Date(),
  unit?: QUnitType | OpUnitType
) => {
  return dayjs(date1).diff(date2, unit);
};

export const addDate = (date: string | Date | number, value: number, unit: ManipulateType) => {
  return dayjs.utc(date).add(value, unit).toDate();
}

export const getCalendarDate = (date?: DateString) => {
  return dayjs(date).calendar();
}

export const getYear = (date?: DateString, utc = false) => {
  return (utc ? dayjs.utc(date) : dayjs(date)).year();
}

export const getMonth = (date?: DateString, utc = false) => {
  return (utc ? dayjs.utc(date) : dayjs(date)).month();
}

export const getFullUTCDate = (year: number, month: number, day: number, date?: DateString): Date => {
  return dayjs.utc(date).year(Number(year)).month(Number(month)).date(Number(day)).toDate()
}

export const getUTCStartOf = (unit: EndOfUnit, date?: DateString,) => {
  return dayjs.utc(date).startOf(unit).toDate()
}

export const getUTCEndOf = (unit: EndOfUnit, date?: DateString) => {
  return dayjs.utc(date).endOf(unit).toDate();
}

export const setFormat = (format: string, date?: DateString) => {
  return dayjs(date).format(format)
}

export const setToISOString = (date?: DateString) => {
  return dayjs(date).toISOString();
}

export const getCurrentStartOfTheDayDate = (value: EndOfUnit, date?: DateString) => {
  return dayjs.utc(date).startOf(value).toDate();
}

export const setDate = ({ year, month, effectiveDate, isUtc }: SetDateProps) => {
  let dateObj = isUtc ? dayjs.utc(effectiveDate) : dayjs(effectiveDate);
  if (year) {
    dateObj = dateObj.set('year', Number(year))
  }
  if (undefined !== month) {
    dateObj = dateObj.set('month', Number(month))
  }

  return dateObj.toDate();
}

export const getDate = (date?: CommonDate) => dayjs(date);

export const getDateUnit = (unit: EndOfUnit, date?: DateString) => {
  return dayjs(date).get(unit);
}

export const getUtcDateUnit = (unit: EndOfUnit, date?: DateString) => {
  return dayjs.utc(date).get(unit);
}

export const getUtc = (value: string) => {
  return dayjs.utc(value)
}

export const subtractDate = (date: CommonDate, value: number, unit: ManipulateType, utc = true) => {
  if (utc) {
    return dayjs.utc(date).subtract(value, unit).toDate();
  }
  return dayjs(date).subtract(value, unit).toDate();
}

export const currentTimestamp = () => dayjs().unix()
export const toTimestamp = (d: string | Date) => dayjs(d).unix()

export const isBefore = (value: CommonDate | number, date?: CommonDate) => {
  return dayjs(date).isBefore(value);
};

export const isAfter = (value?: CommonDate | number, date?: CommonDate) => {
  return dayjs(date).isAfter(value);
};

export const isSame = (date: CommonDate | undefined, compareDate: CommonDate | undefined, unit?: ManipulateType) => {
  return dayjs(date).isSame(compareDate, unit);
}

export const getDateTime = (date: DateString, time?: SetTimeProps) => {
  const current = dayjs();
  const dateTime = dayjs(date);

  if (time) {
    return dateTime.set('hour', time.hour).set('minute', time.minute).set('second', time.second).toDate();
  }
  return dateTime.set('hour', current.hour()).set('minute', current.minute()).set('second', current.second()).toDate();
};

export const getFutureDate = (numOfDays: number, unit: ManipulateType = 'day') => {
  return dayjs().add(numOfDays, unit).toDate();
}

export const getFutureIsoString = (numOfDays: number = 0, unit: ManipulateType = 'day') => {
  return dayjs().add(numOfDays, unit).toISOString();
}

export interface TimeRangeOption {
  value: string | number;
  label: string;
  showRange?: boolean;
}

export const TIME_RANGE_OPTIONS = [
  { value: 12, label: 'Last 12 months' },
  { value: 6, label: 'Last 6 months' },
  { value: 3, label: 'Last 3 months' },
  { value: 1, label: 'Last 1 month' },
  { value: TimeFrameType.AllTime, label: 'All time' },
  { value: TimeFrameType.Custom, label: 'Custom dates', showRange: true },
] satisfies TimeRangeOption[];

export const TIME_RANGE_YEARLY = TIME_RANGE_OPTIONS[0].value as number;
export const TIME_RANGE_ALL = TIME_RANGE_OPTIONS[4].value as number;

export const displayTimeRange = ({
  timeRange,
  dateRange,
  timeRangeOptions = TIME_RANGE_OPTIONS,
}: Pick<TimeRangeSelectorProps, 'timeRange' | 'dateRange'> & { timeRangeOptions?: TimeRangeOption[] }) => {
  const option = timeRangeOptions.find((o) => o.value === timeRange);
  if (!option) {
    return '';
  }
  if (dateRange && option.showRange) {
    return `${setFormat(DATE.DEFAULT_SPACES, dateRange.startDate)} - ${setFormat(
      DATE.DEFAULT_SPACES,
      dateRange.endDate
    )}`;
  }
  return option.label;
};

export const sortEffectiveDateDesc = (a: string, b: string) => (dayjs.utc(a).isBefore(dayjs.utc(b)) ? 1 : -1);
export const sortEffectiveDateAsc = (a: string, b: string) => (dayjs.utc(a).isBefore(dayjs.utc(b)) ? -1 : 1);

export const getUTCDateRange = (dateRange: { startDate?: Date; endDate?: Date }) => {
  return {
    ...(dateRange.startDate && { startDate: getUTCStartOf('day', setFormat(DATE.DATE_PICKER, dateRange.startDate)) }),
    ...(dateRange.endDate && { endDate: getUTCEndOf('day', setFormat(DATE.DATE_PICKER, dateRange.endDate)) }),
  };
};
