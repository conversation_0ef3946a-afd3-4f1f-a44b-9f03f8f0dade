/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { UpdateActions } from '../actions/universalTrackerValue';
import { NotApplicableTypes, StatusReadableText, UtrvStatus } from '../constants/status';
import { convertValue, getUnitDescription, UnitTypes } from './units';
import NumberFormat from './number-format';
import React from 'react';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { UniversalTrackerValuePlain, ValueData } from '../types/surveyScope';
import { TableColumn, TableValueDataData } from '../components/survey/form/input/table/InputInterface';
import UniversalTrackerValue from '../model/UniversalTrackerValue';
import UniversalTracker from '../model/UniversalTracker';
import { ConversionData, UtrvAssuranceStatus, ValueHistory } from '../types/universalTrackerValue';
import { SurveyModelMinimalUtrv, UnitConfig } from '../model/surveyData';
import { UniversalTrackerPlain, UtrValueType } from '../types/universalTracker';
import { DATE, formatDate } from './date';
import { QUESTION } from '@constants/terminology';
import { isNumeric } from './number';
import { getUtrDecimal } from '@utils/universalTracker';
import { handleRouteError } from '../logger';

type MinimalValueHistory = Pick<ValueHistory, 'userId' | 'date'>;
export const userHistoryComparator = (a: MinimalValueHistory, b: MinimalValueHistory): number => {
  if (a.date > b.date) return -1;
  if (a.date < b.date) return 1;
  return 0;
}

function getHistory(utrv: { history?: MinimalValueHistory[] }): MinimalValueHistory[] {
  const history = utrv.history;
  if (!Array.isArray(history)) {
    return [];
  }
  history.sort(userHistoryComparator);
  return history;
}

function getLastHistoryUserId(utrv: { history?: MinimalValueHistory[] }): string {
  const history = getHistory(utrv);
  return history.length <= 0 ? '' : history[0].userId;
}

export function getLatestHistoryUser(
  utrv: { history?: MinimalValueHistory[] },
  users: { _id: string; name?: string; firstName?: string; surname?: string }[]
): {
  _id?: string;
  name?: string;
  firstName?: string;
  surname?: string;
} {
  const defaultName = { firstName: '-', surname: '', name: '-' };
  if (!Array.isArray(users)) {
    return defaultName;
  }

  const userId = getLastHistoryUserId(utrv);
  if (!userId) {
    return defaultName;
  }

  const user = users.find((u) => u._id === userId);

  const latestUser = user ? { ...user, name: user.firstName + ' ' + user.surname } : defaultName;
  return latestUser;
}

export function hasStakeholderAccess(stakeholders: any, userId: string | undefined) {
  if (!stakeholders || !userId) {
    return false;
  }

  return stakeholders.stakeholder.includes(userId) || stakeholders.verifier.includes(userId);
}

export const getNaType = ({
  status,
  value,
  valueData,
}: Partial<Pick<UniversalTrackerValuePlain, 'status' | 'value' | 'valueData'>>): string => {
  //Created items can't have a value set, so can't be N/A
  if (status === UpdateActions.ACTION.UTR.CREATED) {
    return '';
  }

  // Value is set, can't be N/A
  if (value !== undefined) {
    return '';
  }

  // value is empty and there is no valueData, must be N/A (legacy utrvs)
  if (!valueData) {
    return '';
  }

  //This property is a dead give-away, but is not guaranteed
  const { notApplicableType, table, data } = valueData;

  if (notApplicableType) {
    return notApplicableType;
  }

  // ValueData table/data are both empty
  const emptyTable = table === undefined || table.length === 0;
  return data === undefined && emptyTable ? NotApplicableTypes.na : '';
};

export const isNa = (utrv: Pick<UniversalTrackerValuePlain, 'status' | 'value' | 'valueData'>): boolean => {
  return getNaType(utrv) === NotApplicableTypes.na;
}

export const isNr = (utrv: Pick<UniversalTrackerValuePlain, 'status' | 'value' | 'valueData'>): boolean => {
  return getNaType(utrv) === NotApplicableTypes.nr;
}

export const isNaOrNr = (utrvLikeData: Pick<SurveyModelMinimalUtrv, 'status' | 'value' | 'valueData'>) => Boolean(getNaType(utrvLikeData));

export const isAssuredLocked = (utrv: Pick<UniversalTrackerValuePlain, 'assuranceStatus'>) => {
  if (!utrv.assuranceStatus) {
    return false;
  }
  return [UtrvAssuranceStatus.Completed, UtrvAssuranceStatus.Partial].includes(utrv.assuranceStatus);
}

export const isUtrvCompletedOpen = (utrv: Pick<UniversalTrackerValuePlain, 'assuranceStatus'>) => {
  if (!utrv.assuranceStatus) {
    return false;
  }
  return utrv.assuranceStatus === UtrvAssuranceStatus.CompletedOpen;
}

export const getStatusReadableText = (status: UtrvStatus) => {
  return StatusReadableText[status] ?? status;
}

export function validateMinMax(value: any, min?: number, max?: number) {
  if (!isNumeric(value)) {
    return { errored: true, message: 'Value must be numeric' };
  }
  if (isNumeric(min) && value < min) {
    return { errored: true, message: `Value must be greater than ${min}` };
  }
  if (isNumeric(max) && value > max) {
    return { errored: true, message: `Value must be less than ${max}` };
  }

  return { errored: false, message: '' };
}

export const getListValueDataData = ({ valueData }: Pick<ValueHistory, 'valueData'>) => {
  if (!valueData) {
    return {};
  }

  const { data, input } = valueData;
  if (input && input.data) {
    return input.data;
  }

  return data || {};
};

export const getTableInputData = ({ valueData }: Pick<UniversalTrackerValuePlain, 'valueData'>): TableValueDataData => {
  if (!valueData) {
    return [];
  }

  const { table, input } = valueData;
  if (input && input.table) {
    return input.table;
  }

  return table || [];
};

export const getInputValue = ({ value, valueData }: Pick<UniversalTrackerValuePlain, 'value' | 'valueData'>) => {
  if (!valueData) {
    return value;
  }

  return valueData.input?.value !== undefined ? valueData.input.value : value;
};

const getValueDataPropName = (valueType: UtrValueType) => valueType === UtrValueType.Table ? 'table' : 'data'

export const getValueData = (
  { valueData }: Pick<UniversalTrackerValuePlain, 'valueData'>,
  valueType: UtrValueType
) => {
  if (!valueData) {
    return {};
  }

  const propName = getValueDataPropName(valueType)
  return valueData.input?.[propName] ? valueData.input : valueData;
}

export const getValueDataProp = (
  valueData: ValueData | ValueData['input'] | undefined,
  valueType: UtrValueType
): ValueData['table'] | ValueData['data'] => {
  if (!valueData) {
    return {};
  }
  const propName = getValueDataPropName(valueType)
  if ('input' in valueData && valueData.input?.[propName]) {
    return valueData.input[propName]
  }
  return valueData[propName];
}

export const canExecuteAction = ({ status }: Pick<UniversalTrackerValuePlain, 'status'>, action: string, isVerifier?: boolean) => {
  const { UPDATE, VERIFY, REJECT } = UpdateActions.STATUS;
  const { CREATED, UPDATED, REJECTED, VERIFIED } = UpdateActions.ACTION.UTR

  switch (action) {
    case VERIFY:
      return [REJECTED, UPDATED].includes(status);
    case REJECT:
      return [VERIFIED, UPDATED].includes(status);
    case UPDATE:
      return isVerifier || [CREATED, REJECTED, UPDATED].includes(status);
    default:
      return false;
  }
}

const checkIsCreatedOrNAOrNR = (utrv: Pick<SurveyModelMinimalUtrv, 'valueData' | 'status'>) => {
  return utrv.status === UtrvStatus.Created || !!utrv.valueData?.notApplicableType;
};

type UtrUnitCheck = Pick<UniversalTrackerPlain, 'unit' | 'unitInput' | 'unitType'>;
export const getUnitCode = (
  utrv: Pick<SurveyModelMinimalUtrv, 'valueData' | 'status'>,
  utrObj: UniversalTracker | UtrUnitCheck,
  unitConfig?: Partial<UnitConfig>
): string => {
  const utr: UtrUnitCheck | undefined = 'universalTracker' in utrObj ? utrObj?.getRaw() : utrObj;

  if (utrv?.valueData?.input?.unit) {
    return utrv.valueData.input.unit;
  }

  const isCreatedOrNAOrNR = checkIsCreatedOrNAOrNR(utrv);

  if (isCreatedOrNAOrNR && utr?.unitInput) {
    return utr.unitInput;
  }

  if (isCreatedOrNAOrNR && utr?.unitType) {
    const unitType = utr.unitType as keyof UnitConfig;
    if (unitConfig?.[unitType]) {
      return unitConfig[unitType] ?? '';
    }
  }

  if (utr) {
    return utr.unit || '';
  }

  return '';
};

export const getDefaultTableUnitOrNumberScaleCode = ({
  column,
  unitConfig,
  key,
}: {
  column: TableColumn;
  unitConfig?: UnitConfig;
  key: 'unit' | 'numberScale';
}): string | undefined => {

  const keyInput = `${key}Input` as keyof TableColumn;
  if (column[keyInput]) {
    return column[keyInput] as string;
  }

  if (key === 'numberScale') {
    if (column.numberScale) {
      return unitConfig?.numberScale ?? column.numberScale;
    }
    return '';
  }

  const unitType = column.unitType;
  if (unitType && unitConfig) {
    return unitConfig[unitType as keyof UnitConfig] ?? '';
  }
  // default column's unit
  return column[key] ?? '';
};

export const getUnitValue = (utrv: Pick<UniversalTrackerValuePlain, 'valueData' | 'value'>) => {
  return utrv?.valueData?.input?.value || utrv?.value;
};

type UtrNumberScaleCheck = Pick<UniversalTrackerPlain, 'numberScaleInput' | 'numberScale'> & { _id?: string };

export const getNumberScale = (
  utrv: Pick<UniversalTrackerValuePlain, 'valueData' | 'numberScale' | 'status'> & { _id?: string },
  utrObj: UniversalTracker | UtrNumberScaleCheck,
  unitConfig?: UnitConfig,
  defaultScale?: string
) => {
  const utr: UtrNumberScaleCheck = 'universalTracker' in utrObj ? utrObj?.getRaw() : utrObj;

  const isCreatedOrNAOrNR = checkIsCreatedOrNAOrNR(utrv);

  if (utrv) {
    if (utrv.valueData) {
      const { input, isImported } = utrv.valueData;
      if (input && input.numberScale) {
        return input.numberScale;
      }
      // [GU-4066] numberScale is not available in valueData if question is answered through import sheet
      if (isImported && input?.numberScale === undefined) {
        return utrv.numberScale || utr.numberScale;
      }
      // default to 'single' if question is answered and has set overrides
      if (input && utr?.numberScaleInput) {
        if (utr.numberScale !== 'single') {
          handleRouteError(new Error(`Always expected to get "single", but got "${utr.numberScale}"`), {
            utrvId: utrv._id,
            utr: {
              _id: utr._id,
              numberScale: utr.numberScale,
            },
            defaultScale,
          });
        }
        return utr.numberScale;
      }
    }
    if (isCreatedOrNAOrNR && utr.numberScaleInput) {
      return utr.numberScaleInput;
    }
    if (utrv.numberScale) {
      return utrv.numberScale;
    }
  }

  if (utr.numberScale) {
    if (isCreatedOrNAOrNR && unitConfig?.numberScale) {
      return unitConfig.numberScale;
    }
    return utr.numberScale;
  }

  return defaultScale;
};

export function getUnitDesc(unit: string | undefined, value: any, fallback = '') {
  if (!unit) {
    return fallback;
  }

  try {
    const desc = getUnitDescription(unit);
    return Number(value) === 1 ? desc.singular : desc.plural;
  } catch (e) {
    return fallback;
  }
}

export function getConvertedValue({
  value,
  unit,
  defaultUnit,
  numberScale,
  defaultNumberScale,
  isCurrency,
}: Partial<ConversionData>) {

  // Prevent converting non-numeric values as it will fallback to zero
  if (!isNumeric(value)) {
    return value;
  }

  let convertedValue = value;
  if (numberScale && defaultNumberScale && numberScale !== defaultNumberScale) {
    convertedValue = convertValue(value, numberScale, defaultNumberScale);
  }
  if (!isCurrency && unit && defaultUnit && unit !== defaultUnit) {
    convertedValue = convertValue(convertedValue, unit, defaultUnit);
  }

  return convertedValue;
}

export function getSuffixDesc(
  {
    value,
    unit,
    defaultUnit,
    numberScale,
    defaultNumberScale,
    isCurrency,
    suffix
  }: Pick<ConversionData, 'value' | 'unit' | 'defaultUnit' | 'numberScale' | 'defaultNumberScale' | 'isCurrency' | 'suffix'>,
  isDefault = true
): string {
  let desc = '';
  const hasNumberScaleChanged = numberScale !== defaultNumberScale;
  const hasUnitChanged = !isCurrency && unit !== defaultUnit;

  if (!hasNumberScaleChanged && !hasUnitChanged) {
    return desc;
  }

  // once number scale or unit changes -> display both number scale and unit if available
  const hasNumberScaleAnswered = numberScale && defaultNumberScale;
  const hasUnitAnswered = !isCurrency && unit && defaultUnit;

  if (hasNumberScaleAnswered) {
    desc = ' ' + getUnitDesc(isDefault ? defaultNumberScale : numberScale, value);
  }
  // suffix is '%' for percentage question
  if (hasUnitAnswered || suffix) {
    desc = ' ' + getUnitDesc(isDefault ? defaultUnit : unit, value, suffix);
  }

  return desc;
}

function getConvertUnit(
  conversionData: ConversionData,
  className: string
): '' | JSX.Element {
  try {
    const desc = getSuffixDesc(conversionData);
    if (desc === '') {
      return '';
    }
    const convertedValue = getConvertedValue(conversionData);

    return (
      <React.Fragment>
        - Defaults to{' '}
        <NumberFormat
          className={`d-flex-inline strong ${className}`}
          minimumFractionDigits={0}
          value={convertedValue}
          decimalPlaces={3}
        />
        {desc}
      </React.Fragment>
    );
  } catch (e) {
    return '';
  }
}

export const getInputUnitAndNumberScaleText = (props: Pick<ConversionData, 'value' | 'unit' | 'numberScale' | 'suffix'>) => {
  const { value, unit, numberScale, suffix } = props;
  const numberScaleDesc = numberScale ? getUnitDesc(numberScale, value, suffix) : '';
  const unitDesc = getUnitDesc(unit, value, suffix);

  let unitAndNumberScaleText = '';

  if (numberScaleDesc) {
    unitAndNumberScaleText += ' ' + numberScaleDesc;
  }

  if (unitDesc) {
    unitAndNumberScaleText += ' ' + unitDesc;
  }

  return unitAndNumberScaleText;
};

export function renderNumericValue(
  conversionData: ConversionData,
  className: string = 'text-ThemeAccentMedium',
  showExtended: boolean = true
): JSX.Element {
  const { value, unit, isCurrency, utr } = conversionData;

  if (isNaN(value as number)) {
    return <span className={'badge bg-secondary'}>N/A</span>;
  }

  const unitAndNumberScaleText = getInputUnitAndNumberScaleText(conversionData);
  const prefix = isCurrency && unit ? `${unit} ` : '';

  const convertUnit = showExtended ? getConvertUnit(conversionData, className) : '';
  const unitText = (
    <>
      {unitAndNumberScaleText} {convertUnit}
    </>
  );

  const decimal = getUtrDecimal(utr);

  const formattedNumber = (
    <NumberFormat
      className={`d-flex-inline align-top ${className}`}
      minimumFractionDigits={0}
      value={value}
      decimalPlaces={decimal}
    />
  );

  const tooltipText = (
    <>
      {prefix} {formattedNumber} {unitText}
    </>
  );

  return (
    <SimpleTooltip className='renderNumericValue' text={tooltipText}>
      <span className='prefix-and-value text-truncate dont_translate'>
        <span className='prefix align-top'>{prefix}</span>
        {formattedNumber}
      </span>
      <span className='pl-1 suffix align-top text-truncate'>{unitText}</span>
    </SimpleTooltip>
  );
}

const baseConversion = (
  utrv: UniversalTrackerValue | Pick<SurveyModelMinimalUtrv, 'value' | 'valueData'> | ValueHistory,
  utr: UniversalTracker | UniversalTrackerPlain,
  { value, unit, numberScale }: Pick<ConversionData, 'value' | 'unit' | 'numberScale'>
): ConversionData => {

  const v = 'universalTrackerValue' in utrv ? utrv?.universalTrackerValue : utrv;
  const input = v.valueData?.input ?? {};
  const u = 'universalTracker' in utr ? utr?.getRaw() : utr;
  const suffix = 'getSuffix' in utr ? utr.getSuffix() : '';

  const answeredNumberScale = numberScale || input.numberScale;

  return {
    unit: unit || input.unit,
    defaultUnit: u.unit,
    numberScale: answeredNumberScale,
    // fallback to 'single' if we already answered with overridden number scale
    defaultNumberScale: answeredNumberScale && !u.numberScale ? 'single' : u.numberScale,
    value,
    utr: u,
    isCurrency: u.unitType === UnitTypes.currency,
    suffix,
  };
};

export const conversionParams = baseConversion;

export const isUtrvAssuranceComplete = (status: undefined | UtrvAssuranceStatus) => {
  if (!status) {
    return false;
  }

  return [
    UtrvAssuranceStatus.Completed,
    UtrvAssuranceStatus.CompletedOpen,
  ].includes(status)

}

export const isUtrvAssuranceCreated = (status: undefined | UtrvAssuranceStatus) => {
  return status === UtrvAssuranceStatus.Created;
};

export const isUtrvPartialAssurance = (status: undefined | UtrvAssuranceStatus) => status === UtrvAssuranceStatus.Partial;

export const isUtrvAssuranceRejected = (status: undefined | UtrvAssuranceStatus) => {
  return status === UtrvAssuranceStatus.Rejected;
}

export const isUtrvAssuranceRestated = (status: undefined | UtrvAssuranceStatus) => {
  return status === UtrvAssuranceStatus.Restated;
}

export const isCompletedOpenOrRestated = (status: undefined | UtrvAssuranceStatus) => {
  return status && [UtrvAssuranceStatus.CompletedOpen, UtrvAssuranceStatus.Restated].includes(status);
};

export const getStatusIcon = (utrv: Pick<SurveyModelMinimalUtrv, 'assuranceStatus' | 'status'>) => {
  const { assuranceStatus, status } = utrv;
  if (isUtrvAssuranceComplete(assuranceStatus) || isUtrvAssuranceRejected(assuranceStatus)) {
    return 'fa-award';
  }
  if (assuranceStatus === UtrvAssuranceStatus.Restated) {
    return 'fa-rotate';
  }
  return getIconByStatus(status);
}

export const getIconByStatus = (status: UtrvStatus | string) => {
  switch (status) {
    case UtrvStatus.Created:
      return 'fa-user-clock text-ThemeHeadingLight';
    case UtrvStatus.Updated:
      return 'fa-user-edit text-ThemeAccentMedium';
    case UtrvStatus.Rejected:
      return 'fa-user-times text-ThemeDangerMedium';
    case UtrvStatus.Verified:
      return 'fa-user-check text-ThemeSuccessMedium';
    default:
      return 'fa-user';
  }
}

const restatedText = <>Restatement of previously assured data.<br />More info in the {QUESTION.SINGULAR}'s provenance section</>;

export const getAssuranceStatusText = (utrv: Pick<SurveyModelMinimalUtrv, 'assuranceStatus'>) => {
  if (isUtrvAssuranceComplete(utrv.assuranceStatus)) {
    return 'Assured';
  }

  if (isUtrvAssuranceRejected(utrv.assuranceStatus)) {
    return 'Assurance disputed';
  }

  if (isUtrvPartialAssurance(utrv.assuranceStatus)) {
    return 'Partially assured';
  }
}

export const getStatusText = (utrv: Pick<SurveyModelMinimalUtrv, 'status' | 'assuranceStatus' | 'lastUpdated'>) => {
  const assuranceStatusText = getAssuranceStatusText(utrv);

  if (assuranceStatusText) {
    return assuranceStatusText;
  }

  const lastUpdated = formatDate(utrv.lastUpdated, DATE.LONG);

  switch (utrv.status) {
    case UtrvStatus.Created:
      return 'To be completed';
    case UtrvStatus.Updated:
      if (utrv.assuranceStatus === UtrvAssuranceStatus.Restated) {
        return <>Updated on {lastUpdated}<br />{restatedText}</>;
      }
      return 'Waiting verification';
    case UtrvStatus.Rejected:
      if (utrv.assuranceStatus === UtrvAssuranceStatus.Restated) {
        return <>Rejected on {lastUpdated}<br />{restatedText}</>;
      }
      return 'Rejected, waiting resubmission';
    case UtrvStatus.Verified:
      if (utrv.assuranceStatus === UtrvAssuranceStatus.Restated) {
        return <>Verified on {lastUpdated}<br />{restatedText}</>;
      }
      return 'Verified on ' + lastUpdated;
    default:
      return 'Unknown';
  }
}
