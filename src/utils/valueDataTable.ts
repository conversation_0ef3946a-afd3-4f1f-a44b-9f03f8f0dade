import { getConvertedValue, getTableInputData } from './universalTrackerValue';
import { RowStatus, ValueData } from '../components/survey/question/questionInterfaces';
import { InputColumn, TableColumn } from '@components/survey/form/input/table/InputInterface';
import { UnitTypes } from '@utils/units';

export type ColumnMap = Map<string, TableColumn>;

interface ConversionParams {
  value?: any;
  unit?: string;
  numberScale?: string;
}

export const tableDataToView = (valueData: ValueData | undefined) => {
  const table = getTableInputData({ valueData })

  const tableData = Array.isArray(table) ? table : [];

  return {
    rows: tableData.map((row, i) => ({
      rowStatus: RowStatus.original,
      isRemoved: false,
      id: i,
      data: [...row],
    })),
    editRowId: -1,
  }
}

export const createConversionParams = (column: TableColumn, { value, unit, numberScale }: ConversionParams) => {
  return {
    unit,
    numberScale,
    value,
    defaultUnit: column.unit,
    defaultNumberScale: column.numberScale,
    isCurrency: column.unitType === UnitTypes.currency,
  };
};

export const convertInputData = (rowData: InputColumn[][], mapCode: ColumnMap): InputColumn[][] => {
  return rowData.map((row) =>
    row.map((col) => {
      const colMetadata = mapCode.get(col.code);
      if (colMetadata) {
        const params = createConversionParams(colMetadata, col);
        return {
          ...col,
          numberScale: params.defaultNumberScale,
          unit: params.isCurrency ? col.unit : params.defaultUnit,
          value: getConvertedValue(params),
        };
      }
      return col;
    }),
  );
};
